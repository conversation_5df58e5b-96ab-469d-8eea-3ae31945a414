name: ijrochi
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.0.8+14

environment:
  sdk: ">=3.4.1 <=4.0.0"

dependencies:
  flutter:
    sdk: flutter

  # Core UI
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.17
  auto_size_text: ^3.0.0
  flutter_screenutil: ^5.9.3
  google_fonts: ^6.2.1

  # Network & Connectivity
  internet_connection_checker: ^1.0.0+1
  connectivity_plus: ^6.1.3
  http: ^1.1.0
  dio: ^5.8.0+1
  socket_io_client: ^3.0.2
  http_parser: ^4.1.2

  # State Management & Architecture
  get_it: ^8.0.3
  flutter_bloc: ^9.0.0
  bloc_concurrency: ^0.3.0
  injectable: ^2.5.0
  equatable: ^2.0.7
  dartz: ^0.10.1

  # Storage & Database
  shared_preferences: ^2.5.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1
  get_storage: ^2.1.1
  flutter_session_manager: ^1.0.3
  path_provider: ^2.1.5

  # UI Components & Widgets
  mask_text_input_formatter: ^2.9.0
  pin_code_fields: ^8.0.1
  carousel_slider: ^5.0.0
  badges: ^3.1.2
  fading_edge_scrollview: ^4.1.1
  dropdown_search: ^6.0.2
  animated_loading_border: ^0.0.2
  zoom_tap_animation: ^1.1.0
  flutter_gradients_reborn: ^1.0.0+7
  infinite_scroll_pagination: ^4.0.0
  flutter_html: ^3.0.0-alpha.3
  shimmer_animation: ^2.2.2
  dotted_border: ^2.1.0
  liquid_pull_to_refresh: ^3.0.1
  grouped_list: ^6.0.0
  pinput: ^5.0.1
  fancy_indicator: ^0.1.4
  flutter_slidable: ^4.0.0
  flutter_awesome_select: ^6.5.0
  mno_zoom_widget: ^0.1.0
  cr_calendar: ^1.2.1
  flutter_animate: ^4.5.2
  flutter_mask_view: ^1.0.2
  flutter_uvc_camera: ^0.0.4
  image_editor: ^1.6.0

  # Media Handling
  image_picker: ^1.1.2
  cached_network_image: ^3.2.1
  video_player: ^2.9.3
  flutter_image_compress: ^2.4.0
  mime: ^2.0.0
  google_mlkit_face_detection: ^0.12.0

  # Audio
  audioplayers: ^6.2.0
  flutter_ringtone_player: ^4.0.0+4

  # File Management
  file_picker: ^9.0.1
  permission_handler: ^10.4.5
  external_path: ^2.0.1
  open_filex: ^4.6.0
  share_plus: ^10.1.4

  # Authentication & Security
  local_auth: ^2.3.0
  jwt_decoder: ^2.0.1
  otp_autofill: ^4.1.0

  # Location Services
  geolocator: ^12.0.0

  # Device Info & Platform
  platform_device_id_plus: ^1.0.6
  device_info_plus: ^11.3.0
  package_info_plus: ^8.3.0
  native_device_orientation: ^2.0.3

  # Notifications
  awesome_notifications: ^0.10.1
  flutter_local_notifications: ^18.0.1

  # Firebase
  firebase_crashlytics: ^4.3.3
  firebase_messaging: ^15.2.3

  # Localization & Time
  easy_localization: ^3.0.7+1
  intl: ^0.20.2
  flutter_timezone: ^4.1.0

  # Desktop Support
  flutter_acrylic: ^1.1.4
  window_manager: ^0.4.3
  bitsdojo_window: ^0.1.6
  local_notifier: ^0.1.6
  system_tray: ^2.0.3
  launch_at_startup: ^0.3.1
  desktop_window: ^0.4.2

  # Utilities
  camera: ^0.11.1
  uuid: ^3.0.7
  nanoid: ^1.0.0
  context_holder: ^0.0.5
  url_launcher: ^6.3.1
  url_launcher_ios: ^6.3.2
  circular_countdown_timer: ^0.2.4
  in_app_update: ^4.2.3
  upgrader: ^11.3.1
  flutter_background_service: ^5.1.0
  flutter_background_service_android: ^6.3.0
  flutter_background_service_ios: ^5.0.3
#  flutter_background_service: ^5.0.1
#  flutter_background_service_android: ^6.0.1
#  flutter_background_service_ios: ^5.0.3
  restart_app: ^1.3.2
  auto_start_flutter: 0.1.3
  flutter_phoenix: ^1.1.1
  theme_mode_handler: ^3.0.0
  group_radio_button: ^1.3.0
  pdf: ^3.11.3
  html: ^0.15.4
  uzpay: ^0.0.2+2

  action_slider: ^0.7.0
  page_transition: ^2.2.1
  slide_action: ^0.0.2
  ntp: ^2.0.0
  time_change_detector: ^0.0.4
  animated_custom_dropdown: ^3.1.1
  get: ^4.6.5
  alarm: ^4.1.1
  firebase_core: ^3.12.1

  #  awesome_notifications_fcm: any
  #  board_datetime_picker: ^1.0.2
  #  one_context: ^2.1.0
  #  go_router: 12.0.1
  #  video_compress: ^3.1.2
  #  video_editor: ^1.4.3
  #  android_sms_retriever: ^1.3.3
  #  alice: ^0.3.2
  #  social_media_recorder: ^0.2.0
  #  kplayer: ^0.2.4
  #  build_context_provider: ^1.1.0
  #  flutter_inappwebview: ^6.0.0
  #  animated_custom_dropdown: 1.2.2

  # GitHub Dependencies
  window_size:
    git:
      url: https://github.com/google/flutter-desktop-embedding.git
      path: plugins/window_size

dependency_overrides:
  intl: ^0.18.1

dev_dependencies:
#  flutter_test:
#    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.4.3
  build_runner: ^2.4.13
  hive_generator: ^2.0.1
  rename: ^3.0.2
  change_app_package_name: ^1.5.0
  flutter_launcher_icons: ^0.14.3
  easy_localization_loader: ^2.0.2
  isar_generator: ^3.1.0+1

flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/icons/
    - assets/images/
    - assets/translations/
    - assets/fonts/

#dart run flutter_launcher_icons
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: assets/images/logo.png
  image_path_ios: assets/images/logo.png
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: assets/images/logo.png
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
  windows:
    generate: true
    image_path: assets/images/logo.png
    icon_size: 48 # min:48, max:256, default: 48

  #  dart run flutter_native_splash:create
flutter_native_splash:
  image: assets/app_icon.png
  color: "#FFFFFF"
  android: true
  ios: true
  android_12:
    color: "#008ABF"
    image: assets/images/logoAPI31.png
    icon_background_color: "#FFFFFF"

fonts:
  - family: Inter
    fonts:
      - asset: assets/fonts/Inter-Bold.ttf
      - asset: assets/fonts/Inter-Medium.ttf
      - asset: assets/fonts/Inter-SemiBold.ttf
      - asset: assets/fonts/Inter-Regular.ttf

  - family: Montserrat
    fonts:
      - asset: assets/fonts/OpenSans-Regular.ttf

# Build runner
# dart run build_runner build --delete-conflicting-outputs

# Language generate
#     flutter pub run easy_localization:generate -S "assets/translations" -O "lib/translations"
#     flutter pub run easy_localization:generate -S "assets/translations" -O "lib/translations" -o "locale_keys.g.dart" -f  keys