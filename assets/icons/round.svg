<svg fill="none" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_ii_47_341)">
        <circle cx="24" cy="24" fill="white" fill-opacity="0.2" r="24" />
    </g>
    <defs>
        <filter color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="53" id="filter0_ii_47_341" width="48"
            x="0" y="-2">
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" mode="normal" result="shape" />
            <feColorMatrix in="SourceAlpha" result="hardAlpha"
                type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
            <feOffset dy="3" />
            <feGaussianBlur stdDeviation="2" />
            <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic" />
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.18 0" />
            <feBlend in2="shape" mode="normal" result="effect1_innerShadow_47_341" />
            <feColorMatrix in="SourceAlpha" result="hardAlpha"
                type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
            <feOffset dy="-2" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic" />
            <feColorMatrix type="matrix"
                values="0 0 0 0 0.0379302 0 0 0 0 0.0331771 0 0 0 0 0.270833 0 0 0 0.18 0" />
            <feBlend in2="effect1_innerShadow_47_341" mode="normal"
                result="effect2_innerShadow_47_341" />
        </filter>
    </defs>
</svg>
