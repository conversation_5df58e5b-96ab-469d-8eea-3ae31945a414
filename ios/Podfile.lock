PODS:
  - alarm (0.0.1):
    - Flutter
  - audioplayers_darwin (0.0.1):
    - Flutter
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - external_path (2.0.1):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - Firebase/Crashlytics (11.8.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.8.0)
  - Firebase/Messaging (11.8.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.8.0)
  - firebase_core (3.12.1):
    - Firebase/CoreOnly (= 11.8.0)
    - Flutter
  - firebase_crashlytics (4.3.3):
    - Firebase/Crashlytics (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.3):
    - Firebase/Messaging (= 11.8.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.8.1):
    - FirebaseCoreInternal (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseCoreInternal (11.8.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.9.0)
  - FirebaseSessions (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_background_service_ios (0.0.3):
    - Flutter
  - flutter_fgbg (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_ringtone_player (0.0.1):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - google_mlkit_commons (0.9.0):
    - Flutter
    - MLKitVision
  - google_mlkit_face_detection (0.12.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/FaceDetection (~> 7.0.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/FaceDetection (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitFaceDetection (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - image_editor_common (1.0.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - IosAwnCore (0.10.0)
  - isar_flutter_libs (1.0.0):
    - Flutter
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MLImage (1.0.0-beta6)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitFaceDetection (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - native_device_orientation (0.0.1):
    - Flutter
  - open_filex (0.0.2):
    - Flutter
  - OrderedSet (6.0.3)
  - otp_autofill (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - platform_device_id_plus (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - restart_app (0.0.1):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - time_change_detector (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - alarm (from `.symlinks/plugins/alarm/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - external_path (from `.symlinks/plugins/external_path/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_background_service_ios (from `.symlinks/plugins/flutter_background_service_ios/ios`)
  - flutter_fgbg (from `.symlinks/plugins/flutter_fgbg/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_ringtone_player (from `.symlinks/plugins/flutter_ringtone_player/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_face_detection (from `.symlinks/plugins/google_mlkit_face_detection/ios`)
  - image_editor_common (from `.symlinks/plugins/image_editor_common/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - isar_flutter_libs (from `.symlinks/plugins/isar_flutter_libs/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - native_device_orientation (from `.symlinks/plugins/native_device_orientation/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - otp_autofill (from `.symlinks/plugins/otp_autofill/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - platform_device_id_plus (from `.symlinks/plugins/platform_device_id_plus/ios`)
  - restart_app (from `.symlinks/plugins/restart_app/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - time_change_detector (from `.symlinks/plugins/time_change_detector/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - IosAwnCore
    - libwebp
    - Mantle
    - MLImage
    - MLKitCommon
    - MLKitFaceDetection
    - MLKitVision
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif

EXTERNAL SOURCES:
  alarm:
    :path: ".symlinks/plugins/alarm/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  external_path:
    :path: ".symlinks/plugins/external_path/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_background_service_ios:
    :path: ".symlinks/plugins/flutter_background_service_ios/ios"
  flutter_fgbg:
    :path: ".symlinks/plugins/flutter_fgbg/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_ringtone_player:
    :path: ".symlinks/plugins/flutter_ringtone_player/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_face_detection:
    :path: ".symlinks/plugins/google_mlkit_face_detection/ios"
  image_editor_common:
    :path: ".symlinks/plugins/image_editor_common/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  isar_flutter_libs:
    :path: ".symlinks/plugins/isar_flutter_libs/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  native_device_orientation:
    :path: ".symlinks/plugins/native_device_orientation/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  otp_autofill:
    :path: ".symlinks/plugins/otp_autofill/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  platform_device_id_plus:
    :path: ".symlinks/plugins/platform_device_id_plus/ios"
  restart_app:
    :path: ".symlinks/plugins/restart_app/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  time_change_detector:
    :path: ".symlinks/plugins/time_change_detector/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"

SPEC CHECKSUMS:
  alarm: 839eba06f468218db1b9f1736e32322db958e857
  audioplayers_darwin: ccf9c770ee768abb07e26d90af093f7bab1c12ab
  awesome_notifications: 0f432b28098d193920b11a44cfa9d2d9313a3888
  camera_avfoundation: 04b44aeb14070126c6529e5ab82cc7c9fca107cf
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  external_path: fd37c654b69a1336e33a2403f48383ae6100e443
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Firebase: d80354ed7f6df5f9aca55e9eb47cc4b634735eaf
  firebase_core: 8d552814f6c01ccde5d88939fced4ec26f2f5510
  firebase_crashlytics: eb5eb0ef5e6910395adfe177b9ca4a62e8a2f1aa
  firebase_messaging: 5e0adf2eb18b0ee59aa0c109314c091a0497ecac
  FirebaseCore: 99fe0c4b44a39f37d99e6404e02009d2db5d718d
  FirebaseCoreExtension: 3d3f2017a00d06e09ab4ebe065391b0bb642565e
  FirebaseCoreInternal: df24ce5af28864660ecbd13596fc8dd3a8c34629
  FirebaseCrashlytics: a1102c035f18d5dd94a5969ee439c526d0c9e313
  FirebaseInstallations: 6c963bd2a86aca0481eef4f48f5a4df783ae5917
  FirebaseMessaging: 487b634ccdf6f7b7ff180fdcb2a9935490f764e8
  FirebaseRemoteConfigInterop: 710954a00e956c5fe5144a8e46164f0361389203
  FirebaseSessions: c4d40a97f88f9eaff2834d61b4fea0a522d62123
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_background_service_ios: 00d31bdff7b4bfe06d32375df358abe0329cf87e
  flutter_fgbg: d3da78df78454b1808f0829a5da9cd17dfe16444
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_local_notifications: 395056b3175ba4f08480a7c5de30cd36d69827e4
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_ringtone_player: a77c42464250845611eaa44c27e8714acc800138
  flutter_timezone: 7c838e17ffd4645d261e87037e5bebf6d38fe544
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  geolocator_apple: 1560c3c875af2a412242c7a923e15d0d401966ff
  google_mlkit_commons: 92c769cc2e0a2bfdeb3c38091a36e8a9cc8c63d4
  google_mlkit_face_detection: 76f493a9ffcb1aeb0ad4abd13ea58403e092ab84
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_editor_common: 3de87e7c4804f4ae24c8f8a998362b98c105cac1
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  isar_flutter_libs: 9fc2cfb928c539e1b76c481ba5d143d556d94920
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  local_auth_darwin: 553ce4f9b16d3fdfeafce9cf042e7c9f77c1c391
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitFaceDetection: 2a593db4837db503ad3426b565e7aab045cefea5
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  native_device_orientation: e3580675687d5034770da198f6839ebf2122ef94
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  otp_autofill: a50d5ea8173b9a6fd930a4f1252416793a4e1906
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 3787117e48f80715ff04a3830ca039283d6a4f29
  platform_device_id_plus: ead043ea948d38cec3e8d2b9aa17cfd0825e2d23
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  restart_app: 9cda5378aacc5000e3f66ee76a9201534e7d3ecf
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  time_change_detector: fd99f9e7f8c6de624bca8f19ffff56c782eca13e
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b

PODFILE CHECKSUM: 5d955ee1004ffc5887a48764882bb98daa07c98f

COCOAPODS: 1.16.2
