// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class  LocaleKeys {
  static const website_name = 'website_name';
  static const lang_uz = 'lang_uz';
  static const lang_ru = 'lang_ru';
  static const enter_phone_number = 'enter_phone_number';
  static const phone_number = 'phone_number';
  static const send = 'send';
  static const help = 'help';
  static const connect_us = 'connect_us';
  static const verification_code = 'verification_code';
  static const input_verification_code = 'input_verification_code';
  static const no_code_send = 'no_code_send';
  static const insatall_pin_code = 'insatall_pin_code';
  static const re_enter_pin_code = 're_enter_pin_code';
  static const entering_pin_code = 'entering_pin_code';
  static const no_user = 'no_user';
  static const no_internet = 'no_internet';
  static const data_load_error = 'data_load_error';
  static const no_phone_number = 'no_phone_number';
  static const pin_code_no_same = 'pin_code_no_same';
  static const new_ = 'new_';
  static const active = 'active';
  static const notifications = 'notifications';
  static const late = 'late';
  static const progress = 'progress';
  static const route = 'route';
  static const connect = 'connect';
  static const dayRegime = 'dayRegime';
  static const exit_system = 'exit_system';
  static const telephone = 'telephone';
  static const app_language = 'app_language';
  static const change_pin_code = 'change_pin_code';
  static const enter_fingerprint = 'enter_fingerprint';
  static const retry_communication = 'retry_communication';
  static const app_version = 'app_version';
  static const select_language = 'select_language';
  static const stop_alert = 'stop_alert';
  static const stop_this_session_alert = 'stop_this_session_alert';
  static const current_pin_code_wring = 'current_pin_code_wring';
  static const pin_codes_mismatch = 'pin_codes_mismatch';
  static const need_to_fill = 'need_to_fill';
  static const going_to_change_pin_code = 'going_to_change_pin_code';
  static const all = 'all';
  static const january = 'january';
  static const february = 'february';
  static const march = 'march';
  static const april = 'april';
  static const may = 'may';
  static const june = 'june';
  static const july = 'july';
  static const august = 'august';
  static const september = 'september';
  static const october = 'october';
  static const november = 'november';
  static const december = 'december';
  static const task_stopped = 'task_stopped';
  static const feedback_telegram = 'feedback_telegram';
  static const gallery = 'gallery';
  static const camera = 'camera';
  static const choose = 'choose';
  static const cancel = 'cancel';
  static const title_progress = 'title_progress';
  static const yes = 'yes';
  static const action_update = 'action_update';
  static const last_version = 'last_version';
  static const error_title = 'error_title';
  static const retry_title = 'retry_title';
  static const success_title = 'success_title';
  static const logout_text = 'logout_text';
  static const attention = 'attention';
  static const turn_on = 'turn_on';
  static const turn_on_gps_my_location_dialog_message = 'turn_on_gps_my_location_dialog_message';
  static const input_login_please = 'input_login_please';
  static const input_verification_please = 'input_verification_please';
  static const input_password_please = 'input_password_please';
  static const cyrillic_error = 'cyrillic_error';
  static const choose_language = 'choose_language';
  static const pin_error = 'pin_error';
  static const pin_code_is_required = 'pin_code_is_required';
  static const new_password_must_entered = 'new_password_must_entered';
  static const confirm_password_must_entered = 'confirm_password_must_entered';
  static const field_is_required = 'field_is_required';
  static const confirm_pin_error = 'confirm_pin_error';
  static const pin_desc = 'pin_desc';
  static const enter_pin_code_error = 'enter_pin_code_error';
  static const current_pin_error = 'current_pin_error';
  static const change_passcode = 'change_passcode';
  static const are_you_sure_change = 'are_you_sure_change';
  static const current_password_error = 'current_password_error';
  static const passwords_do_not_match = 'passwords_do_not_match';
  static const warning = 'warning';
  static const search = 'search';
  static const current_lang = 'current_lang';
  static const current_lang_2 = 'current_lang_2';
  static const welcome = 'welcome';
  static const welcome_2 = 'welcome_2';
  static const login_desc = 'login_desc';
  static const login = 'login';
  static const password = 'password';
  static const sign_in = 'sign_in';
  static const pin_code = 'pin_code';
  static const pin_code_desc = 'pin_code_desc';
  static const confirm_pin_code = 'confirm_pin_code';
  static const email = 'email';
  static const phone = 'phone';
  static const login_phone = 'login_phone';
  static const settings_language = 'settings_language';
  static const settings_pin = 'settings_pin';
  static const settings_fingerprint = 'settings_fingerprint';
  static const settings_version = 'settings_version';
  static const settings_password = 'settings_password';
  static const settings_logout = 'settings_logout';
  static const loading = 'loading';
  static const loading_desc = 'loading_desc';
  static const error = 'error';
  static const success = 'success';
  static const error_desc = 'error_desc';
  static const current_pin_code = 'current_pin_code';
  static const new_pin_code = 'new_pin_code';
  static const current_password = 'current_password';
  static const new_password = 'new_password';
  static const confirm_password = 'confirm_password';
  static const change_password = 'change_password';
  static const are_you_sure_change_password = 'are_you_sure_change_password';
  static const format_is_incorect = 'format_is_incorect';
  static const info_not_found = 'info_not_found';
  static const login_sign_up_desc = 'login_sign_up_desc';
  static const login_sign_up_admin = 'login_sign_up_admin';
  static const verification_title_up = 'verification_title_up';
  static const verification_title_bottom = 'verification_title_bottom';
  static const verification_desc = 'verification_desc';
  static const code = 'code';
  static const resend_code = 'resend_code';
  static const input_login_incorrect_format = 'input_login_incorrect_format';
  static const input_verification_incorrect_format = 'input_verification_incorrect_format';
  static const archive_notifications = 'archive_notifications';
  static const archive = 'archive';
  static const task_new = 'task_new';
  static const task_process = 'task_process';
  static const task_archive = 'task_archive';
  static const settings = 'settings';
  static const contacts = 'contacts';
  static const welcomeTo = 'welcomeTo';
  static const not_info_current_time = 'not_info_current_time';
  static const error_message_title = 'error_message_title';
  static const error_message_desc = 'error_message_desc';
  static const notification_number = 'notification_number';
  static const notification_time = 'notification_time';
  static const notification_date = 'notification_date';
  static const notification_status = 'notification_status';
  static const department = 'department';
  static const application = 'application';
  static const notification_accept = 'notification_accept';
  static const share = 'share';
  static const index = 'index';
  static const term = 'term';
  static const time_left = 'time_left';
  static const task_complete_late = 'task_complete_late';
  static const task_complete_early = 'task_complete_early';
  static const task_number = 'task_number';
  static const task = 'task';
  static const organization = 'organization';
  static const fio = 'fio';
  static const position = 'position';
  static const own_phone = 'own_phone';
  static const work_phone = 'work_phone';
  static const settings_feedback = 'settings_feedback';
  static const filter_all = 'filter_all';
  static const task_time = 'task_time';
  static const count_of_new_notifications = 'count_of_new_notifications';
  static const error_activity_for_share_not_found = 'error_activity_for_share_not_found';
  static const error_activity_for_download_not_found = 'error_activity_for_download_not_found';
  static const error_activity_for_phone_not_found = 'error_activity_for_phone_not_found';
  static const error_activity_for_sms_not_found = 'error_activity_for_sms_not_found';
  static const error_activity_for_telegram_not_found = 'error_activity_for_telegram_not_found';
  static const error_activity_for_email_not_found = 'error_activity_for_email_not_found';
  static const feedback_email = 'feedback_email';
  static const feedback_phone = 'feedback_phone';
  static const day2 = 'day2';
  static const day = 'day';
  static const task_infinity = 'task_infinity';
  static const term_undefined = 'term_undefined';
  static const biometric_subtitle = 'biometric_subtitle';
  static const biometric_title = 'biometric_title';
  static const error_authentication_failed = 'error_authentication_failed';
  static const error_biometric_unavailable = 'error_biometric_unavailable';
  static const error_biometric_no_hardware = 'error_biometric_no_hardware';
  static const count = 'count';
  static const notifications_archive_desc = 'notifications_archive_desc';
  static const notifications_new_desc = 'notifications_new_desc';
  static const send_organisation = 'send_organisation';
  static const error_activity_for_open_not_found = 'error_activity_for_open_not_found';
  static const error_sharing_data = 'error_sharing_data';
  static const error_first_load_appendix = 'error_first_load_appendix';
  static const error_on_load_file = 'error_on_load_file';
  static const permission_denied = 'permission_denied';
  static const notification_process = 'notification_process';
  static const tasks_dashboard = 'tasks_dashboard';
  static const instruction = 'instruction';
  static const main = 'main';
  static const support = 'support';
  static const theme = 'theme';
  static const app_for_sharing_not_found = 'app_for_sharing_not_found';
  static const current_session = 'current_session';
  static const stop_other_sessions = 'stop_other_sessions';
  static const active_session = 'active_session';
  static const are_you_sure_stop_all_session = 'are_you_sure_stop_all_session';
  static const stop_session = 'stop_session';
  static const are_you_sure_stop_this_session = 'are_you_sure_stop_this_session';
  static const notifications_active = 'notifications_active';
  static const add_reminder = 'add_reminder';
  static const reminder = 'reminder';
  static const save = 'save';
  static const reminder_date_in_past = 'reminder_date_in_past';
  static const comments = 'comments';
  static const delete = 'delete';
  static const task_push_title = 'task_push_title';
  static const notification_push_title = 'notification_push_title';
  static const reminder_push_title = 'reminder_push_title';
  static const director_fio = 'director_fio';
  static const faks = 'faks';
  static const exat = 'exat';
  static const change_name = 'change_name';
  static const edit = 'edit';
  static const reminder_accept = 'reminder_accept';
  static const update_reminder = 'update_reminder';
  static const open = 'open';
  static const push_accept = 'push_accept';
  static const push_open = 'push_open';
  static const date = 'date';
  static const filter = 'filter';
  static const cancel_filter = 'cancel_filter';
  static const add_filter = 'add_filter';
  static const moderator_fio = 'moderator_fio';
  static const not_chosen = 'not_chosen';
  static const unknown = 'unknown';
  static const write_comment = 'write_comment';
  static const cancel_alarm = 'cancel_alarm';
  static const alarm_added = 'alarm_added';
  static const refresh_desc = 'refresh_desc';
  static const no_data_fount = 'no_data_fount';
  static const empty_list = 'empty_list';
  static const error_in_load = 'error_in_load';
  static const reload = 'reload';
  static const change_time = 'change_time';
  static const understandable = 'understandable';
  static const server_error = 'server_error';
  static const share_address = 'share_address';
  static const want_to_exit = 'want_to_exit';
  static const alert_delete_info = 'alert_delete_info';
  static const select_to_continue = 'select_to_continue';
  static const no = 'no';
  static const favourites = 'favourites';
  static const open_app = 'open_app';
  static const invalid_code = 'invalid_code';
  static const no_actual_code = 'no_actual_code';
  static const no_deadline = 'no_deadline';
  static const tasks = 'tasks';
  static const selectLongPeriod = 'selectLongPeriod';
  static const notification = 'notification';
  static const applying = 'applying';
  static const autoWallDesc = 'autoWallDesc';
  static const openSettings = 'openSettings';
  static const warning1 = 'warning1';
  static const errorDownloading = 'errorDownloading';
  static const understand = 'understand';
  static const upGraderBody = 'upGraderBody';
  static const buttonTitleIgnore = 'buttonTitleIgnore';
  static const buttonTitleLater = 'buttonTitleLater';
  static const buttonTitleUpdate = 'buttonTitleUpdate';
  static const prompt = 'prompt';
  static const releaseNotes = 'releaseNotes';
  static const title = 'title';
  static const unknown_error = 'unknown_error';
  static const takeCamera = 'takeCamera';
  static const uploadFile = 'uploadFile';
  static const select = 'select';
  static const givePermission = 'givePermission';
  static const notification_settings = 'notification_settings';
  static const notification_permissions = 'notification_permissions';
  static const give_notification_permissions = 'give_notification_permissions';
  static const deny = 'deny';
  static const allow = 'allow';
  static const refresh = 'refresh';
  static const check_internet_connection = 'check_internet_connection';
  static const make_payment = 'make_payment';
  static const tariff = 'tariff';
  static const monthly = 'monthly';
  static const sum = 'sum';
  static const start_time = 'start_time';
  static const finished_time = 'finished_time';
  static const payments = 'payments';
  static const next_payment_sum = 'next_payment_sum';
  static const payment_history = 'payment_history';
  static const som = 'som';
  static const to_month = 'to_month';
  static const year = 'year';
  static const payment_system = 'payment_system';
  static const add_payment_in_progress = 'add_payment_in_progress';
  static const outer_browser = 'outer_browser';
  static const inner_browser = 'inner_browser';
  static const choose_browser = 'choose_browser';
  static const payment_impossible = 'payment_impossible';
  static const payment_with_click = 'payment_with_click';
  static const payment_with_payme = 'payment_with_payme';
  static const payment = 'payment';
  static const fast = 'fast';
  static const simple = 'simple';
  static const important = 'important';
  static const very_important = 'very_important';
  static const today = 'today';
  static const three_days = 'three_days';
  static const week = 'week';
  static const error_open_file = 'error_open_file';
  static const uploading = 'uploading';
  static const done = 'done';
  static const not_done = 'not_done';
  static const enter_name = 'enter_name';
  static const name = 'name';
  static const surname = 'surname';
  static const retry = 'retry';
  static const change = 'change';
  static const is_correct_number = 'is_correct_number';

}
