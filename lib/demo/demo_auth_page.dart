import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/core/widgets/language_dialog.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/lock/presentation/pages/lock_page.dart';
import 'package:ijrochi/features/login/presentation/bloc/login_bloc.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../core/utils/app_constants.dart';
import '../features/auth/presentation/bloc/auth_bloc.dart';
import '../generated/assets.dart';
import '../translations/locale_keys.g.dart';

class DemoAuthPage extends StatefulWidget {
  final String tel;

  const DemoAuthPage({super.key, required this.tel});

  static Widget screen(String tel) => MultiBlocProvider(
          providers: [
            BlocProvider<AuthBloc>(create: (context) => di<AuthBloc>()),
            BlocProvider<LoginBloc>(create: (context) => di<LoginBloc>())
          ],
          child: DemoAuthPage(
            tel: tel,
          ));

  @override
  State<DemoAuthPage> createState() => _DemoAuthPageState();
}

class _DemoAuthPageState extends State<DemoAuthPage> {
  final SharedPreferences sharedPreferences = di();
  String _verticalGroupValue = "O\'zbekcha";
  final focusNode = FocusNode();
  var maskFormatter = MaskTextInputFormatter(mask: '######');
  int numberCount = 0;
  String deviceName = 'unknown';
  String deviceVersion = 'unknown';

  getSelectedLanguage() async {
    String lang = await sharedPreferences.getString(language_pref) ?? 'uz';
    switch (lang) {
      case 'uz':
        {
          _verticalGroupValue = 'O\'zbekcha';
          break;
        }
      case 'ru':
        {
          _verticalGroupValue = 'Русский';
          break;
        }
      case 'cr':
        {
          _verticalGroupValue = 'Ўзбекча';
          break;
        }
      default:
        {
          _verticalGroupValue = 'O\'zbekcha';
        }
    }
  }

  @override
  void initState() {
    getTokenFromLocal();
    getFirebaseTokenLocal();

    ///Device names
    getDeviceName().then((value) => deviceName = value);
    getDeviceVersion().then((value) => deviceVersion = value);
    getSelectedLanguage();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: cWhiteColor,
      appBar: AppBar(
        elevation: 0,
        iconTheme: IconThemeData(
          color: cFirstColor, //change your color here
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              _verticalGroupValue,
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            SizedBox(
              width: 5.w,
            ),
            InkWell(
              onTap: () {
                showDialog(
                    context: context,
                    builder: (BuildContext context1) {
                      return LanguageDialog.screen(onSelectCallBack: () {
                        setState(() {
                          getSelectedLanguage();
                        });
                      });
                    });
              },
              child: SvgPicture.asset(
                Assets.iconsIcLanguage,
                width: 24.w,
                height: 24.w,
                color: cFirstColor,
              ),
            )
          ],
        ),
      ),
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthSuccessName) {
            Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => LockPage()),
                (route) => false);
          } else if (state is AuthFailure) {
            WidgetsBinding.instance.addPostFrameCallback((time) {
              Snack(state.message, context, cRedColor);
            });
          }
        },
        builder: (context, state) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 40.h,
                ),
                Text(
                  LocaleKeys.input_verification_code.tr(),
                  style: TextStyle(
                      color: cFirstColor,
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  height: 20.h,
                ),
                Container(
                  height: 50.h,
                  child: TextField(
                    focusNode: focusNode,
                    decoration: InputDecoration(
                      enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(width: 2, color: cFirstColor)),
                      border: OutlineInputBorder(
                          borderSide: BorderSide(width: 2, color: cFirstColor)),
                      focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(width: 2, color: cFirstColor)),
                      disabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(width: 2, color: cFirstColor)),
                      label: Text(LocaleKeys.code.tr()),
                      prefixStyle: TextStyle(
                          color: cFirstColor,
                          fontSize: 16.sp,
                          fontFamily: medium),
                    ),
                    style: TextStyle(fontSize: 16.sp, fontFamily: medium),
                    inputFormatters: [maskFormatter],
                    keyboardType: TextInputType.phone,
                    onChanged: (value) {
                      setState(() {
                        numberCount = value.length;
                      });
                    },
                  ),
                ),
                SizedBox(
                  height: 4.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      "${numberCount}/6",
                      style: TextStyle(
                          color: cFirstColor,
                          fontSize: 14.sp,
                          fontFamily: medium),
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                MaterialButton(
                  onPressed: numberCount == 6
                      ? () {
                          BlocProvider.of<AuthBloc>(context).add(SendAuthEvent(
                              phone: widget.tel,
                              verifyCode: maskFormatter.getUnmaskedText()));
                        }
                      : null,
                  disabledColor: cFirstColor.withAlpha(100),
                  child: _button(state),
                  color: cFirstColor,
                  minWidth: MediaQuery.of(context).size.width,
                  height: 48.h,
                )
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _button(state) {
    if (state is AuthLoading) {
      return const CupertinoActivityIndicator();
    } else {
      return Text(
        LocaleKeys.send.tr(),
        style: TextStyle(
            fontSize: 14.sp,
            fontFamily: Assets.fontsNunitoSansRegular,
            color: cWhiteColor),
      );
    }
  }
}
