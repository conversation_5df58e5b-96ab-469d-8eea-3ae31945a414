import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:get_it/get_it.dart';
import 'package:get_storage/get_storage.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/network/app_interceptor.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/auth/data/datasources/auth_local_datasources.dart';
import 'package:ijrochi/features/auth/data/datasources/auth_remote_datasources.dart';
import 'package:ijrochi/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:ijrochi/features/auth/presentation/name_bloc/input_name_bloc.dart';
import 'package:ijrochi/features/main/data/datasources/main_local_datasources.dart';
import 'package:ijrochi/features/main/data/datasources/main_remote_datasources.dart';
import 'package:ijrochi/features/main/data/model/content_count.dart';
import 'package:ijrochi/features/main/data/repositories/main_repository_impl.dart';
import 'package:ijrochi/features/main/domain/reposirories/main_repository.dart';
import 'package:ijrochi/features/main/domain/usescases/content_count_from_local.dart';
import 'package:ijrochi/features/main/domain/usescases/main_usecases.dart';
import 'package:ijrochi/features/main/presentation/bloc/main_bloc.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/alarm_cubit/alarm_cubit.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/notification_detail_bloc/notification_detail_bloc.dart';
import 'package:ijrochi/features/notifications/notification/datasources/notification_local_datasource.dart';
import 'package:ijrochi/features/notifications/notification/datasources/notification_remote_datasource.dart';
import 'package:ijrochi/features/notifications/notification/presentation/bloc/notification_bloc.dart';
import 'package:ijrochi/features/notifications/notification_count/datasources/notification_count_local_datasource.dart';
import 'package:ijrochi/features/notifications/notification_count/datasources/notification_count_remote_datasource.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/notification_bloc/notification_count_bloc.dart';
import 'package:ijrochi/features/settings/data/datasources/settings_local_datasources.dart';
import 'package:ijrochi/features/settings/data/datasources/settings_remote_datasources.dart';
import 'package:ijrochi/features/settings/presentation/bloc/settings_bloc.dart';
import 'package:ijrochi/features/task_detail/bloc/task_detail_bloc.dart';
import 'package:ijrochi/features/tasks/task/datasources/task_local_datasource.dart';
import 'package:ijrochi/features/tasks/task/datasources/task_remote_datasource.dart';
import 'package:ijrochi/features/tasks/task/presentation/bloc/task_bloc.dart';
import 'package:ijrochi/features/tasks/task_count/datasource/task_count_local_datasource.dart';
import 'package:ijrochi/features/tasks/task_count/datasource/task_count_remote_datasource.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/task_bloc/task_count_bloc.dart';
import 'package:ijrochi/push_notifications/notification_service.dart';

import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:socket_io_client/socket_io_client.dart';
import 'package:upgrader/upgrader.dart';

import '../core/network/network_info.dart';
import '../features/auth/data/repositories/auth_repository_impl.dart';
import '../features/auth/domain/repositories/auth_repository.dart';
import '../features/auth/domain/usescases/auth.dart';
import '../features/login/data/datasources/login_local_datasources.dart';
import '../features/login/data/datasources/login_remote_datasources.dart';
import '../features/login/data/repositories/login_repository_impl.dart';
import '../features/login/domain/repository/login_repository.dart';
import '../features/login/domain/usecases/u_login.dart';
import '../features/login/presentation/bloc/login_bloc.dart';
import '../features/notification_detail/presentation/bloc/download_chat_file_cubit/download_chat_file_bloc.dart';
import '../upgrader.dart';

final di = GetIt.instance;

Future<void> init() async {
  print('=========== Dependency injection initializing.... ===========');

  /// Local cache

  final SharedPreferences prefs = await SharedPreferences.getInstance();
  di.registerFactory(() => prefs);
  di.registerFactory(() => SessionManager());

  await GetStorage.init();
  di.registerLazySingleton(() => GetStorage());

  final IsarService isar = await IsarService.buildIsarService();
  di.registerLazySingleton(() => isar);

  ///Versioning
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  APP_VERSION = packageInfo.version;

  final Dio dio = Dio(BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: Duration(seconds: 60),
    receiveTimeout: Duration(seconds: 60),
  ));
  dio.interceptors.add(AppInterceptor(getStorage: di()));

  // if (Platform.isAndroid) {
  //   DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  //   AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
  //   di.registerLazySingleton(() => androidInfo);
  // }

  //  di.registerLazySingleton<Socket>(() => io(
  //     socketUrl,
  //     OptionBuilder()
  //         .setAuth({"token": getStorage.read(BEARER_TOKEN)}).setTransports(
  //             ['websocket']).build()));

  /// Network
  di.registerLazySingleton<http.Client>(() => http.Client());
  di.registerLazySingleton<Dio>(() => dio);

  GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  di.registerLazySingleton(() => navigatorKey);

  var notificationToken;
  var listener =
      InternetConnectionChecker().onStatusChange.listen((status) async {
    switch (status) {
      case InternetConnectionStatus.connected:
        print('Data connection is available.');
        notificationToken = await createChannel(di());
        if (notificationToken.isNotEmpty) {
          prefs.setString(firebaseTokenKEY, notificationToken);
          print('Firebase token is set: ' + notificationToken);
        } else {
          print('Firebase token is EMPTY');
        }
        break;
      case InternetConnectionStatus.disconnected:
        print('You are disconnected from the internet.');
        break;
      }
  });

  ///BLOCK
  //login
  di.registerFactory(
    () => LoginBloc(
      dio: di(),
      networkInfo: di(),
    ),
  );

  ///auth
  di.registerFactory(
    () => AuthBloc(dio: di(), networkInfo: di(), getStorage: di()),
  );

  ///main
  di.registerFactory(
      () => MainBloc(mainUsesCases: di(), contentCountUsesCases: di()));

  ///Repositories
  /// login
  di.registerLazySingleton<LoginRepository>(
    () => LoginRepositoryImpl(
        networkInfo: di(),
        loginRemoteDatasource: di(),
        loginLocalDatasource: di()),
  );

  /// auth
  di.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(
      networkInfo: di(),
      authLocalDataSources: di(),
      authRemoteDataSource: di()));

  ///main
  di.registerLazySingleton<MainRepository>(() => MainRepositoryImpl(
      mainRemoteDataSourcesImpl: di(),
      mainLocalDataSourcesImpl: di(),
      networkInfo: di(),
      isarService: di()));

  ///Use Cases

  ///login
  di.registerLazySingleton(() => LoginData(loginRepository: di()));

  ///auth
  di.registerLazySingleton(() => AuthData(authRepository: di()));

  ///main
  di.registerLazySingleton(() => MainUsesCases(mainRepository: di()));
  di.registerLazySingleton(() => ContentCountUsesCases(mainRepository: di()));

  ///Data sources
  ///login
  di.registerLazySingleton(() => LoginRemoteDatasourceImpl(client: di()));
  di.registerLazySingleton(
    () => LoginLocalDataSourceImpl(sharedPreferences: di()),
  );

  ///auth
  di.registerLazySingleton(() => AuthRemoteDataSourceImpl(client: di()));
  di.registerLazySingleton(
      () => AuthLocalDataSourcesImpl(sharedPreferences: di()));

  ///main
  di.registerLazySingleton(
      () => MainRemoteDataSourcesImpl(dio: di(), getStorage: di()));
  di.registerLazySingleton(() => MainLocalDataSourcesImpl());

  /// Network Info
  di.registerLazySingleton(() => InternetConnectionChecker());
  di.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(di()));

  ///network monitor
  // di.registerLazySingleton<Alice>(() => Alice(
  //     showNotification: true,
  //     showInspectorOnShake: true,
  //     darkTheme: false,
  //     maxCallsCount: 1000));

  ///Android version

  if (Platform.isAndroid) {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
    di.registerLazySingleton(() => androidInfo);
  }

  ///Audio Player
  AudioPlayer audioPlayer = AudioPlayer();
  di.registerLazySingleton(() => audioPlayer);

  /// Local datasource
  var dir = await getApplicationDocumentsDirectory();
  await Hive
    ..init(dir.path + "/Ijrochi");

  //for active session
  await Hive.openBox(sessionBox);
  //for main Screen  //for main Screen
  Hive.registerAdapter(ContentCountAdapter());
  await Hive.openBox(contentCountBox);

  ///NotificationCount

  di.registerLazySingleton<NotificationCountRemoteDatasource>(
      () => NotificationCountRemoteDatasourceImpl(dio: di()));
  di.registerLazySingleton<NotificationCountLocalDatasource>(
      () => NotificationCountLocalDatasourceImpl(isarService: di()));

  di.registerFactory(() => NotificationCountBloc(
      notificationLocalDatasource: di(),
      notificationRemoteDatasource: di(),
      networkInfo: di()));

  ///Notification

  di.registerLazySingleton<NotificationRemoteDatasource>(
      () => NotificationRemoteDatasourceImpl(dio: di()));

  di.registerLazySingleton<NotificationLocalDatasource>(
      () => NotificationLocalDatasourceImpl(isarService: di()));

  di.registerFactory(() => NotificationBloc(
      networkInfo: di(),
      notificationRemoteDatasource: di(),
      notificationLocalDatasource: di()));

  ///NotificationDetail
  di.registerFactory(
      () => NotificationDetailBloc(networkInfo: di(), dio: di()));

  ///TaskCount

  di.registerLazySingleton<TaskCountRemoteDataSource>(
      () => TaskCountRemoteDataSourceImpl(dio: di()));
  di.registerLazySingleton<TaskCountLocalDataSource>(
      () => TaskCountLocalDataSourceImpl(isarService: di()));

  di.registerFactory(() => TaskCountBloc(
      taskCountLocalDataSource: di(),
      taskCountRemoteDataSource: di(),
      networkInfo: di()));

  ///Task
  di.registerLazySingleton<TaskRemoteDatasource>(
      () => TaskRemoteDatasourceImpl(dio: di()));

  di.registerLazySingleton<TaskLocalDataSource>(
      () => TaskLocalDataSourceImpl(isarService: di()));

  di.registerFactory(
    () => TaskBloc(
        networkInfo: di(),
        taskRemoteDatasource: di(),
        taskLocalDataSource: di()),
  );

  ///TaskDetail
  di.registerFactory(() => TaskDetailBloc(networkInfo: di(), dio: di()));

  ///InputName
  di.registerFactory(() => InputNameBloc(networkInfo: di(), dio: di()));

  ///AlarmCubit
  di.registerFactory(() => AlarmCubit(isarService: di()));

  ///setting
  di.registerLazySingleton<SettingsRemoteDataSources>(
      () => SettingsRemoteDataSourcesImpl(dio: di()));
  di.registerLazySingleton<SettingsLocalDataSources>(
      () => SettingsLocalDataSourcesImpl(isarService: di()));

  di.registerFactory(() => SettingsBloc(
      settingsLocalDataSources: di(),
      settingsRemoteDataSources: di(),
      networkInfo: di(),
      sharedPreferences: di(),
      dio: di()));

  di.registerFactory(() => DownloadChatFileBloc(networkInfo: di(), dio: di()));

  /// Force Upgrader
  final upgrader = Upgrader(
    messages: MyCustomMessages(),
    durationUntilAlertAgain: const Duration(seconds: 1),
    debugDisplayAlways: true,
    // minAppVersion: APP_VERSION,
    upgraderDevice: UpgraderDevice(),
    storeController: UpgraderStoreController(
      onAndroid: () => UpgraderPlayStore(),
      oniOS: () => UpgraderAppStore(),
    ),
  );

  await upgrader.initialize();

  di.registerLazySingleton<Upgrader>(() => upgrader);
}
