import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:desktop_window/desktop_window.dart';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_acrylic/flutter_acrylic.dart';
import 'package:ijrochi/translations/codegen_loader.g.dart';
import 'package:launch_at_startup/launch_at_startup.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:upgrader/upgrader.dart';
import 'package:window_manager/window_manager.dart';
import 'package:window_size/window_size.dart';
import 'back_service.dart';
import 'core/functions/functions.dart';
import 'core/network/network_provider.dart';
import 'di/dependency_injection.dart' as di;
import 'features/app.dart';
import 'package:easy_localization/easy_localization.dart';

import 'firebase_options.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';

import 'push_notifications/awesome_notification_controller.dart';

void main() async {
  runZonedGuarded(() async {
    ///Don't move these stupid
    WidgetsFlutterBinding.ensureInitialized();
    await di.init();
    await EasyLocalization.ensureInitialized();

    HttpOverrides.global = MyHttpOverrides();
    await Upgrader.clearSavedSettings();

    if (Platform.isWindows | Platform.isMacOS | Platform.isLinux) {
      await windowManager.ensureInitialized();
      Window.initialize();

      var screenSize = await getCurrentScreen();
      var height = screenSize?.frame.height ?? 600;
      var width = screenSize?.frame.width ?? 1000;

      windowManager.setTitle("\"Ижрочи\" - www.ijrochi.uz");
      setWindowMaxSize(Size(width / 4.4, height / 1.2));
      setWindowMinSize(Size(width / 4.4, height / 1.2));
      await DesktopWindow.setWindowSize(Size(width / 4.4, height / 1.2));
      windowManager.setFullScreen(false);
      windowManager.setMaximizable(false);
      windowManager.setAsFrameless();
      windowManager.setAlignment(Alignment.bottomRight);
      Window.setEffect(effect: WindowEffect.acrylic);
      windowManager.waitUntilReadyToShow();

      ///Launch at start-up
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      launchAtStartup.setup(
        appName: packageInfo.appName,
        appPath: Platform.resolvedExecutable,
      );
      bool isEnabled = await launchAtStartup.isEnabled();
      if (!isEnabled) {
        print('Auto-launch is enabled!');
        await launchAtStartup.enable();
      }
      // await launchAtStartup.disable();
    }

    ///App Store may reject
    // AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
    //   if (!isAllowed) {
    //     // This is just a basic example. For real apps, you must show some
    //     // friendly dialog box before call the request method.
    //     // This is very important to not harm the user experience
    //     AwesomeNotifications()
    //         .requestPermissionToSendNotifications(permissions: [
    //       NotificationPermission.PreciseAlarms,
    //       NotificationPermission.Alert,
    //       NotificationPermission.Badge,
    //       NotificationPermission.Sound,
    //       NotificationPermission.Vibration,
    //       NotificationPermission.CriticalAlert,
    //       NotificationPermission.FullScreenIntent
    //     ]);
    //   }
    // });

    await NotificationController.initializeLocalNotifications();
    // bool set = await NotificationController.scheduleNewNotification(
    //     id: int.parse('1111'),
    //     title: 'test',
    //     body: 'body test',
    //     dateTime: DateTime.now().add(const Duration(seconds: 10)));
    await NotificationController.startListeningNotificationEvents();

    // await Alarm.init();
    // initialize Required for alarm events to bind with flutter method channel
    // FlutterAlarmBackgroundTrigger.initialize();
    // NotificationController.setAlarm(DateTime(2023, 6, 8, 19, 15));

    if (!Platform.isWindows) {
      await Firebase.initializeApp(
        name: "ijro-uz",
        options: DefaultFirebaseOptions.currentPlatform,
      );

      FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
      FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
    }

    SystemChrome.setPreferredOrientations(
      [
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ],
    ).then((val) {
      runApp(EasyLocalization(
          child: Phoenix(
            child: AppProvider(),
          ),
          supportedLocales: [
            Locale('uz', 'latin'),
            Locale('uz', 'cyrillic'),
            Locale('ru')
          ],
          fallbackLocale: Locale('uz', 'latin'),
          startLocale: Locale('uz', 'latin'),
          assetLoader: CodegenLoader(),
          path: 'assets/translations'));
    });
  }, (error, stacktrace) {
    log('runZonedGuarded Errors: $error');
    debugPrint("Ijrochi app error");
  });
}
