import 'dart:async';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:socket_io_client/socket_io_client.dart';

import '../push_notifications/notification_service.dart';
import 'core/utils/api_path.dart';
import 'core/utils/app_constants.dart';
import 'di/dependency_injection.dart';

late Socket socket;

@pragma("vm:entry-point")
void listenSocketDesktop() async {

  GetStorage gs = di();

  socket = io(
      socketUrl,
      OptionBuilder()
          .setAuth({"token": gs.read(BEARER_TOKEN)})
          .setTransports(['websocket'])
          .disableAutoConnect()
          .build());

  socket.connect();
  socket.onConnect((_) {
    print(_);
    print("Desktop socket connected");
  });

  socket.onConnectError((error) {
    print(error);
  });

  DartPluginRegistrant.ensureInitialized();

  socket.off('devices');
  socket.on('devices', (data) {
    print(data);
    showDesktopNotification(data);
  });
}
