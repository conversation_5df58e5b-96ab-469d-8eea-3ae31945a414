import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/functions/date_picker.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/features/home/<USER>/pages/home.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'background_animation.dart';
import 'notification_service.dart';

class RingingPageTask extends StatefulWidget {
  final dynamic receivedNotification;

  RingingPageTask({Key? key, required this.receivedNotification})
      : super(key: key);

  @override
  _RingingPageTaskState createState() => _RingingPageTaskState();
}

class _RingingPageTaskState extends State<RingingPageTask> {
  final GlobalKey<BackgroundAnimationState> backAnimKey = GlobalKey();
  DateTime selectedDate = DateTime.now();
  DateTime sendDate = DateTime.now();
  DateTime endDate = DateTime.now();

  int differenceCount = 0;
  int lastCount = 0;

  final DateFormat dateFormat = DateFormat('dd.MM.yyyy');
  late Color color;
  late bool isDark;

  @override
  void initState() {
    color = HexColor.fromHex(
        widget.receivedNotification.payload?["statusColor"] ?? "#000000");
    sendDate = DateTime.parse(
        widget.receivedNotification.payload?["sendDate"] ?? "1970-00-00");
    endDate = DateTime.parse(
        widget.receivedNotification.payload?["endDate"] ?? "1970-00-00");

    Duration difference = endDate.difference(sendDate);
    differenceCount = difference.inDays;

    Duration difference2 = endDate.difference(DateTime.now());
    lastCount = difference2.inDays;

    super.initState();
    // playRing();
  }

  @override
  void dispose() {
    // widget.audioPlayer?.stop();
    // widget.audioPlayer?.dispose();
    // FlutterRingtonePlayer.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      backgroundColor: cRedColor,
      appBar: AppBar(
        title: Center(
            child: Text(
          LocaleKeys.task.tr(),
          style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w400),
        )),
        automaticallyImplyLeading: false,
        elevation: 0,
      ),
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        color: cRedColor,
        child: Stack(
          alignment: Alignment.center,
          fit: StackFit.expand,
          children: [
            BackgroundAnimation(
              key: backAnimKey,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.receivedNotification.title.toString(),
                          style: TextStyle(
                              fontSize: 20.sp,
                              color: isDark ? cWhiteColor : color),
                        ),
                        SizedBox(height: 20.h),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${LocaleKeys.term.tr()}:' +
                                    ' ${dateFormat.format(endDate)} ($differenceCount ' +
                                    '${LocaleKeys.day.tr()}' +
                                    ')',
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    fontSize: 15.sp, color: cGrayColor),
                              ),
                              Row(
                                children: [
                                  Icon(Icons.history, color: color),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  Container(
                                    padding: EdgeInsets.all(5.h),
                                    width: 60.w,
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                          color: color,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(10.r)),
                                    child: Text(
                                      lastCount.toString() +
                                          ' ' +
                                          '${LocaleKeys.day.tr()}',
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          fontSize: 15.sp, color: color),
                                    ),
                                  ),
                                ],
                              ),
                            ]),
                        SizedBox(height: 20.h),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${LocaleKeys.send_organisation.tr()}:',
                                style: TextStyle(
                                    fontSize: 15.sp, color: cGrayColor),
                              ),
                              Text(
                                widget.receivedNotification.payload?["user"] ??
                                    'user',
                                textAlign: TextAlign.right,
                                style: TextStyle(fontSize: 15.sp),
                              ),
                            ]),
                        SizedBox(height: 40.h),
                        Text(
                          widget.receivedNotification.body.toString(),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontSize: 20.sp, fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 10.h),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: cGrayColor.withAlpha(40)),
                          child: Html(
                            data: widget.receivedNotification
                                    .payload?["actualBody"] ??
                                "<p>Loading..</p>",
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            TextButton(
                              child: Text(
                                LocaleKeys.change_time.tr(),
                                style: TextStyle(
                                    fontSize: 15.sp, color: cGrayColor),
                              ),
                              onPressed: () async {
                                print(widget.receivedNotification.payload);
                                print(widget.receivedNotification.body.toString());
                                print(widget.receivedNotification.title.toString());

                                showDateTimePicker(context, onPressedOK: (val) {
                                  setState(() {
                                    selectedDate = val;
                                  });
                                  var data = Task(
                                    taskId: widget.receivedNotification
                                        .payload?["actualId"],
                                    title: widget.receivedNotification.body,
                                    content: widget.receivedNotification
                                        .payload?["actualBody"],
                                    startDate: widget.receivedNotification
                                        .payload?["sendDate"],
                                    endDate: widget.receivedNotification
                                        .payload?["endDate"],
                                    statusColor: widget.receivedNotification
                                        .payload?["statusColor"],
                                    moderator: EmbeddedModerator(
                                        fullName: widget.receivedNotification
                                            .payload?["user"]),
                                    serialNumber: int.parse(widget.receivedNotification.payload?["serialNumber"])
                                  );

                                  addScheduledTask(
                                      dateTime: selectedDate, data: data);

                                  CustomToast.showToast(
                                      LocaleKeys.alarm_added.tr());

                                  // FlutterRingtonePlayer.stop();

                                  Navigator.pushAndRemoveUntil(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => HomePage()),
                                      (route) => false);
                                });
                              },
                            ),
                          ],
                        ),
                        TextButton(
                          child: Text(
                            LocaleKeys.understandable.tr(),
                            style: TextStyle(fontSize: 15.sp),
                          ),
                          onPressed: () async {
                            // widget.audioPlayer?.stop();
                            // FlutterRingtonePlayer.stop();
                            backAnimKey.currentState?.stopAnimation();
                            Navigator.pushAndRemoveUntil(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => HomePage()),
                                (route) => false);
                            // exit(0); // Close the app
                          },
                        ),
                      ],
                    )
                  ]),
            ),
          ],
        ),
      ),
    );
  }
}
