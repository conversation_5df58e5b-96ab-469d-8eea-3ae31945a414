import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_gradients_reborn/flutter_gradients_reborn.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:theme_mode_handler/theme_mode_handler.dart';

import '../../../../core/utils/app_constants.dart';

class BackgroundAnimation extends StatefulWidget {
  const BackgroundAnimation({Key? key}) : super(key: key);

  @override
  State<BackgroundAnimation> createState() => BackgroundAnimationState();
}

class BackgroundAnimationState extends State<BackgroundAnimation>
    with SingleTickerProviderStateMixin {
  late final Ticker _ticker;
  double radius = 0.3;
  bool? isDark;

  void stopAnimation() {
    _ticker.stop();
    _ticker.dispose();
  }

  void startAnimation() {
    var relapse = true;
    // 3. initialize Ticker
    _ticker = this.createTicker((elapsed) {
      // 4. update state
      setState(() {
        if (radius < 0.9 && relapse) {
          {
            radius = radius + 0.01;
          }
        } else {
          radius = radius - 0.01;
          relapse = false;
          if (radius < 0.3) {
            relapse = true;
          }
        }
      });
    });
    // 5. start ticker
    _ticker.start();
  }

  @override
  void initState() {
    startAnimation();
    super.initState();
  }

  @override
  void dispose() {
    stopAnimation();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isDark == null) {
      isDark = Theme.of(context).brightness == Brightness.dark;
      ThemeModeHandler.of(context)!
          .saveThemeMode(isDark! ? ThemeMode.dark : ThemeMode.light);
    }
    return Column(children: [
      Expanded(
        child: ColorFiltered(
          colorFilter: ColorFilter.mode(
              isDark!
                  ? cBlackColor.withOpacity(0.8)
                  : cWhiteColor.withOpacity(0.6),
              BlendMode.srcOver),
          child: Container(
            decoration: BoxDecoration(
              gradient: FlutterGradients.heavyRain(
                type: GradientType.radial,
                center: Alignment.center,
                radius: radius,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Align(
                    alignment: Alignment(0, 0),
                    child: Icon(
                      Icons.alarm,
                      size: 120.h,
                      color: isDark! ? cWhiteColor : cTextColor.withAlpha(50),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ]);
  }
}
