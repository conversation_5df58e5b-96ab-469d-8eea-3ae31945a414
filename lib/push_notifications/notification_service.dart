// flutter local notification

import 'dart:convert';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:context_holder/context_holder.dart';
import 'package:dio/dio.dart';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/database/embeded_models.dart' as n;
import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/app.dart';
import 'package:ijrochi/features/notification_detail/presentation/page/notification_detailed_page.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart'
    as n;
import 'package:ijrochi/features/task_detail/page/task_detail_page.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';

import 'package:ijrochi/push_notifications/schedule_timer.dart';
import 'package:isar/isar.dart';
import 'package:local_notifier/local_notifier.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:system_tray/system_tray.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'awesome_notification_controller.dart';

const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    description: 'This channel is used for important notifications.',
    // description
    importance: Importance.max,
    playSound: true,
    enableVibration: true);

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

///Listen message from FCM (Background message)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();

  Map<String, dynamic> data = message.data;

  print('A Background message just showed up :  ${message.messageId}');
  print(data);

  SharedPreferences prefs = await SharedPreferences.getInstance();
  bool isSocket = await (prefs.getString(SWITCH_PUSH) ?? '') == SOCKET_SWITCH;
  if (!isSocket) {
    showNotification(data);
  }
}

///Method for creating a notification channel (+Foreground message listener)
Future<String> createChannel(NetworkInfo networkInfo) async {
  await Firebase.initializeApp();
  SharedPreferences prefs = await SharedPreferences.getInstance();

  String? token = '';

  ///iOS section for notification (Asking permission)
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  ///Ask permission with Custom Dialog
  // NotificationSettings settings = await messaging.requestPermission(
  //   alert: true,
  //   announcement: true,
  //   badge: true,
  //   carPlay: false,
  //   criticalAlert: true,
  //   provisional: false,
  //   sound: true,
  // );

  // print('User granted permission: ${settings.authorizationStatus}');

  messaging.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  ///-------------------ends iOS specific section

  ///----------------- Sets TOKEN (Important)
  try {
    token = await FirebaseMessaging.instance.getToken();
    if ((token != null && token.isEmpty) || token == null) {
      token = prefs.getString(firebaseTokenKEY) ?? token;
    }
  } catch (e) {
    print(e.toString());
  }

  ///-------------------------------------------

  ///Configure time zone
  _configureLocalTimeZone();

  /// Create channel for Android
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  ///Initializing local plugin settings for both platforms
  await flutterLocalNotificationsPlugin.initialize(
    InitializationSettings(
        android: AndroidInitializationSettings(
          "@mipmap/ic_launcher",
        ),
        iOS: DarwinInitializationSettings(
          defaultPresentSound: true,
        )),
    onDidReceiveNotificationResponse:
        (NotificationResponse notificationResponse) async {
      print('justTappedThis');

      Map<String, dynamic> data =
          jsonDecode(notificationResponse.payload ?? "{}");
      String id = data['id'];
      String type = data['type'];

      if (notificationResponse.actionId == null) {
        ///DEFAULT
        open(id, type, OPEN);
      } else if (notificationResponse.actionId == 'id_1') {
        ///ACCEPT if not accepted
        open(id, type, ACCEPT);
      } else if (notificationResponse.actionId == 'id_2') {
        ///OPEN with button if not accepted
        open(id, type, OPEN);
      }
      print("Foreground action: ${notificationResponse.actionId}");
    },
  );

  /// Handle background message
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  ///Notification tap from Firebase automatic notification which triggers 'notification' key
  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
    print('A new message open app event was published');
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;
    print("------------------------>${message}");

    if (notification != null && android != null) {
      ///
      print("Tapped firebase controlled message:"
          "\n=> To show this 'notification' key must be written to the payload");

      ///JUST PUSH
    }
  });

  ///When token is changed, callback triggers and function saves new token to the prefs
  FirebaseMessaging.instance.onTokenRefresh.listen((token) {
    print('Token refreshed: $token');
    // Do something with the refreshed token
    prefs.setString(firebaseTokenKEY, token);
  });

  ///Listen message from FCM (Foreground message)
  FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
    Map<String, dynamic> data = message.data;

    print('A Foreground message just showed up :  ${message.messageId}');
    print(data);

    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isSocket = await (prefs.getString(SWITCH_PUSH) ?? '') == SOCKET_SWITCH;
    if (!isSocket) {
      showNotification(data);
    }
  });

  return Future<String>.value(token);
}

///Method for showing the "Desktop" local notification

showDesktopNotification(dynamic data) async {

  delivered(data['_id'], data['type']);

  final AppWindow _appWindow = AppWindow();

  LocalNotification notification = LocalNotification(
    title: await titleMaker(data['serialNumber'].toString(), data['type']),
    body: data['body'],
  );
  notification.onShow = () {
    print('onShow ${notification.identifier}');
  };
  notification.onClose = (closeReason) {
    // Only supported on windows, other platforms closeReason is always unknown.
    switch (closeReason) {
      case LocalNotificationCloseReason.userCanceled:
        // do something
        break;
      case LocalNotificationCloseReason.timedOut:
        // do something
        break;
      default:
    }
    print('onClose ${notification.identifier} - $closeReason');
  };
  notification.onClick = () {
    print('onClick ${notification.identifier}');
    _appWindow.show();
    open(data['_id'], data['type'], OPEN);
  };
  notification.onClickAction = (actionIndex) {
    print('onClickAction ${notification.identifier} - $actionIndex');
  };
  notification.show();
}

///Method for showing the local notification
showNotification(data) async {
  if (data != null) {
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // String lang = prefs.getString(language_pref) ?? 'uz';

    // flutterLocalNotificationsPlugin.show(
    //   data.hashCode,
    //   titleMaker(data['id'].toString(), data['type']),
    //   data['title'],
    //   NotificationDetails(
    //       android: AndroidNotificationDetails(
    //         channel.id,
    //         channel.name,
    //         channelDescription: channel.description,
    //         color: Colors.blue,
    //         icon: "@mipmap/ic_launcher",
    //         enableVibration: true,
    //         actions: <AndroidNotificationAction>[
    //           AndroidNotificationAction('id_1', lang=="uz"?"Qabul qilish":"Принятие",
    //               showsUserInterface: true),
    //           AndroidNotificationAction('id_2', lang=="uz"?"Ochish":"Открыть",
    //               showsUserInterface: true),
    //           // AndroidNotificationAction('id_3', 'Action 3',
    //           //     showsUserInterface: true),
    //         ],
    //       ),
    //       iOS: DarwinNotificationDetails()),
    //   payload: "{\"id\": \"${data['id']}\", \"type\": \"${data['type']}\"}",);
    NotificationController.createNewNotification(data);
  } else {
    CustomToast.showToast('=== Notification display error! ===');
  }

  try {
    delivered(data["_id"], data["type"]);
  } catch (e) {
    print("=== Delivered error: $e");
  }

  // TaskModel? task = await getData(data["id"], data["type"]);
  // addScheduledTask(DateTime.now().add(const Duration(seconds: 5)), task!);

  if (data['type'] == TASK) {
    try {
      TaskModel? task = await getData(data["_id"], data["type"]);

      // NotificationScheduler.scheduleTaskForEveryDay(
      //     DateTime(2023, 7, 15), task ?? TaskModel());

      // NotificationScheduler.scheduleTaskLastThreeDays(
      //     DateTime(2023, 7, 15), task ?? TaskModel());

      NotificationScheduler.scheduleTaskForMock(
          DateTime(2023, 7, 15), task ?? TaskModel());

      print("Scheduled massage successfully set!");
    } catch (e) {
      print("Scheduled message creation failed: $e");
    }
  }
}

/// NOTIFICATION METHODS

Future<void> addScheduledNotification(
    {required DateTime dateTime, required n.Notification data}) async {
  // Opening Isar if it's null
  var isar = await getIsarForSchedule();

  try {
    await NotificationController.initializeLocalNotifications();

    int scheduledNotificationId = 0;

    var id = data.notificationId ?? '';
    List<n.NotificationDocs> items = await isar.notificationDocs
        .filter()
        .notificationIdEqualTo(id)
        .findAll();

    var item = items.first.copyWith(scheduledTime: dateTime);

    await isar.writeTxn(() async {
      scheduledNotificationId = await isar.notificationDocs.put(item);
    });
    print("scheduledNotificationId------------->${scheduledNotificationId}");

    await NotificationController.scheduleNewNotification(
        id: scheduledNotificationId,
        actualId: id,
        title: await titleMaker(data.serialNumber.toString(), NOTIFICATION),
        body: data.title.toString(),
        actualBody: data.content.toString(),
        statusName: data.statusName ?? StatusName(),
        statusColor: data.statusColor ?? "#000000",
        user: data.moderator?.fullName ?? "user",
        dateTime: dateTime);

    print("Scheduled massage successfully set to the $dateTime");
  } catch (e) {
    print("Scheduled message creation failed: $e");
  }
}

deleteScheduledNotificationById({required String actualId}) async {
  try {
    // Opening Isar if it's null
    var isar = await getIsarForSchedule();

    List<n.NotificationDocs>? items;
    await isar.writeTxn(() async {
      items = await isar.notificationDocs
          .filter()
          .scheduledTimeIsNotNull()
          .notificationIdEqualTo(actualId)
          .findAll();
    });
    var item = items?.first;
    var duplicateItemIsarId = item?.id ?? 0;

    //delete from base by id
    await isar.writeTxn(() async {
      await isar.notificationDocs
          .filter()
          .scheduledTimeIsNotNull()
          .idEqualTo(duplicateItemIsarId)
          .deleteAll();
    });
    print('Delete: schedule deleted from ID: $actualId ');
  } catch (e) {
    print(e);
  }
}

Future<void> cancelAndDeleteScheduledNotificationById(
    {required String actualId}) async {
  try {
    // Opening Isar if it's null
    var isar = await getIsarForSchedule();
    //delete from base by id

    List<n.NotificationDocs>? items;
    await isar.writeTxn(() async {
      items = await isar.notificationDocs
          .filter()
          .scheduledTimeIsNotNull()
          .notificationIdEqualTo(actualId)
          .findAll();
    });
    var item = items?.first;
    var duplicateItemIsarId = item?.id ?? 0;

    await isar.writeTxn(() async {
      await isar.notificationDocs
          .filter()
          .scheduledTimeIsNotNull()
          .idEqualTo(duplicateItemIsarId)
          .deleteAll();
    });

    await AwesomeNotifications().cancel(duplicateItemIsarId);
    print('Cancel and delete: schedule deleted from ID: $duplicateItemIsarId ');
  } catch (e) {
    print(e);
  }
}

cancelAllScheduledNotifications() async {
  // Opening Isar if it's null
  var isar = await getIsarForSchedule();

  await AwesomeNotifications().cancelAll();
  //delete all from base
  await isar.writeTxn(() async {
    await isar.notificationDocs.filter().scheduledTimeIsNotNull().deleteAll();
    print('All schedules are cleared!');
  });
}

/// TASK METHODS

Future<void> addScheduledTask(
    {required DateTime dateTime, required Task data}) async {
  // Opening Isar if it's null
  var isar = await getIsarForSchedule();

  int scheduledTaskId = 0;

  print("Task Id:${data.taskId ?? ''}");
  print("Id:${data.id ?? ''}");
  print("User:${data.moderator?.fullName ?? ''}");

  var id = data.taskId ?? '';
  List<TaskDocs> items =
      await isar.taskDocs.filter().taskIdEqualTo(id).findAll();

  var item = items.first.copyWith(scheduledTime: dateTime);

  await isar.writeTxn(() async {
    scheduledTaskId = await isar.taskDocs.put(item);
  });

  try {
    await NotificationController.initializeLocalNotifications();
    await NotificationController.scheduleNewTask(
        id: scheduledTaskId,
        actualId: id,
        title: await titleMaker(data.serialNumber.toString(), TASK),
        body: data.title.toString(),
        actualBody: data.content.toString(),
        sendDate: data.startDate ?? DateTime.now().toString(),
        endDate: data.endDate ?? DateTime.now().toString(),
        statusColor: data.statusColor ?? "#000000",
        user: data.moderator?.fullName ?? "user",
        dateTime: dateTime,
        serialNumber: data.serialNumber.toString() ?? "");

    print("Scheduled massage successfully set to the $dateTime");
  } catch (e) {
    print("Scheduled message creation failed: $e");
  }
}

// addRandomizedScheduledTask(DateTime dateTime, Task data) async {
//   // Opening Isar if it's null
//   var isar = await getIsarForSchedule();
//   // var uniqueIdString = await customAlphabet('0123456789', 10);
//   // var uniqueId = int.parse(uniqueIdString);
//
//   int scheduledTaskId = 0;
//
//   var id = data.taskId ?? '';
//   List<TaskDocs> items =
//       await isar.taskDocs.filter().taskIdEqualTo(id).findAll();
//
//   var item = items.first.copyWith(scheduledTime: dateTime);
//
//   await isar.writeTxn(() async {
//     scheduledTaskId = await isar.taskDocs.put(item);
//   });
//
//   try {
//     await NotificationController.initializeLocalNotifications();
//     await NotificationController.scheduleNewTask(
//         id: scheduledTaskId,
//         actualId: id,
//         title: await titleMaker(data.serialNumber.toString(), TASK),
//         body: data.title.toString(),
//         actualBody: data.content.toString(),
//         sendDate: data.startDate ?? DateTime.now().toString(),
//         endDate: data.endDate ?? DateTime.now().toString(),
//         statusColor: data.statusColor ?? "#000000",
//         user: data.moderator?.fullName ?? "user",
//         dateTime: dateTime);
//
//     print("Scheduled massage successfully set to the $dateTime");
//   } catch (e) {
//     print("Scheduled message creation failed: $e");
//   }
// }

deleteScheduledTaskById({required String actualId}) async {
  try {
    // Opening Isar if it's null
    var isar = await getIsarForSchedule();

    List<TaskDocs>? items;
    await isar.writeTxn(() async {
      items = await isar.taskDocs
          .filter()
          .scheduledTimeIsNotNull()
          .taskIdEqualTo(actualId)
          .findAll();
    });
    var item = items?.first;
    var duplicateItemIsarId = item?.id ?? 0;

    //delete from base by id
    await isar.writeTxn(() async {
      await isar.taskDocs
          .filter()
          .scheduledTimeIsNotNull()
          .idEqualTo(duplicateItemIsarId)
          .deleteAll();
    });
    print('Delete: schedule deleted from ID: $duplicateItemIsarId');
  } catch (e) {
    print(e);
  }
}

Future<void> cancelAndDeleteScheduledTaskById(
    {required String actualId}) async {
  print("cancelAndDeleteScheduledTaskById:$actualId");
  try {
    // Opening Isar if it's null
    var isar = await getIsarForSchedule();

    List<TaskDocs>? items;
    await isar.writeTxn(() async {
      items = await isar.taskDocs
          .filter()
          .scheduledTimeIsNotNull()
          .taskIdEqualTo(actualId)
          .findAll();
    });
    var item = items?.first;
    var duplicateItemIsarId = item?.id ?? 0;

    //delete from base by id
    await isar.writeTxn(() async {
      await isar.taskDocs
          .filter()
          .scheduledTimeIsNotNull()
          .idEqualTo(duplicateItemIsarId)
          .deleteAll();
    });

    await AwesomeNotifications().cancel(duplicateItemIsarId);
    print('Cancel and delete: schedule deleted from ID: $duplicateItemIsarId ');
  } catch (e) {
    print(e);
  }
}

cancelAllScheduledTasks() async {
  // Opening Isar if it's null
  var isar = await getIsarForSchedule();

  await AwesomeNotifications().cancelAll();
  //delete all from base
  await isar.writeTxn(() async {
    await isar.notificationDocs.filter().scheduledTimeIsNotNull().deleteAll();
    print('All schedules are cleared!');
    // CustomToast.showToast('Барча хабарномалар тозаланди!');
  });
}

///TODO: Test
cancelRandomizedScheduledTasksById({required String actualId}) async {
  // Opening Isar if it's null
  var isar = await getIsarForSchedule();

  List<TaskDocs>? items;
  await isar.writeTxn(() async {
    items = await isar.taskDocs
        .filter()
        .scheduledTimeIsNotNull()
        .taskIdEqualTo(actualId)
        .findAll();
  });
  var item = items?.first;
  var duplicateItemIsarId = item?.id ?? 0;

  List<TaskDocs> randomizes = await isar.taskDocs
      .filter()
      .scheduledTimeIsNotNull()
      .idEqualTo(duplicateItemIsarId)
      .findAll();

  for (TaskDocs r in randomizes) {
    await AwesomeNotifications().cancel(r.id);
  }

  //delete all from base
  await isar.writeTxn(() async {
    isar.taskDocs
        .filter()
        .scheduledTimeIsNotNull()
        .idEqualTo(duplicateItemIsarId)
        .deleteAll();
    print('All schedules are cleared!');
    // CustomToast.showToast('Барча хабарномалар тозаланди!');
  });
}

///TODO: Test
cancelAllRandomizedScheduledTasks() async {
  // Opening Isar if it's null
  var isar = await getIsarForSchedule();

  List<TaskDocs> randomizes =
      await isar.taskDocs.filter().scheduledTimeIsNotNull().findAll();

  for (TaskDocs r in randomizes) {
    await AwesomeNotifications().cancel(r.id);
  }

  //delete all from base
  await isar.writeTxn(() async {
    isar.taskDocs.filter().scheduledTimeIsNotNull().deleteAll();
    print('All schedules are cleared!');
    // CustomToast.showToast('Барча хабарномалар тозаланди!');
  });
}

/// Action function when notification clicked

void open(String id, String type, String ACTION) async {
  ///TODO: Test here (pushReplacement changed to push)
  var isAccepted = await isAcceptedFunc(id, type);

  if (type == NOTIFICATION) {
    n.NotificationDocs? notification = await getData(id, type);
    if (notification != null) {
      if (!isAccepted) {
        if (shouldLockStatic) {
          print("REPLACE TO PREVENT BACK STACK NAVIGATION WHEN LOCAL AUTH");
          Navigator.pushReplacement(
              ContextHolder.currentContext,
              MaterialPageRoute(
                  builder: (context) => NotificationDetailedPage.screen(
                      shouldLock: shouldLockStatic,
                      notificationDocs: notification,
                      pushKey: ACTION)));
        } else {
          print("NO REPLACE");
          Navigator.push(
              ContextHolder.currentContext,
              MaterialPageRoute(
                  builder: (context) => NotificationDetailedPage.screen(
                      shouldLock: shouldLockStatic,
                      notificationDocs: notification,
                      pushKey: ACTION)));
        }
      } else {
        if (shouldLockStatic) {
          Navigator.pushReplacement(
              ContextHolder.currentContext,
              MaterialPageRoute(
                  builder: (context) => NotificationDetailedPage.screen(
                      shouldLock: shouldLockStatic,
                      notificationDocs: notification)));
        } else {
          Navigator.push(
              ContextHolder.currentContext,
              MaterialPageRoute(
                  builder: (context) => NotificationDetailedPage.screen(
                      shouldLock: shouldLockStatic,
                      notificationDocs: notification)));
        }
      }
    }
  } else {
    TaskDocs? task = await getData(id, type);
    if (task != null) {
      if (!isAccepted) {
        if (shouldLockStatic) {
          Navigator.pushReplacement(
              ContextHolder.currentContext,
              MaterialPageRoute(
                  builder: (context) => TaskDetailPage.screen(
                      taskDocs: task,
                      pushKey: ACTION,
                      shouldLock: shouldLockStatic)));
        } else {
          Navigator.push(
              ContextHolder.currentContext,
              MaterialPageRoute(
                  builder: (context) => TaskDetailPage.screen(
                      taskDocs: task,
                      pushKey: ACTION,
                      shouldLock: shouldLockStatic)));
        }
      } else {
        if (shouldLockStatic) {
          Navigator.pushReplacement(
              ContextHolder.currentContext,
              MaterialPageRoute(
                  builder: (context) => TaskDetailPage.screen(
                      taskDocs: task, shouldLock: shouldLockStatic)));
        } else {
          Navigator.push(
              ContextHolder.currentContext,
              MaterialPageRoute(
                  builder: (context) => TaskDetailPage.screen(
                      taskDocs: task, shouldLock: shouldLockStatic)));
        }
      }
    }
  }
}

void delivered(dynamic id, String type) async {
  await GetStorage.init();
  GetStorage gs = GetStorage();
  final Dio dio = Dio();

  try {
    var token = await gs.read(BEARER_TOKEN);
    print("TOKEN:$token");
    final response = await dio.get(
        baseUrl +
            (type == TASK ? deliveredTaskPath : deliveredNotificationPath) +
            id,
        options: Options(
          headers: <String, String>{
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + '$token'
          },
        ));

    if (response.statusCode == 200) {
      final parsed = response.data;
      print("Delivered Success: $parsed");
    } else {
      print("Delivered Error: $response.data");
    }
  } catch (e) {
    print(e);
  }
}

Future<bool> isAcceptedFunc(String id, String type) async {
  final Dio dio = Dio();
  final GetStorage gs = GetStorage();
  final String token = gs.read(BEARER_TOKEN);
  try {
    final response = await dio.get(
      baseUrl + (type == TASK ? getOneTask : getOneNotificationFirebase) + id,
      options: Options(
        headers: <String, String>{
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + '$token'
        },
      ),
    );

    if (response.statusCode == 200) {
      final parsed = response.data;
      print("getData (forIsAccepted) Success: $parsed");

      if (type == NOTIFICATION) {
        var item = n.NotificationDocs.fromJson(parsed);
        return item.isRead ?? false;
      } else {
        var item = TaskDocs.fromJson(parsed);
        return item.isRead ?? false;
      }
    } else {
      print("getData Error: ${response.data}");
      return false;
    }
  } catch (e) {
    print(e);
    return false;
  }
}

Future<dynamic> getData(dynamic id, String type) async {
  final Dio dio = Dio();
  final GetStorage gs = GetStorage();
  final String token = gs.read(BEARER_TOKEN);

  try {
    final response = await dio.get(
      baseUrl +
          (type == TASK ? getOneTaskFirebase : getOneNotificationFirebase) +
          id,
      options: Options(
        headers: <String, String>{
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + '$token'
        },
      ),
    );

    if (response.statusCode == 200) {
      final parsed = response.data;
      print("getData Success: $parsed");

      if (type == NOTIFICATION)
        return n.NotificationDocs.fromJson(parsed);
      else
        return TaskDocs.fromJson(parsed);
    } else {
      print("getData Error: ${response.data}");
      return null;
    }
  } catch (e) {
    print(e);
    return null;
  }
}

Future<String> titleMaker(String id, String type) async {
  switch (type) {
    case NOTIFICATION:
      {
        return '${await titleMakerNotification()} ${id.addExtraSpacesBefore(5)}';
      }

    case TASK:
      {
        return '${await titleMakerTask()} ${id.addExtraSpacesBefore(5)}';
      }

    default:
      {
        return '${await titleMakerResponse()} ${id.addExtraSpacesBefore(5)}';
      }
  }
}

/// Set right date and time for notifications
tz.TZDateTime _convertTime(int hour, int minutes) {
  final tz.TZDateTime now = tz.TZDateTime.now(tz.local);
  tz.TZDateTime scheduleDate = tz.TZDateTime(
    tz.local,
    now.year,
    now.month,
    now.day,
    hour,
    minutes,
  );
  if (scheduleDate.isBefore(now)) {
    scheduleDate = scheduleDate.add(const Duration(days: 1));
  }
  return scheduleDate;
}

Future<void> _configureLocalTimeZone() async {
  tz.initializeTimeZones();
  final String timeZone = await FlutterTimezone.getLocalTimezone();
  tz.setLocalLocation(tz.getLocation(timeZone));
}

/// Scheduled Notification
scheduledNotification({
  required int hour,
  required int minutes,
  required int id,
}) async {
  await flutterLocalNotificationsPlugin.zonedSchedule(
    id,
    'It\'s time to drink water!',
    'After drinking, touch the cup to confirm',
    _convertTime(hour, minutes),
    NotificationDetails(
      android: AndroidNotificationDetails(
        'scheduled_notification_channel', // id
        'For scheduled Notifications', // title
        channelDescription: 'This channel is used for scheduled notifications.',
        importance: Importance.max,
        priority: Priority.high,
      ),
      iOS: DarwinNotificationDetails(),
    ),
    //androidAllowWhileIdle: true,
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.absoluteTime,
    matchDateTimeComponents: DateTimeComponents.time,
    payload: 'It could be anything you pass', androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
  );
}

getActiveNotifications() async {
  var a = await flutterLocalNotificationsPlugin.getActiveNotifications();
  for (var element in a) {
    print("Active: ${element.id}");
  }
}

setScheduledNotification() async {
  ///For testing only
  // List<ScheduledRecord> scheduleRecords = [
  //   ScheduledRecord(time: DateTime(2024, 02, 01), actualId: 'abcd'),
  //   ScheduledRecord(time: DateTime(2024, 02, 02), actualId: 'abcd'),
  //   // ScheduledRecord(id: 12, time: "04:40"),
  // ];
  //
  // for (int i = 0; i < scheduleRecords.length; i++) {
  //   await scheduledNotification(
  //     hour: scheduleRecords[i].time.hour,
  //     minutes: scheduleRecords[i].time.minute,
  //     id: scheduleRecords[i].localId,
  //   );
  //   print('set ${scheduleRecords[i].localId}');
  // }
}

extension StringExtension on String {
  String addExtraSpacesBefore(int count) {
    String spaces = ' ' * count;
    return spaces + '№' + this;
  }
}
