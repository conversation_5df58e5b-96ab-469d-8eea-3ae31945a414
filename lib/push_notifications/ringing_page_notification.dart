import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/database/embeded_models.dart' as n;
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/functions/date_picker.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/features/home/<USER>/pages/home.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

import 'background_animation.dart';
import 'notification_service.dart';

class RingingPageNotification extends StatefulWidget {
  final dynamic receivedNotification;

  RingingPageNotification({Key? key, required this.receivedNotification})
      : super(key: key);

  @override
  _RingingPageNotificationState createState() =>
      _RingingPageNotificationState();
}

class _RingingPageNotificationState extends State<RingingPageNotification> {
  final GlobalKey<BackgroundAnimationState> backAnimKey = GlobalKey();
  DateTime selectedDate = DateTime.now();
  final DateFormat dateFormat = DateFormat('dd.MM.yyyy');
  late String statusNameUZ;
  late String statusNameRU;
  late String statusNameCR;
  late Color color;
  late bool isDark;

  @override
  void initState() {
    color = HexColor.fromHex(
        widget.receivedNotification.payload?["statusColor"] ?? "#000000");
    statusNameUZ =
        widget.receivedNotification.payload?["statusNameUZ"] ?? 'status';
    statusNameRU =
        widget.receivedNotification.payload?["statusNameRU"] ?? 'status';
    statusNameCR =
        widget.receivedNotification.payload?["statusNameCR"] ?? 'status';
    super.initState();
    // playRing();
  }

  @override
  void dispose() {
    // widget.audioPlayer?.stop();
    // widget.audioPlayer?.dispose();
    // FlutterRingtonePlayer.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        title: Center(
            child: Text(
          LocaleKeys.notification_push_title.tr(),
          style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w400),
        )),
        automaticallyImplyLeading: false,
        elevation: 0,
      ),
      body: Container(
        child: Stack(
          alignment: Alignment.center,
          fit: StackFit.expand,
          children: [
            BackgroundAnimation(
              key: backAnimKey,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.receivedNotification.title.toString(),
                          style: TextStyle(
                              fontSize: 20.sp,
                              color: isDark ? cWhiteColor : cFirstColor),
                        ),
                        SizedBox(height: 20.h),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${LocaleKeys.notification_status.tr()}:',
                                style: TextStyle(
                                    fontSize: 15.sp, color: cGrayColor),
                              ),
                              Text(
                                statusNameUZ,
                                textAlign: TextAlign.center,
                                style: TextStyle(fontSize: 15.sp, color: color),
                              ),
                            ]),
                        SizedBox(height: 20.h),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${LocaleKeys.send_organisation.tr()}:',
                                style: TextStyle(
                                    fontSize: 15.sp, color: cGrayColor),
                              ),
                              Container(
                                width: 220.w,
                                child: Text(
                                  widget.receivedNotification
                                          .payload?["user"] ??
                                      'user',
                                  textAlign: TextAlign.right,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(fontSize: 15.sp),
                                ),
                              ),
                            ]),
                        SizedBox(height: 40.h),
                        Text(
                          widget.receivedNotification.body.toString(),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontSize: 20.sp, fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 10.h),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: cGrayColor.withAlpha(40)),
                          child: Html(
                            data: widget.receivedNotification
                                    .payload?["actualBody"] ??
                                "<p>Loading..</p>",
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            TextButton(
                              child: Text(
                                "${LocaleKeys.change_time.tr()}",
                                style: TextStyle(
                                    fontSize: 15.sp, color: cGrayColor),
                              ),
                              onPressed: () async {
                                showDateTimePicker(context, onPressedOK: (val) {
                                  setState(() {
                                    selectedDate = val;
                                  });
                                  print("hello:${widget.receivedNotification}");

                                  var data = n.Notification(
                                    id: widget.receivedNotification
                                        .payload?["actualId"],
                                    notificationId: widget.receivedNotification
                                        .payload?["actualId"],
                                    title: widget.receivedNotification.title,
                                    content: widget.receivedNotification
                                        .payload?["actualBody"],
                                    statusName: StatusName(
                                        cr: statusNameCR,
                                        ru: statusNameRU,
                                        uz: statusNameUZ),
                                    statusColor: widget.receivedNotification
                                        .payload?["statusColor"],
                                    moderator: EmbeddedModerator(
                                        fullName: widget.receivedNotification
                                            .payload?["user"]),
                                  );

                                  addScheduledNotification(
                                      dateTime: selectedDate, data: data);
                                  CustomToast.showToast(
                                      LocaleKeys.alarm_added.tr());

                                  // FlutterRingtonePlayer.stop();

                                  Navigator.pushAndRemoveUntil(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => HomePage()),
                                      (route) => false);
                                });
                              },
                            ),
                          ],
                        ),
                        TextButton(
                          child: Text(
                            LocaleKeys.understandable.tr(),
                            style: TextStyle(fontSize: 15.sp),
                          ),
                          onPressed: () {
                            // widget.audioPlayer?.stop();
                            // FlutterRingtonePlayer.stop();
                            backAnimKey.currentState?.stopAnimation();
                            Navigator.pushAndRemoveUntil(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => HomePage()),
                                (route) => false);
                            // exit(0); // Close the app
                          },
                        ),
                      ],
                    )
                  ]),
            ),
          ],
        ),
      ),
    );
  }
}
