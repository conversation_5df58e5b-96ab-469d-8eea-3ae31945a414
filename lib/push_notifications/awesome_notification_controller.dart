import 'dart:isolate';
import 'dart:ui';

import 'package:alarm/alarm.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:context_holder/context_holder.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/push_notifications/ringing_page_notification.dart';
import 'package:ijrochi/push_notifications/notification_service.dart';
import 'package:ijrochi/push_notifications/ringing_page_task.dart';
import 'package:local_notifier/local_notifier.dart';
import 'package:shared_preferences/shared_preferences.dart';

///  *********************************************
///     NOTIFICATION CONTROLLER
///  *********************************************

class NotificationController {
  static ReceivedAction? initialAction;

  static var _initialized = true;

  void bindBackgroundIsolate() {
    ReceivePort port = ReceivePort();
    IsolateNameServer.registerPortWithName(port.sendPort, 'background_isolate');

    port.listen((var received) async {
      _handleBackgroundAction(received);
    });
  }

  /// ============================ NOT USED  =========================== ///

  ///TODO: NOTES!
  /// Don't forget, you can't get valid context after opening killed app when set scheduled notification ///
  /// EVEN WITH SWITCHING ISOLATES ///

  static Future<void> onSilentActionHandle(ReceivedAction received) async {
    print('On new background action received: ${received.toMap()}');

    if (!_initialized) {
      SendPort? uiSendPort =
      IsolateNameServer.lookupPortByName('background_isolate');
      if (uiSendPort != null) {
        print(
            'Background action running on parallel isolate without valid context. Redirecting execution');
        uiSendPort.send(received);
        return;
      }
    }

    print('Background action running on main isolate');
    await _handleBackgroundAction(received);
  }

  static Future<void> _handleBackgroundAction(ReceivedAction received) async {
    ///
  }

  ///===================================================================///

  // static AudioPlayer audioPlayer = di();
  // static Source source =
  //     AssetSource('ringtone.m4a'); // Replace with your sound file path

  static late ReceivedNotification receivedNotification;
  static AndroidDeviceInfo androidInfo = di();

  ///  *********************************************
  ///     INITIALIZATIONS
  ///  *********************************************
  ///
  static Future<void> initializeLocalNotifications() async {
    ///Desktop notification init! ==============================

    // Add in main method.
    await localNotifier.setup(
      appName: 'Ijrochi',
      // The parameter shortcutPolicy only works on Windows
      shortcutPolicy: ShortcutPolicy.requireCreate,
    );

    ///=========================================================

    await AwesomeNotifications().initialize(
        null, //'resource://drawable/res_app_icon',//
        [
          NotificationChannel(
              channelKey: 'basic_channel',
              channelName: 'Simple notifications',
              channelDescription: 'Notification are awesome!',
              playSound: true,
              onlyAlertOnce: true,
              locked: true,
              // soundSource: 'resource://raw/ringtone',
              defaultRingtoneType: DefaultRingtoneType.Notification,
              groupAlertBehavior: GroupAlertBehavior.Children,
              importance: NotificationImportance.Max,
              defaultPrivacy: NotificationPrivacy.Private,
              defaultColor: cFirstColor,
              ledColor: cSecondColor),
          NotificationChannel(
              channelKey: 'sound_channel_notification',
              channelName: 'Scheduled alerts',
              channelDescription: 'Notification tests as alerts',
              playSound: true,
              onlyAlertOnce: false,
              // soundSource: 'resource://raw/ringtone',
              defaultRingtoneType: DefaultRingtoneType.Alarm,
              enableVibration: true,
              groupAlertBehavior: GroupAlertBehavior.All,
              importance: NotificationImportance.Max,
              criticalAlerts: true,
              locked: true,
              defaultPrivacy: NotificationPrivacy.Public,
              defaultColor: cFirstColor,
              ledColor: cSecondColor),
          NotificationChannel(
              channelKey: 'sound_channel_task',
              channelName: 'Scheduled alerts',
              channelDescription: 'Notification tests as alerts',
              playSound: true,
              onlyAlertOnce: false,
              // soundSource: 'resource://raw/ringtone',
              defaultRingtoneType: DefaultRingtoneType.Alarm,
              enableVibration: true,
              groupAlertBehavior: GroupAlertBehavior.All,
              importance: NotificationImportance.Max,
              criticalAlerts: true,
              locked: true,
              defaultPrivacy: NotificationPrivacy.Public,
              defaultColor: cFirstColor,
              ledColor: cSecondColor),
        ],
        debug: true);

    // Get initial notification action is optional
    initialAction = await AwesomeNotifications()
        .getInitialNotificationAction(removeFromActionEvents: false);
  }

  ///  *********************************************
  ///     NOTIFICATION EVENTS LISTENER
  ///  *********************************************
  ///  Notifications events are only delivered after call this method
  static Future<void> startListeningNotificationEvents() async {
    AwesomeNotifications().setListeners(
        onActionReceivedMethod: onActionReceivedMethod,
        onNotificationCreatedMethod: onNotificationCreatedMethod,
        onDismissActionReceivedMethod: onDismissActionReceivedMethod,
        onNotificationDisplayedMethod: onNotificationDisplayedMethod);
  }

  ///  *********************************************
  ///     NOTIFICATION EVENTS
  ///  *********************************************

  ///TODO: NOTES!
  /// Here context is valid even app is killed when notification is not from schedule///

  @pragma('vm:entry-point')
  static Future<void> onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    print(
        "Action (sometimes auto on lower SDK) is: ${receivedAction.buttonKeyPressed}");

    print("=== Payload: ${receivedAction.payload}");

    if (receivedAction.buttonKeyPressed == REDIRECT) {
      // For background actions, you must hold the execution until the end

      openRingingPage(receivedAction);
    } else if (receivedAction.buttonKeyPressed == ACCEPT) {
      open(receivedAction.payload?['uid'] ?? '-1',
          receivedAction.payload?['type'] ?? NULL, ACCEPT);
    } else if (receivedAction.buttonKeyPressed == OPEN) {
      open(receivedAction.payload?['uid'] ?? '-1',
          receivedAction.payload?['type'] ?? NULL, OPEN);
    } else {
      if (receivedAction.channelKey == 'sound_channel_notification' ||
          receivedAction.channelKey == 'sound_channel_task') {
        openRingingPage(receivedAction);
      } else if (receivedAction.channelKey == 'basic_channel') {
        open(receivedAction.payload?['uid'] ?? '-1',
            receivedAction.payload?['type'] ?? NULL, OPEN);
      }
    }
  }

  ///TODO: Bug in iOS: https://github.com/rafaelsetragni/awesome_notifications/issues/743
  ///
  /// Solution: https://github.com/rafaelsetragni/awesome_notifications/issues/717

  /// Use this method to detect every time that a new notification is displayed
  @pragma("vm:entry-point")
  static Future<void> onNotificationDisplayedMethod(
      ReceivedNotification notification) async {
    receivedNotification = notification;

    var androidSDK = androidInfo.version.sdkInt;

    print(
        'Android: onNotificationDisplayedMethod | If you see me on IOS, issue has been solved!');

    // Your code goes here
    if (notification.channelKey == 'sound_channel_notification' ||
        notification.channelKey == 'sound_channel_task') {
      // audioPlayer.play(source);
      // audioPlayer.setReleaseMode(ReleaseMode.loop);

      // FlutterRingtonePlayer.play(
      //   android: AndroidSounds.alarm,
      //   ios: IosSounds.alarm,
      //   looping: true,
      //   // Android only - API >= 28
      //   volume: 0.5,
      //   // Android only - API >= 28
      //   asAlarm: true, // Android only - all APIs
      // );

      var type = receivedNotification.payload?['type'] ?? '';
      var actualId = receivedNotification.payload?['actualId'] ?? '';

      if (type == NOTIFICATION) {
        Isolate.spawn(deleteScheduledNotificationById(actualId: actualId),
            "Hello, World!");
      } else {
        Isolate.spawn(
            deleteScheduledTaskById(actualId: actualId), "Hello, World!");
      }
    }

    if (androidSDK < 29) {
      // openRingingPage(receivedNotification);
      ///What should i do here? Haha, haven't thought about yet..
    }
  }

  /// Use this method to detect when a new notification or a schedule is created
  @pragma("vm:entry-point")
  static Future<void> onNotificationCreatedMethod(
      ReceivedNotification receivedNotification) async {
    print("Notification ${receivedNotification.id} is created!");
  }

  /// Use this method to detect if the user dismissed a notification
  @pragma("vm:entry-point")
  static Future<void> onDismissActionReceivedMethod(
      ReceivedAction receivedAction) async {
    // Your code goes here
    // audioPlayer.stop();
    // FlutterRingtonePlayer.stop();
    print('Dismissed!');
  }

  static openRingingPage(dynamic receivedAction) {
    if (receivedAction.payload?['type'] == NOTIFICATION) {
      try {
        Navigator.push(
            ContextHolder.currentContext,
            MaterialPageRoute(
                builder: (context) => RingingPageNotification(
                  receivedNotification: receivedNotification,
                )));
      } catch (e) {
        print(e);
      }
    } else {
      try {
        Navigator.push(
            ContextHolder.currentContext,
            MaterialPageRoute(
                builder: (context) => RingingPageTask(
                  receivedNotification: receivedNotification,
                )));
      } catch (e) {
        print(e);
      }
    }

    print("=== Ringing page opened up! ===");
  }

  ///  *********************************************
  ///     REQUESTING NOTIFICATION PERMISSIONS
  ///  *********************************************
  ///
  static Future<bool> displayNotificationRationale(BuildContext context) async {
    bool userAuthorized = false;

    await showDialog(
        context: context,
        builder: (BuildContext ctx) {
          return AlertDialog(
            title: Text('Get Notified!',
                style: Theme.of(context).textTheme.titleLarge),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Image.asset(
                        'assets/animated-bell.gif',
                        height: MediaQuery.of(context).size.height * 0.3,
                        fit: BoxFit.fitWidth,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Text(
                    'Allow Awesome Notifications to send you beautiful notifications!'),
              ],
            ),
            actions: [
              TextButton(
                  onPressed: () {
                    Navigator.of(ctx).pop();
                  },
                  child: Text(
                    'Deny',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(color: Colors.red),
                  )),
              TextButton(
                  onPressed: () async {
                    userAuthorized = true;
                    Navigator.of(ctx).pop();
                  },
                  child: Text(
                    'Allow',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(color: Colors.deepPurple),
                  )),
            ],
          );
        });
    return userAuthorized &&
        await AwesomeNotifications().requestPermissionToSendNotifications();
  }

  ///  *********************************************
  ///     NOTIFICATION CREATION METHODS
  ///  *********************************************

  static Future<void> createNewNotification(data) async {
    //for reading language from preference
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String lang = prefs.getString(language_pref) ?? 'uz';
    bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
    // if (!isAllowed) isAllowed = await displayNotificationRationale(context);
    if (!isAllowed) return;

    var id = data['_id'];
    var serialNumber = data['serialNumber'];
    var type = data['type'];

    await AwesomeNotifications().createNotification(
        content: NotificationContent(
            id: -1,
            // -1 is replaced by a random number
            channelKey: 'basic_channel',
            title: await titleMaker(serialNumber, type),
            body: data['body'],
            notificationLayout: NotificationLayout.BigText,
            actionType: ActionType.Default,
            color: cFirstColor,
            backgroundColor: cSecondColor,
            wakeUpScreen: true,
            criticalAlert: true,
            payload: {'uid': id, 'type': type}),
        actionButtons: [
          NotificationActionButton(
              key: ACCEPT, label: lang == "uz" ? "Qabul qilish" : "Принятие"),
          NotificationActionButton(
              key: OPEN, label: lang == "uz" ? "Ochish" : "Открыть"),
          // NotificationActionButton(
          //     key: 'DISMISS',
          //     label: 'Dismiss',
          //     actionType: ActionType.DismissAction,
          //     isDangerousOption: true)
        ]);
  }

  ///Payload's id property always returns null, so set id to "uid" String

  static Future<bool> scheduleNewNotification(
      {required int id,
        required String actualId,
        required String title,
        required String body,
        required String actualBody,
        required String statusColor,
        required StatusName statusName,
        required String user,
        required DateTime dateTime}) async {
    //for reading language from preference
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String lang = prefs.getString(language_pref) ?? 'uz';
    bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
    // if (!isAllowed) isAllowed = await displayNotificationRationale(context);
    if (!isAllowed) return true;

    return await AwesomeNotifications().createNotification(
        content: NotificationContent(
            id: id,
            // -1 is replaced by a random number
            channelKey: 'sound_channel_notification',
            title: title,
            body: body,
            // bigPicture: 'https://storage.googleapis.com/cms-storage-bucket/d406c736e7c4c57f5f61.png',
            // largeIcon: 'https://storage.googleapis.com/cms-storage-bucket/0dbfcc7a59cd1cf16282.png',
            //'asset://assets/images/balloons-in-sky.jpg',
            notificationLayout: NotificationLayout.BigText,
            category: NotificationCategory.Call,
            autoDismissible: false,
            locked: true,
            criticalAlert: true,
            fullScreenIntent: true,
            wakeUpScreen: true,
            actionType: ActionType.Default,
            payload: {
              'statusNameUZ': statusName.uz,
              'statusNameRU': statusName.ru,
              'statusNameCR': statusName.cr,
              'actualBody': actualBody,
              'statusColor': statusColor,
              'user': user,
              'type': NOTIFICATION,
              'actualId': actualId
            }),
        actionButtons: [
          NotificationActionButton(
              key: REDIRECT, label: lang == "uz" ? "Ochish" : "Открыть"),
          NotificationActionButton(
              key: DISMISS,
              label: lang == "uz" ? "Tushunarli" : "Понятно",
              actionType: ActionType.DismissAction,
              isDangerousOption: true)
        ],
        schedule: NotificationCalendar.fromDate(
            date: dateTime, preciseAlarm: true, repeats: true));
  }

  ///Payload's id property always returns null, so set id to "uid" String

  static Future<bool> scheduleNewTask(
      {required int id,
        required String actualId,
        required String title,
        required String body,
        required String actualBody,
        required String statusColor,
        required String sendDate,
        required String endDate,
        required String user,
        required DateTime dateTime,
        required String serialNumber}) async {
    //for reading language from preference
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String lang = prefs.getString(language_pref) ?? 'uz';
    bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
    // if (!isAllowed) isAllowed = await displayNotificationRationale(context);
    if (!isAllowed) return true;

    return await AwesomeNotifications().createNotification(
        content: NotificationContent(
            id: id,
            // -1 is replaced by a random number
            channelKey: 'sound_channel_task',
            title: title,
            body: body,
            // bigPicture: 'https://storage.googleapis.com/cms-storage-bucket/d406c736e7c4c57f5f61.png',
            // largeIcon: 'https://storage.googleapis.com/cms-storage-bucket/0dbfcc7a59cd1cf16282.png',
            //'asset://assets/images/balloons-in-sky.jpg',
            notificationLayout: NotificationLayout.BigText,
            category: NotificationCategory.Call,
            autoDismissible: true,
            locked: true,
            criticalAlert: true,
            fullScreenIntent: true,
            wakeUpScreen: true,
            actionType: ActionType.Default,
            payload: {
              'sendDate': sendDate,
              'endDate': endDate,
              'statusColor': statusColor,
              'actualBody': actualBody,
              'user': user,
              'type': TASK,
              'actualId': actualId,
              'serialNumber': serialNumber
            }),
        actionButtons: [
          NotificationActionButton(
              key: REDIRECT, label: lang == "uz" ? "Ochish" : "Открыть"),
          NotificationActionButton(
              key: DISMISS,
              label: lang == "uz" ? "Tushunarli" : "Понятно",
              actionType: ActionType.DismissAction,
              isDangerousOption: true)
        ],
        schedule: NotificationCalendar.fromDate(
            date: dateTime, preciseAlarm: true, repeats: true));
  }

  static Future<void> resetBadgeCounter() async {
    await AwesomeNotifications().resetGlobalBadge();
  }

  static Future<void> cancelNotifications() async {
    await AwesomeNotifications().cancelAll();
  }

  static Future<void> cancelNotificationById(int id) async {
    await AwesomeNotifications().cancel(id);
  }

  /// ************************ Default alarm SAMPLE ************************* ///
  ///TODO need to remove if it does not use
  static setAlarm(DateTime time) async {
    // setLaunchableAlarm(time.subtract(Duration(seconds: 10)));
    final alarmSettings = AlarmSettings(
      id: 68,
      dateTime: time,
      assetAudioPath: Assets.assetsRingtone,
      loopAudio: true,
      vibrate: true,
      fadeDuration: 20.0,
      notificationSettings: NotificationSettings(
        title: 'This is the title', // optional
        body: 'This is the body', // optional
      ),
    );

    await Alarm.set(alarmSettings: alarmSettings);
    Alarm.ringStream.stream.listen((_) {
      print('Running......');
    });
  }

  static stopAlarms() async {
    var alarms = await Alarm.getAlarms();
    for (AlarmSettings a in alarms) {
      await Alarm.stop(a.id);
      print("Stopped alarm id: ${a.id}");
    }
  }

// static setLaunchableAlarm(DateTime time) async {
//   var alarmPlugin = FlutterAlarmBackgroundTrigger();
//   alarmPlugin.addAlarm(
//       // Required
//       time,
//       //Optional
//       uid: "YOUR_APP_ID_TO_IDENTIFY",
//       payload: {"YOUR_EXTRA_DATA": "FOR_ALARM"},
//
//       // screenWakeDuration: For how much time you want
//       // to make screen awake when alarm triggered
//       screenWakeDuration: Duration(minutes: 1));
//
//   alarmPlugin.requestPermission().then((isGranted) {
//     if (isGranted) {
//       alarmPlugin.onForegroundAlarmEventHandler((alarm) {
//         // Perform your action here such as navigation
//         // This event will be triggered on both cases,
//         // when app is in foreground or background!
//         // print(alarm);
//         WidgetsBinding.instance.addPostFrameCallback((time) {
//           Navigator.of(ContextHolder.currentContext).push(MaterialPageRoute(
//             builder: (context) => HomePage(),
//           ));
//         });
//         stopAlarms();
//       });
//     }
//   });
// }
}