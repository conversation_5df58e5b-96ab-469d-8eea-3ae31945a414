import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/lock/presentation/pages/lock_page.dart';
import 'package:ijrochi/features/login/presentation/pages/login_page.dart';
import 'package:ijrochi/features/notification_detail/presentation/page/notification_detailed_page.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart'
    as n;
import 'package:ijrochi/features/payments/lock_switcher.dart';
import 'package:ijrochi/features/task_detail/page/task_detail_page.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../di/dependency_injection.dart';
import 'notification_service.dart';
import 'ringing_page_notification.dart';
import 'ringing_page_task.dart';

///It's addon for our working code in case app can't get context when killed

Future<Widget> getInitialPage() async {
  final SharedPreferences prefs = di();

  ReceivedAction? receivedAction = await AwesomeNotifications()
      .getInitialNotificationAction(removeFromActionEvents: true);

  var lastStamp = prefs.getString(LAST_ACTION_STAMP) ?? null;

  if ((receivedAction?.channelKey == 'sound_channel_notification' ||
          receivedAction?.channelKey == 'sound_channel_task') &&
      receivedAction != null) {
    if (lastStamp != receivedAction.displayedDate.toString()) {
      print(
          'SOUND-CHANNEL: ========== Set initial page to RINGING PAGE ==========');
      prefs.setString(
          LAST_ACTION_STAMP, receivedAction.displayedDate.toString());
      return setInitialPageToRingingPage(receivedAction);
    } else {
      print(
          'SOUND-CHANNEL: ========== Set initial page to HOME PAGE ==========');
      return setInitialPageToHomePage();
    }
  } else if (receivedAction?.channelKey == 'basic_channel' &&
      receivedAction != null) {
    if (lastStamp != receivedAction.displayedDate.toString() &&
        receivedAction.buttonKeyPressed == OPEN) {
      print(
          'BASIC-CHANNEL: ========== Set initial page to OPEN PAGE ==========');
      prefs.setString(
          LAST_ACTION_STAMP, receivedAction.displayedDate.toString());
      return openReturn(receivedAction.payload?['uid'] ?? '-1',
          receivedAction.payload?['type'] ?? NULL, OPEN);
    } else if (lastStamp != receivedAction.displayedDate.toString() &&
        receivedAction.buttonKeyPressed == ACCEPT) {
      print(
          'BASIC-CHANNEL: ========== Set initial page to ACCEPT PAGE ==========');
      prefs.setString(
          LAST_ACTION_STAMP, receivedAction.displayedDate.toString());
      return openReturn(receivedAction.payload?['uid'] ?? '-1',
          receivedAction.payload?['type'] ?? NULL, ACCEPT);
    } else {
      print(
          'BASIC-CHANNEL: ========== Set initial page to HOME PAGE ==========');
      return setInitialPageToHomePage();
    }
  } else {
    print('========== Set initial page to HOME PAGE ==========');
    return setInitialPageToHomePage();
  }
}

Widget setInitialPageToRingingPage(ReceivedAction receivedAction) {
  if (receivedAction.payload?['type'] == NOTIFICATION) {
    return RingingPageNotification(receivedNotification: receivedAction);
  } else {
    return RingingPageTask(
      receivedNotification: receivedAction,
    );
  }
}

class Loading extends StatefulWidget {
  const Loading({super.key});

  @override
  State<Loading> createState() => _LoadingState();
}

class _LoadingState extends State<Loading> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
            height: double.infinity,
            width: double.infinity,
            child: Center(
              child: CircularProgressIndicator(
                color: cFirstColor,
              ),
            )));
  }
}

Widget setInitialPageToHomePage() {
  final SharedPreferences prefs = di();
  final GetStorage getStorage = di();

  //Ensure to stop ringing if something happens wrong!
  // FlutterRingtonePlayer.stop();

  String id = getStorage.read(ID) ?? "";
  bool isLoggedIn = (prefs.getString('pin_code') ?? '') != '';
  return (id == ""
      ? LoginPage.screen()
      : isLoggedIn
          ? LockPage()
          : LockProvider());
}

Future<Widget> openReturn(String id, String type, String ACTION) async {
  var isAccepted = await isAcceptedFunc(id, type);

  if (type == NOTIFICATION) {
    n.NotificationDocs? notification = await getData(id, type);
    if (notification != null) {
      if (!isAccepted) {
        return NotificationDetailedPage.screen(
            notificationDocs: notification, pushKey: ACTION, shouldLock: true);
      } else {
        return NotificationDetailedPage.screen(
            notificationDocs: notification, shouldLock: true);
      }
    } else {
      return setInitialPageToHomePage();
    }
  } else {
    TaskDocs? task = await getData(id, type);
    if (task != null) {
      if (!isAccepted) {
        return TaskDetailPage.screen(
            taskDocs: task, pushKey: ACTION, shouldLock: true);
      } else {
        return TaskDetailPage.screen(taskDocs: task, shouldLock: true);
      }
    } else {
      return setInitialPageToHomePage();
    }
  }
}
