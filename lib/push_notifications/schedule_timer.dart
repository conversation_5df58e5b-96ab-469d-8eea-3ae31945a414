import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:intl/intl.dart';

class NotificationScheduler {
  ///TODO: Clear mockup and write test

  /// ***************** Every AutoScheduler ids should be random ****************** ///

  /// Rings at specific days if TASK is accepted
  static void scheduleTaskLastThreeDays(DateTime deadline, TaskModel data) {
    DateTime currentDate = DateTime.now();
    // print(currentDate);
    Duration remainingDays = deadline.difference(currentDate);
    // print(remainingDays.inDays);
    int days = remainingDays.inDays;

    if (days >= 3) {
      for (int i = 0; i <= 3; i++) {
        if (i == 0) {
          DateTime iterationDate = deadline.subtract(Duration(days: i));
          DateTime dateTime1 = DateTime(
              iterationDate.year, iterationDate.month, iterationDate.day, 9, 0);
          DateTime dateTime2 = DateTime(iterationDate.year, iterationDate.month,
              iterationDate.day, 14, 0);
          DateTime dateTime3 = DateTime(iterationDate.year, iterationDate.month,
              iterationDate.day, 17, 0);
          printFormattedDate(dateTime1);
          printFormattedDate(dateTime2);
          printFormattedDate(dateTime3);
        } else if (i == 1) {
          DateTime iterationDate = deadline.subtract(Duration(days: i));
          DateTime dateTime1 = DateTime(
              iterationDate.year, iterationDate.month, iterationDate.day, 9, 0);
          DateTime dateTime2 = DateTime(iterationDate.year, iterationDate.month,
              iterationDate.day, 17, 0);
          printFormattedDate(dateTime1);
          printFormattedDate(dateTime2);
        } else if (i == 2) {
          DateTime iterationDate = deadline.subtract(Duration(days: i));
          DateTime dateTime = DateTime(iterationDate.year, iterationDate.month,
              iterationDate.day, 14, 0);
          printFormattedDate(dateTime);
        }
      }
    } else if (days == 2) {
      for (int i = 0; i <= 2; i++) {
        if (i == 0) {
          DateTime iterationDate = deadline.subtract(Duration(days: i));
          DateTime dateTime1 = DateTime(
              iterationDate.year, iterationDate.month, iterationDate.day, 9, 0);
          DateTime dateTime2 = DateTime(iterationDate.year, iterationDate.month,
              iterationDate.day, 14, 0);
          DateTime dateTime3 = DateTime(iterationDate.year, iterationDate.month,
              iterationDate.day, 17, 0);
          printFormattedDate(dateTime1);
          printFormattedDate(dateTime2);
          printFormattedDate(dateTime3);
        } else if (i == 1) {
          DateTime iterationDate = deadline.subtract(Duration(days: i));
          DateTime dateTime1 = DateTime(
              iterationDate.year, iterationDate.month, iterationDate.day, 9, 0);
          DateTime dateTime2 = DateTime(iterationDate.year, iterationDate.month,
              iterationDate.day, 17, 0);
          printFormattedDate(dateTime1);
          printFormattedDate(dateTime2);
        }
      }
    } else if (days == 1) {
      DateTime iterationDate = deadline.subtract(Duration(days: 0));
      DateTime dateTime1 = DateTime(
          iterationDate.year, iterationDate.month, iterationDate.day, 9, 0);
      DateTime dateTime2 = DateTime(
          iterationDate.year, iterationDate.month, iterationDate.day, 14, 0);
      DateTime dateTime3 = DateTime(
          iterationDate.year, iterationDate.month, iterationDate.day, 17, 0);
      printFormattedDate(dateTime1);
      printFormattedDate(dateTime2);
      printFormattedDate(dateTime3);
    }
  }

  /// Rings every day if TASK is not accepted
  static void scheduleTaskForEveryDay(DateTime deadline, TaskModel data) {
    DateTime currentDate = DateTime.now();
    // print(currentDate);
    Duration remainingDays = deadline.difference(currentDate);
    // print(remainingDays.inDays);
    int days = remainingDays.inDays;
    print('Leftover days =======> $days');
    for (int i = 0; i <= days; i++) {
      var date = deadline.subtract(Duration(days: i));
      var dateTime = date.copyWith(hour: 14, minute: 0);
      printFormattedDate(dateTime);
    }
  }

  static void scheduleTaskForMock(DateTime deadline, TaskModel data) {
    DateTime currentDate = DateTime.now();

    for (int i = 1; i <= 3; i++) {
      var dateTime = currentDate.add(Duration(minutes: i));
      printFormattedDate(dateTime);
    }
  }

  static void printFormattedDate(DateTime dateTime) {
    String formattedDate = DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
    print("====== TASK ====== [Scheduled for $formattedDate]");
  }
}
