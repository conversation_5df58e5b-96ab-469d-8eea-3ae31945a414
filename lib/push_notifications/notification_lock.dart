import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/widgets/auth.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/features/app.dart';
import 'package:ijrochi/features/lock/presentation/widgets/num_pad.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/utils/app_constants.dart';
import '../../../../di/dependency_injection.dart';

class LockPageNotification extends StatefulWidget {
  final VoidCallback onAuthenticated;

  const LockPageNotification({Key? key, required this.onAuthenticated}) : super(key: key);

  @override
  State<LockPageNotification> createState() => _LockPageState();
}

class _LockPageState extends State<LockPageNotification> {
  final SharedPreferences sharedPreferences = di();

  late bool isLoggedIn;
  late bool useBiometric;
  late bool isDark;
  String? pin_code = '';

  @override
  void initState() {
    super.initState();
    pin_code = sharedPreferences.getString(pin_code_pref) ?? '';
    useBiometric = sharedPreferences.getBool('use_biometric') ?? true;
    isLoggedIn = !(pin_code == '');
    if (useBiometric && isLoggedIn) {
      authenticate();
    }
  }

  final TextEditingController _pinPutController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      color: isDark ? cFirstColorDark : cFirstColor,
      child: SafeArea(
        bottom: false,
        child: Scaffold(
          body: Column(
            children: [
              const Spacer(flex: 2),
              Text(
                LocaleKeys.entering_pin_code.tr(),
                style: TextStyle(
                  color: cFirstColor,
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 30.h),
              SizedBox(
                width: 160.w,
                height: 30.h,
                child: PinCodeTextField(
                  animationType: AnimationType.fade,
                  animationCurve: Curves.easeInCirc,
                  useHapticFeedback: true,
                  showCursor: false,
                  appContext: context,
                  controller: _pinPutController,
                  length: 4,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  obscuringWidget: Container(
                    height: 30.h,
                    width: 30.w,
                    decoration: BoxDecoration(
                      color: cFirstColor,
                      borderRadius: BorderRadius.circular(50.r),
                    ),
                  ),
                  enableActiveFill: true,
                  enablePinAutofill: true,
                  pinTheme: PinTheme(
                    fieldHeight: 25.h,
                    fieldWidth: 25.w,
                    borderWidth: 1.5.w,
                    shape: PinCodeFieldShape.circle,
                    activeColor: cFirstColor,
                    inactiveColor: cFirstColor,
                    disabledColor: cWhiteColor,
                    activeFillColor: cWhiteColor,
                    selectedFillColor: cWhiteColor,
                    inactiveFillColor: cWhiteColor,
                    errorBorderColor: cWhiteColor,
                  ),
                  onChanged: (v) {},
                  onCompleted: (v) async {
                    if (pin_code == v) {
                      shouldLockStatic = false;
                      widget.onAuthenticated();
                    } else {
                      Snack(LocaleKeys.pin_code_no_same.tr(), context,
                          cRedColor);
                      _pinPutController.clear();
                    }
                  },
                ),
              ),
              SizedBox(height: 10.h),
              Divider(
                color: isDark ? cWhiteColor : cBlackColor,
                thickness: 1.3.h,
                indent: 30.w,
                endIndent: 30.w,
              ),
              const Spacer(flex: 2),
              numPad(_pinPutController, context, useBiometric, (){
                shouldLockStatic = false;
                widget.onAuthenticated();
              }),
              const Spacer(flex: 1),
            ],
          ),
        ),
      ),
    );
  }

  authenticate() async {
    print('Use biometric is: $useBiometric');
    try {
      final isAuthenticated = await LocalAuthApi.authenticate();
      print("Is authenticated: $isAuthenticated");
      if (isAuthenticated) {
        shouldLockStatic = false;
        widget.onAuthenticated();
      }
    } catch (e) {
      print(e.toString());
    }
  }
}