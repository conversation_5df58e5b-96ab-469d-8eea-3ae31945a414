import 'package:easy_localization/easy_localization.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:upgrader/upgrader.dart';

class MyCustomMessages extends UpgraderMessages {
  /// Override the message function to provide custom language localization.
  @override
  String? message(UpgraderMessage messageKey) {
    if (languageCode == 'en') {
      switch (messageKey) {
        case UpgraderMessage.body:
          return LocaleKeys.upGraderBody.tr();
        case UpgraderMessage.buttonTitleIgnore:
          return LocaleKeys.buttonTitleIgnore.tr();
        case UpgraderMessage.buttonTitleLater:
          return LocaleKeys.buttonTitleLater.tr();
        case UpgraderMessage.buttonTitleUpdate:
          return LocaleKeys.buttonTitleUpdate.tr();
        case UpgraderMessage.prompt:
          return LocaleKeys.prompt.tr();
        case UpgraderMessage.releaseNotes:
          return LocaleKeys.releaseNotes.tr();
        case UpgraderMessage.title:
          return LocaleKeys.title.tr();
      }
    }
    // Messages that are not provided above can still use the default values.
    return super.message(messageKey);
  }
}

