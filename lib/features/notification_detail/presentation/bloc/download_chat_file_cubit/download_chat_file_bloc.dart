import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:path_provider/path_provider.dart';

part 'download_chat_file_event.dart';

part 'download_chat_file_state.dart';

class DownloadChatFileBloc
    extends Bloc<DownloadChatFileEvent, DownloadChatFileState> {
  final NetworkInfo networkInfo;
  final Dio dio;

  DownloadChatFileBloc({required this.networkInfo, required this.dio})
      : super(DownloadChatFileState.initial()) {
    on<DownloadAndOpenChatFileEvent>(downloadAndOpenFile);
  }

  Future<void> downloadAndOpenFile(DownloadAndOpenChatFileEvent event,
      Emitter<DownloadChatFileState> emit) async {
    emit(state.copyWith(
        status: DownloadChatFileStatus.loading, messageId: event.messageId));
    if (await networkInfo.isConnected) {
      try {
        String? path = "";
        if (Platform.isIOS || Platform.isWindows) {
          final Directory? downloadsDir =
              await getApplicationDocumentsDirectory();
          path = downloadsDir?.path;
        } else if (Platform.isAndroid) {
          path = androidDownloadPath;
        }
        try {
          String localFilePath = "$path${event.fileUrl.split("/").last}";
          var response = await dio.download(event.fileUrl ?? "", localFilePath);
          if (response.statusCode == 200) {
            emit(state.copyWith(
                status: DownloadChatFileStatus.success,
                localPath: localFilePath,
                messageId: event.messageId));
          }
        } on DioException catch (e) {
          emit(state.copyWith(
              status: DownloadChatFileStatus.failure,
              message: LocaleKeys.error.tr()));
        }
      } catch (e) {
        emit(state.copyWith(
            status: DownloadChatFileStatus.failure,
            message: LocaleKeys.error.tr()));
      }
    } else {
      emit(state.copyWith(
          status: DownloadChatFileStatus.failure,
          message: LocaleKeys.error.tr()));
    }
  }
}
