part of 'download_chat_file_bloc.dart';

enum DownloadChatFileStatus {
  initial,
  loading,
  failure,
  success,
  noInternet,
}

class DownloadChatFileState extends Equatable {
  final DownloadChatFileStatus status;
  final String? localPath;
  final String? message;
  final String? messageId;

  DownloadChatFileState(
      {required this.status, this.localPath, this.message, this.messageId});

  static DownloadChatFileState initial() => DownloadChatFileState(
        status: DownloadChatFileStatus.initial,
      );

  DownloadChatFileState copyWith(
          {DownloadChatFileStatus? status,
          String? localPath,
          String? message,
          String? messageId}) =>
      DownloadChatFileState(
          status: status ?? this.status,
          localPath: localPath ?? this.localPath,
          message: message ?? this.message,
          messageId: messageId ?? this.messageId);

  @override
  List<Object?> get props => [status, message, messageId];
}
