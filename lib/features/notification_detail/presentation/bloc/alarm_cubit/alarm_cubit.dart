import 'package:bloc/bloc.dart';
import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:isar/isar.dart';
import 'package:meta/meta.dart';

part 'alarm_state.dart';

class AlarmCubit extends Cubit<AlarmState> {
  AlarmCubit({required this.isarService}) : super(AlarmInitial());
  final IsarService isarService;

  alarmText({required String id, required String type}) async {
    if (type == TASK) {
      var notifications = await isarService.isar.taskDocs
          .filter()
          .scheduledTimeIsNotNull()
          .taskIdEqualTo(id)
          .findAll();
      if (notifications.isEmpty) {
        emit(AlarmEmptyTime());
      } else {
        emit(AlarmPuttedTime(dateTime: notifications.first.scheduledTime));
      }
    } else {
      var notifications = await isarService.isar.notificationDocs
          .filter()
          .scheduledTimeIsNotNull()
          .notificationIdEqualTo(id)
          .findAll();
      if (notifications.isEmpty) {
        emit(AlarmEmptyTime());
      } else {
        emit(AlarmPuttedTime(dateTime: notifications.first.scheduledTime));
      }
    }
  }
}
