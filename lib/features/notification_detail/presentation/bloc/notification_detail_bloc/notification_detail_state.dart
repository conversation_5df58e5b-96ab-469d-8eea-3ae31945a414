part of 'notification_detail_bloc.dart';

enum NotificationDetailStatus {
  initial,
  loading,
  failure,
  success,
  noInternet,
}

class NotificationDetailState extends Equatable {
  final NotificationDetailStatus status;
  final String? message;

  NotificationDetailState({required this.status, this.message});

  static NotificationDetailState initial() => NotificationDetailState(
        status: NotificationDetailStatus.initial,
      );

  NotificationDetailState copyWith(
          {NotificationDetailStatus? status, String? message}) =>
      NotificationDetailState(
          status: status ?? this.status, message: message ?? this.message);

  @override
  List<Object?> get props => [status, message];
}
