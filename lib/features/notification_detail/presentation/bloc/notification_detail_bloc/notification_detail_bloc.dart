import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

import '../../../../../core/widgets/custom_toast.dart';

part 'notification_detail_event.dart';

part 'notification_detail_state.dart';

class NotificationDetailBloc
    extends Bloc<NotificationDetailEvent, NotificationDetailState> {
  final NetworkInfo networkInfo;
  final Dio dio;

  NotificationDetailBloc({required this.networkInfo, required this.dio})
      : super(NotificationDetailState.initial()) {
    on<ConfirmEvent>(confirmNotification);
  }

  confirmNotification(
      ConfirmEvent event, Emitter<NotificationDetailState> emit) async {
    if (await networkInfo.isConnected) {
      emit(state.copyWith(status: NotificationDetailStatus.loading));
      try {
        var response =
            await dio.get(updateNotificationPath + event.notificationId);
        if (response.statusCode == 200) {
          emit(state.copyWith(status: NotificationDetailStatus.success));
        } else {
          throw DioException(requestOptions: RequestOptions());
        }
      } on DioException catch (e) {
        CustomToast.showToast(e.response.toString());
        emit(state.copyWith(
            status: NotificationDetailStatus.failure,
            message: LocaleKeys.error.tr()));
      } catch (e) {
        emit(state.copyWith(
            status: NotificationDetailStatus.failure,
            message: LocaleKeys.error.tr()));
      }
    } else {
      CustomToast.showToast(LocaleKeys.no_internet.tr());
      emit(state.copyWith(
          status: NotificationDetailStatus.failure,
          message: LocaleKeys.no_internet.tr()));
    }
  }
}
