import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/download_chat_file_cubit/download_chat_file_bloc.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:open_filex/open_filex.dart';

class AppendixItems extends StatefulWidget {
  final List<Files>? items;

  const AppendixItems({Key? key, required this.items}) : super(key: key);

  @override
  State<AppendixItems> createState() => _AppendixItemsState();
}

class _AppendixItemsState extends State<AppendixItems> {
  final Dio dio = di();
  late bool isDark;
  late AndroidDeviceInfo? androidInfo;

  @override
  void initState() {
    super.initState();
    print(widget.items);
    if (Platform.isAndroid) {
      androidInfo = di();
    } else {
      androidInfo = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Column(
      children: [
        Column(
          children: widget.items?.map((e) {
                return InkWell(
                  onTap: () async {
                    try {
                      OpenFilex.open(
                          File(await getPath() + "/" + e.originalname).path);
                    } catch (e) {
                      print(e);
                    }
                  },
                  child:
                      BlocConsumer<DownloadChatFileBloc, DownloadChatFileState>(
                    listener: (context, state) {
                      if (state.status == DownloadChatFileStatus.success &&
                          e.path == state.messageId) {
                        try {
                          OpenFilex.open(File(state.localPath ?? '').path);
                        } catch (e) {
                          CustomToast.showToast(
                              LocaleKeys.error_open_file.tr());
                        }
                      } else if (state.status ==
                              DownloadChatFileStatus.failure &&
                          e.path == state.messageId) {
                        CustomToast.showToast(state.message ?? "");
                      }
                    },
                    builder: (context, state) {
                      return InkWell(
                        onTap: () {
                          BlocProvider.of<DownloadChatFileBloc>(context).add(
                              DownloadAndOpenChatFileEvent(
                                  fileUrl: e.path ?? "",
                                  messageId: e.path ?? ""));
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 22.h),
                          margin: EdgeInsets.symmetric(vertical: 4.h),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: themeIdentify(context)
                                ? cDateBackgroundDark.withAlpha(30)
                                : cGrayBackgroundColor,
                          ),
                          width: MediaQuery.of(context).size.width,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              SizedBox(
                                width: 20.w,
                              ),
                              state.status == DownloadChatFileStatus.loading
                                  ? e.path == state.messageId
                                      ? SizedBox(
                                          width: 20.h,
                                          height: 20.h,
                                          child: CircularProgressIndicator())
                                      : CircleAvatar(
                                          backgroundColor: cWhiteColor,
                                          child: SvgPicture.asset(
                                              Assets.iconsDocument))
                                  : CircleAvatar(
                                      backgroundColor: cWhiteColor,
                                      child: SvgPicture.asset(
                                          Assets.iconsDocument)),
                              SizedBox(
                                width: 20.w,
                              ),
                              Expanded(
                                flex: 8,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      e.originalname ?? "",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: themeIdentify(context)
                                              ? null
                                              : cBlackColor),
                                    ),
                                    Text(
                                      kbToMb(e.size ?? 0),
                                      style: TextStyle(
                                          color: cGrayTextColor,
                                          fontSize: 12.sp),
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 20.w,
                              ),
                              SizedBox(
                                width: 20.w,
                              )
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                );
              }).toList() ??
              [],
        ),
      ],
    );
  }
}
