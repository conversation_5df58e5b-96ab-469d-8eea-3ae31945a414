import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/upload_dialog_bloc/upload_dialog_cubit.dart';

class ProgressDialog extends StatefulWidget {
  const ProgressDialog({super.key});

  static Widget screen() {
    return BlocProvider(
      create: (context) => UploadDialogCubit(),
      child: ProgressDialog(),
    );
  }

  @override
  State<ProgressDialog> createState() => _ProgressDialogState();
}

class _ProgressDialogState extends State<ProgressDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: BlocBuilder<UploadDialogCubit, UploadDialogState>(
        builder: (context, state) {
          if (state is UpdateNumberState) {
            return Container(
              padding: EdgeInsets.all(10.w),
              width: MediaQuery.of(context).size.width * 075,
              height: 100,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(cRadius16.r)),
              child: Row(
                children: [
                  SizedBox(
                    width: 20.w,
                  ),
                  CircularProgressIndicator(),
                  SizedBox(
                    width: 20.w,
                  ),
                  Text("Yuklanmoqda..."),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Icon(Icons.close)),
                        Text("${state.number}/100")
                      ],
                    ),
                  )
                ],
              ),
            );
          } else {
            return Container(
              padding: EdgeInsets.all(10.w),
              width: MediaQuery.of(context).size.width * 075,
              height: 100,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(cRadius16.r)),
              child: Row(
                children: [
                  SizedBox(
                    width: 20.w,
                  ),
                  CircularProgressIndicator(),
                  SizedBox(
                    width: 20.w,
                  ),
                  Text("Yuklanmoqda..."),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Icon(Icons.close)),
                        Text("0/100")
                      ],
                    ),
                  )
                ],
              ),
            );
          }
        },
      ),
    );
  }
}
