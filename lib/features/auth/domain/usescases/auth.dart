import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/errors/failures.dart';
import 'package:ijrochi/core/usecases/usecase.dart';
import 'package:ijrochi/features/auth/domain/repositories/auth_repository.dart';

class AuthData extends UseCase<bool, AuthParams> {
  final AuthRepository authRepository;

  AuthData({required this.authRepository});

  @override
  Future<Either<Failure, bool>> call(AuthParams params) {
    return authRepository.sendAuth(
        params.phone,
        params.sms_code,
        params.token,
        params.android_version,
        params.device_name,
        params.android_id,
        params.firebase_token,
        params.platform);
  }
}

class AuthParams extends Equatable {
  final String phone;
  final String sms_code;
  final String token;
  final String android_version;
  final String device_name;
  final String android_id;
  final String firebase_token;
  final int platform;

  AuthParams(this.phone, this.sms_code, this.token, this.android_version,
      this.device_name, this.android_id, this.firebase_token, this.platform);

  @override
  List<Object> get props => [
        phone,
        sms_code,
        token,
        android_version,
        device_name,
        android_id,
        firebase_token,
      ];
}
