class AuthModel {
  int? id;
  String? name;
  String? phone;
  String? email;
  String? mobileToken;

  AuthModel({
    this.id,
    this.name,
    this.phone,
    this.email,
    this.mobileToken,
  });

  AuthModel.fromJson(dynamic json) {
    id = json['id'];
    name = json['name'];
    phone = json['phone'];
    email = json['email'];
    mobileToken = json['mobile_token'];
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['phone'] = phone;
    map['email'] = email;
    map['mobile_token'] = mobileToken;
    return map;
  }
}
