import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:ijrochi/core/errors/failures.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/features/auth/data/datasources/auth_local_datasources.dart';
import 'package:ijrochi/features/auth/data/datasources/auth_remote_datasources.dart';
import 'package:ijrochi/features/auth/domain/repositories/auth_repository.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class AuthRepositoryImpl extends AuthRepository {
  final AuthRemoteDataSourceImpl authRemoteDataSource;
  final AuthLocalDataSourcesImpl authLocalDataSources;
  final NetworkInfo networkInfo;

  AuthRepositoryImpl(
      {required this.authRemoteDataSource,
      required this.authLocalDataSources,
      required this.networkInfo});

  @override
  Future<Either<Failure, bool>> sendAuth(
      String phone,
      String sms_code,
      String token,
      String android_version,
      String device_name,
      String android_id,
      String firebase_token,
      int platform) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await authRemoteDataSource.setData(
            phone,
            sms_code,
            token,
            android_version,
            device_name,
            android_id,
            firebase_token,
            platform);

        if (result == "0") {
          return Right(false);
        } else if (result == "1") {
          return Left(ServerFailure(LocaleKeys.no_actual_code.tr()));
        } else if (result == "2") {
          return Left(ServerFailure(LocaleKeys.no_actual_code.tr()));
        } else if (result == "3") {
          return Left(ServerFailure("Server Failure"));
        } else {
          try {
            final resultLocal = await authLocalDataSources.setDataLocal(result);
            return Right(resultLocal);
          } on LocalFailure {
            return Left(LocalFailure(LocaleKeys.error.tr()));
          }
        }
      } on ServerFailure {
        return Left(ServerFailure("Server Failure"));
      } on InputFormatterFailure {
        return Left(InputFormatterFailure("Input Formatter error"));
      }
    } else {
      return Left(NoConnectionFailure(LocaleKeys.no_internet.tr()));
    }
  }
}
