import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:ijrochi/core/utils/api_path.dart';

import '../models/auth_model.dart';

abstract class AuthRemoteDataSource {
  Future<dynamic> setData(
      String phone,
      String sms_code,
      String token,
      String android_version,
      String device_name,
      String android_id,
      String firebase_token,
      int platform);
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final http.Client client;

  AuthRemoteDataSourceImpl({required this.client});

  @override
  Future<dynamic> setData(
      String phone,
      String sms_code,
      String token,
      String android_version,
      String device_name,
      String android_id,
      String firebase_token,
      int platform) async {
    try {
      var body = {
        "phone": phone,
        "sms_code": sms_code,
        "token": token,
        "android_version": android_version,
        "device_name": device_name,
        "android_id": android_id,
        "firebase_token": firebase_token,
        "platform": platform
      };
      final response = await client.post(Uri.parse(baseUrl + authPath),
          body: jsonEncode(body),
          headers: <String, String>{
            'Content-Type': 'application/json; charset=UTF-8',
            'Accept': 'application/json',
            'lang': 'ru',
          });
      print("Response====>" + response.body);
      if (response.statusCode == 200) {
        return AuthModel.fromJson(jsonDecode(response.body));
      } else if (response.statusCode == 400) {
        return "0";
      } else if (response.statusCode == 404) {
        return "1";
      } else if (response.statusCode == 505) {
        if (jsonDecode(response.body)['status'] == "error" &&
            jsonDecode(response.body)['msg'] == "code is invalid") {
          return "2";
        } else {
          return "3";
        }
      }
    } catch (e) {
      print('error------>$e');
    }
  }
}
