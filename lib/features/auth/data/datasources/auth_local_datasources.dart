import 'package:ijrochi/core/errors/failures.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/auth_model.dart';

abstract class AuthLocalDataSources {
  Future<bool> setDataLocal(AuthModel userModel);
}

class AuthLocalDataSourcesImpl implements AuthLocalDataSources {
  final SharedPreferences sharedPreferences;

  AuthLocalDataSourcesImpl({required this.sharedPreferences});

  @override
  Future<bool> setDataLocal(AuthModel user) async {
    try {
      ///Not used
      // sharedPreferences.setString("id", user.id.toString());
      // sharedPreferences.setString("name", user.name.toString());
      // sharedPreferences.setString("phone", user.phone.toString());
      // sharedPreferences.setString("email", user.email.toString());
      // sharedPreferences.setString("mobile_token", user.mobileToken.toString());
      return true;
    } on LocalFailure {
      return false;
    }
  }
}
