part of 'auth_bloc.dart';

abstract class AuthState extends Equatable {
  const AuthState();
}

class AuthInitial extends AuthState {
  @override
  List<Object> get props => [];
}

class AuthLoading extends AuthState {
  @override
  List<Object> get props => [];
}

class AuthNoInternet extends AuthState {
  @override
  List<Object> get props => [];
}

class AuthError extends AuthState {
  @override
  List<Object> get props => [];
}

class AuthFailure extends AuthState {
  final String message;

  AuthFailure(this.message);

  @override
  List<Object> get props => [message];
}

class AuthSuccessName extends AuthState {

  AuthSuccessName();

  @override
  List<Object> get props => [];
}

class AuthSuccessNoName extends AuthState {

  AuthSuccessNoName();

  @override
  List<Object> get props => [];
}

class AuthTimer extends AuthState {
  final String time;

  AuthTimer(this.time);

  @override
  List<Object> get props => [time];
}

class AuthTimerEnded extends AuthState{
  @override
  List<Object?> get props =>[];

}
