import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

part 'auth_event.dart';

part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final Dio dio;
  final NetworkInfo networkInfo;
  final GetStorage getStorage;

  AuthBloc(
      {required this.dio, required this.networkInfo, required this.getStorage})
      : super(AuthInitial()) {
    on<UpdateTimeEvent>(update, transformer: droppable());
    on<AuthTimeEndedEvent>(ended, transformer: droppable());
    on<SendAuthEvent>(_sendSms, transformer: droppable());
  }

  FutureOr<void> update(UpdateTimeEvent event, Emitter<AuthState> emit) async {
    emit(AuthTimer(event.time));
  }

  FutureOr<void> ended(
      AuthTimeEndedEvent event, Emitter<AuthState> emit) async {
    emit(AuthTimerEnded());
  }

  FutureOr<void> _sendSms(SendAuthEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    if (await networkInfo.isConnected) {
      try {
        var response = await dio.post(authPath, data: {
          "phone": event.phone,
          "macAddress": await getDeviceId(),
          "deviceName": await getDeviceName(),
          "deviceVersion": await getDeviceVersion(),
          "verifyCode": event.verifyCode
        });
        if (response.statusCode == 200) {
          Map<String, dynamic> decodedToken =
          JwtDecoder.decode(response.data['token']);
          var fullName = decodedToken['title']?['fullName'] ?? "";
          try {
            saveTokenAndData(
                response.data['token'], decodedToken, event.phone);
            if (fullName == '') {
              emit(AuthSuccessNoName());
            } else {
              emit(AuthSuccessName());
            }
          } catch (e) {
            emit(AuthFailure("Local database error!"));
          }
        } else {
          AuthFailure(LocaleKeys.error.tr());
        }
      } on DioException catch (e) {
        if (e.response?.statusCode == 404) {
          emit(AuthLoading());
          emit(AuthFailure(LocaleKeys.invalid_code.tr()));
        } else if (e.type == DioExceptionType.connectionTimeout) {
          emit(AuthLoading());
          emit(AuthFailure("Timeout exception!"));
        } else {
          emit(AuthLoading());
          emit(AuthFailure(LocaleKeys.error.tr()));
        }
      } catch (e) {
        emit(AuthLoading());
        emit(AuthFailure("Parsing error!"));
      }
    } else {
      emit(AuthNoInternet());
    }
  }

}