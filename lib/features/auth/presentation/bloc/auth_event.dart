part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();
}

class SendAuthEvent extends AuthEvent {
  final String phone;
  final String verifyCode;

  SendAuthEvent(
      {required this.phone,
      required this.verifyCode,});

  @override
  List<Object> get props =>
      [phone, verifyCode];
}

class UpdateTimeEvent extends AuthEvent {
  final String time;

  UpdateTimeEvent(this.time);

  @override
  List<Object> get props => [time];
}

class AuthTimeEndedEvent extends AuthEvent {
  @override
  List<Object> get props => [];
}
