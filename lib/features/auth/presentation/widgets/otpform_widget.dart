import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/resources/color_manager.dart';

class OtpForm extends StatelessWidget {
  final TextEditingController oneController;
  final TextEditingController twoController;
  final TextEditingController threeController;
  final TextEditingController fourController;
  final TextEditingController fiveController;
  final TextEditingController sixController;

  const OtpForm(
      {Key? key,
      required this.oneController,
      required this.twoController,
      required this.threeController,
      required this.fourController,
      required this.fiveController,
      required this.sixController})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Form(
        child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          color: ColorManager.lightGreyForOtpView,
          height: 56.h,
          width: 50.w,
          child: TextForm<PERSON>ield(
            controller: oneController,
            onChanged: (value) {
              if (value.length == 1) {
                FocusScope.of(context).nextFocus();
              }
            },
            decoration: InputDecoration(
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
              FilteringTextInputFormatter.digitsOnly
            ],
          ),
        ),
        Container(
          color: ColorManager.lightGreyForOtpView,
          height: 56.h,
          width: 50.w,
          child: TextFormField(
            controller: twoController,
            onChanged: (value) {
              if (value.length == 1) {
                FocusScope.of(context).nextFocus();
              }
            },
            decoration: InputDecoration(border: OutlineInputBorder()),
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
              FilteringTextInputFormatter.digitsOnly
            ],
          ),
        ),
        Container(
          color: ColorManager.lightGreyForOtpView,
          height: 56.h,
          width: 50.w,
          child: TextFormField(
            controller: threeController,
            onChanged: (value) {
              if (value.length == 1) {
                FocusScope.of(context).nextFocus();
              }
            },
            decoration: InputDecoration(border: OutlineInputBorder()),
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
              FilteringTextInputFormatter.digitsOnly
            ],
          ),
        ),
        Container(
          color: ColorManager.lightGreyForOtpView,
          height: 56.h,
          width: 50.w,
          child: TextFormField(
            controller: fourController,
            onChanged: (value) {
              if (value.length == 1) {
                FocusScope.of(context).nextFocus();
              }
            },
            decoration: InputDecoration(border: OutlineInputBorder()),
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
              FilteringTextInputFormatter.digitsOnly
            ],
          ),
        ),
        Container(
          color: ColorManager.lightGreyForOtpView,
          height: 56.h,
          width: 50.w,
          child: TextFormField(
            controller: fiveController,
            onChanged: (value) {
              if (value.length == 1) {
                FocusScope.of(context).nextFocus();
              }
            },
            decoration: InputDecoration(border: OutlineInputBorder()),
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
              FilteringTextInputFormatter.digitsOnly
            ],
          ),
        ),
        Container(
          color: ColorManager.lightGreyForOtpView,
          height: 56.h,
          width: 50.w,
          child: TextFormField(
            controller: sixController,
            onChanged: (value) {
              if (value.length == 1) {}
            },
            decoration: InputDecoration(border: OutlineInputBorder()),
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
              FilteringTextInputFormatter.digitsOnly
            ],
          ),
        ),
      ],
    ));
  }
}
