import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:equatable/equatable.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

part 'input_name_event.dart';

part 'input_name_state.dart';

class InputNameBloc extends Bloc<InputNameEvent, InputNameState> {
  final NetworkInfo networkInfo;
  final Dio dio;

  InputNameBloc({required this.networkInfo, required this.dio})
      : super(InputNameState.initial()) {
    on<AddNameEvent>(addName);
  }

  addName(AddNameEvent event, Emitter<InputNameState> emit) async {
    if (await networkInfo.isConnected) {
      emit(state.copyWith(status: InputNameStatus.loading));
      try {
        var response = await dio
            .get(setNamePath, queryParameters: {'fullName': event.fullName});
        if (response.statusCode == 200) {
          try {
            Map<String, dynamic> decodedToken =
                JwtDecoder.decode(response.data['token']);
            saveTokenAndData(
                response.data['token'], decodedToken, event.phone_number);
            emit(state.copyWith(status: InputNameStatus.success));
          } catch (e) {
            emit(state.copyWith(
                status: InputNameStatus.failure,
                message: LocaleKeys.error.tr()));
          }
        } else {
          emit(state.copyWith(
              status: InputNameStatus.failure,
              message: LocaleKeys.error.tr()));
        }
      } on DioException catch (e) {
        emit(state.copyWith(
            status: InputNameStatus.failure,
            message: LocaleKeys.error.tr()));
      } catch (e) {
        emit(state.copyWith(
            status: InputNameStatus.failure,
            message: LocaleKeys.error.tr()));
      }
    } else {
      emit(state.copyWith(
          status: InputNameStatus.noInternet,
          message: LocaleKeys.no_internet.tr()));
    }
  }
}
