part of 'input_name_bloc.dart';


enum InputNameStatus {
 initial,
 loading,
 failure,
 success,
 noInternet,
}

class InputNameState extends Equatable {
 final InputNameStatus status;
 final String? message;

 InputNameState({required this.status, this.message});

 static InputNameState initial() => InputNameState(
  status: InputNameStatus.initial,
 );

 InputNameState copyWith(
     {InputNameStatus? status, String? message}) =>
     InputNameState(
         status: status ?? this.status, message: message ?? this.message);

 @override
 List<Object?> get props => [status, message];
}
