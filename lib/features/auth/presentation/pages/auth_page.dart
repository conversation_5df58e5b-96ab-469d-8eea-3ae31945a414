import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/widgets/keyboard_dismissible_widget.dart';
import 'package:ijrochi/core/widgets/language_dialog.dart';
import 'package:ijrochi/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:ijrochi/features/auth/presentation/pages/input_name_page.dart';
import 'package:ijrochi/features/lock/presentation/pages/lock_page.dart';
import 'package:ijrochi/features/login/presentation/bloc/login_bloc.dart';
import 'package:ijrochi/features/payments/lock_switcher.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../../../../core/utils/app_constants.dart';
import '../../../../core/widgets/custom_toast.dart';
import '../../../../di/dependency_injection.dart';
import 'package:otp_autofill/otp_autofill.dart';
import 'package:pinput/pinput.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthPage extends StatefulWidget {
  final String tel;

  const AuthPage(this.tel, {Key? key}) : super(key: key);

  static Widget screen(String tel) => MultiBlocProvider(providers: [
        BlocProvider<AuthBloc>(create: (context) => di<AuthBloc>()),
        BlocProvider<LoginBloc>(create: (context) => di<LoginBloc>())
      ], child: AuthPage(tel));

  @override
  State<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage> {
  final SharedPreferences sharedPreferences = di();
  late AuthBloc _bloc;
  late LoginBloc _loginBloc;
  late Timer timer;
  late OTPTextEditController pinController;
  final focusNode = FocusNode();
  int numberCount = 0;
  var maskFormatter = MaskTextInputFormatter(mask: '######');

  String deviceName = 'unknown';
  String deviceVersion = 'unknown';

  ///style for otp entry
  final defaultPinTheme = PinTheme(
    width: 56.w,
    height: 46.h,
    textStyle: TextStyle(
        fontSize: 20.sp,
        color: Color.fromRGBO(30, 60, 87, 1),
        fontWeight: FontWeight.w600),
    decoration: BoxDecoration(
      border: Border.all(color: Color.fromRGBO(234, 239, 243, 1)),
      borderRadius: BorderRadius.circular(4.r),
    ),
  );

  ///style for otp entry

  @override
  void initState() {
    initSmsListener();
    _bloc = BlocProvider.of<AuthBloc>(context);
    _loginBloc = BlocProvider.of<LoginBloc>(context);
    startTimer(120);

    ///Tokens
    getTokenFromLocal();
    getFirebaseTokenLocal();

    ///Device names

    getDeviceName().then((value) => deviceName = value);
    getDeviceVersion().then((value) => deviceVersion = value);

    getSelectedLanguage();
    super.initState();
  }

  @override
  void dispose() {
    pinController.dispose();
    focusNode.dispose();
    _bloc.close();
    _loginBloc.close();
    timer.cancel();
    super.dispose();
  }

  String _verticalGroupValue = "O\'zbekcha";

  getSelectedLanguage() async {
    String lang = await sharedPreferences.getString(language_pref) ?? 'uz';
    switch (lang) {
      case 'uz':
        {
          _verticalGroupValue = 'O\'zbekcha';
          break;
        }
      case 'ru':
        {
          _verticalGroupValue = 'Русский';
          break;
        }
      case 'cr':
        {
          _verticalGroupValue = 'Ўзбекча';
          break;
        }
      default:
        {
          _verticalGroupValue = 'O\'zbekcha';
        }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: cWhiteColor,
      appBar: AppBar(
        elevation: 0,
        iconTheme: IconThemeData(
          color: cFirstColor, //change your color here
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              _verticalGroupValue,
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            SizedBox(
              width: 5.w,
            ),
            InkWell(
              onTap: () {
                showDialog(
                    context: context,
                    builder: (BuildContext context1) {
                      return LanguageDialog.screen(onSelectCallBack: () {
                        setState(() {
                          getSelectedLanguage();
                          dismissDialog(context1);
                        });
                      });
                    });
              },
              child: SvgPicture.asset(
                Assets.iconsIcLanguage,
                width: 24.w,
                height: 24.w,
                color: cFirstColor,
              ),
            )
          ],
        ),
      ),
      body: KeyboardDismissibleWidget(
        child: SingleChildScrollView(
          physics:
              AlwaysScrollableScrollPhysics(parent: BouncingScrollPhysics()),
          child: SafeArea(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Html(data: LocaleKeys.verification_code.tr(), style: {
                    "body": Style(
                        fontSize: FontSize(32.0), fontWeight: FontWeight.bold)
                  }),
                  SizedBox(
                    height: 30.h,
                  ),
                  Container(
                    height: 50.h,
                    child: TextField(
                      focusNode: focusNode,
                      decoration: InputDecoration(
                          isDense: true,
                          enabledBorder: OutlineInputBorder(
                              borderSide:
                                  BorderSide(width: 2, color: cFirstColor)),
                          border: OutlineInputBorder(
                              borderSide:
                                  BorderSide(width: 2, color: cFirstColor)),
                          focusedBorder: OutlineInputBorder(
                              borderSide:
                                  BorderSide(width: 2, color: cFirstColor)),
                          disabledBorder: OutlineInputBorder(
                              borderSide:
                                  BorderSide(width: 2, color: cFirstColor)),
                          label: Text(LocaleKeys.code.tr()),
                          prefixStyle: TextStyle(
                              color: cFirstColor,
                              fontSize: 16.sp,
                              fontFamily: medium),
                          labelStyle: TextStyle(color: cFirstColor)),
                      style: TextStyle(fontSize: 16.sp, fontFamily: medium),
                      inputFormatters: [maskFormatter],
                      keyboardType: TextInputType.phone,
                      controller: pinController,
                      onChanged: (value) {
                        setState(() {
                          numberCount = value.length;
                        });
                        if (numberCount == 6) {
                          _bloc.add(SendAuthEvent(
                            phone: widget.tel,
                            verifyCode: pinController.value.text,
                          ));
                        }
                      },
                    ),
                  ),
                  SizedBox(
                    height: 4.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        "${numberCount}/6",
                        style: TextStyle(
                            color: cFirstColor,
                            fontSize: 14.sp,
                            fontFamily: medium),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Container(
                    height: 40.h,
                    child: Center(
                      child: BlocBuilder<AuthBloc, AuthState>(
                        builder: (context, state) {
                          if (state is AuthLoading) {
                            return CircularProgressIndicator();
                          } else {
                            return Container();
                          }
                        },
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Center(
                    child: BlocConsumer<AuthBloc, AuthState>(
                      buildWhen: (prevoius, current) {
                        if (current is AuthLoading) {
                          return false;
                        } else if (current is AuthFailure) {
                          return false;
                        }
                        return true;
                      },
                      listener: (context, state) {
                        if (state is AuthSuccessName) {
                          Navigator.pushAndRemoveUntil(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => LockPage()),
                              (route) => false);
                        }
                        if (state is AuthSuccessNoName) {
                          Navigator.pushAndRemoveUntil(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => InputNamePage.screen(
                                      phone_number: widget.tel)),
                              (route) => false);
                        } else if (state is AuthFailure) {
                          WidgetsBinding.instance.addPostFrameCallback((time) {
                            Snack(state.message, context, cRedColor);
                          });
                        }
                      },
                      builder: (context, state) {
                        if (state is AuthTimer) {
                          return Text(
                            state.time,
                            style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: cGrayTextColor),
                          );
                        } else {
                          return SizedBox();
                        }
                      },
                    ),
                  ),
                  SizedBox(
                    height: 60,
                  ),
                  Center(
                    child: BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, state) {
                        if (state is AuthTimerEnded) {
                          return InkWell(
                              onTap: () {
                                _loginBloc.add(SendLoginEvent(tel: widget.tel));
                                startTimer(120);
                              },
                              child: Text(
                                LocaleKeys.resend_code.tr(),
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    fontFamily: medium,
                                    color: cFirstColor),
                              ));
                        } else {
                          return SizedBox();
                        }
                      },
                    ),
                  ),
                  SizedBox(
                    height: 60,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  initSmsListener() {
    try {
      pinController = OTPTextEditController(
          codeLength: 6,
          onCodeReceive: (code) async {
            _bloc.add(SendAuthEvent(phone: widget.tel, verifyCode: code));
          })
        ..startListenUserConsent((code) {
          final exp = RegExp(r'(\d{6})');
          return exp.stringMatch(code ?? '') ?? '';
        });
    } catch (e) {
      print("ERROR: ${e}");
    }
  }

  startTimer(int seconds) {
    var remainingSeconds = seconds;
    const duration = Duration(seconds: 1);
    timer = Timer.periodic(duration, (timer) {
      if (remainingSeconds == 0) {
        _bloc.add(AuthTimeEndedEvent());
        timer.cancel();
      } else {
        int minutes = remainingSeconds ~/ 60;
        int second = remainingSeconds % 60;
        var time = minutes.toString().padLeft(2, "0") +
            ":" +
            second.toString().padLeft(2, "0");
        remainingSeconds--;
        _bloc.add(UpdateTimeEvent(time));
      }
    });
  }
}
