import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/features/auth/presentation/name_bloc/input_name_bloc.dart';
import 'package:ijrochi/features/lock/presentation/pages/lock_page.dart';
import 'package:ijrochi/features/payments/lock_switcher.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

import '../../../../core/utils/app_constants.dart';
import '../../../../di/dependency_injection.dart';

class InputNamePage extends StatefulWidget {
  final String phone_number;

  const InputNamePage({Key? key, required this.phone_number}) : super(key: key);

  static Widget screen({required String phone_number}) {
    return BlocProvider(
      create: (context) => di<InputNameBloc>(),
      child: InputNamePage(
        phone_number: phone_number,
      ),
    );
  }

  @override
  State<InputNamePage> createState() => _InputNamePageState();
}

class _InputNamePageState extends State<InputNamePage> {
  final GetStorage gs = di();
  final Dio dio = di();
  final TextEditingController firstNameCont = TextEditingController();
  final TextEditingController lastNameCont = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        body: SingleChildScrollView(
          child: SafeArea(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              width: MediaQuery.of(context).size.width,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 100.h,
                  ),
                  Align(
                      child: Icon(
                    Icons.person,
                    size: 100.h,
                    color: cFirstColor,
                  )),
                  SizedBox(
                    height: 20.h,
                  ),
                  Align(
                    child: Text(LocaleKeys.enter_name.tr(),
                        style: TextStyle(
                            fontSize: 24.sp,
                            fontFamily: regular,
                            color: cBlackColor)),
                  ),
                  SizedBox(
                    height: 14.h,
                  ),
                  TextField(
                    decoration: InputDecoration(hintText: LocaleKeys.name.tr()),
                    controller: firstNameCont,
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  TextField(
                    decoration: InputDecoration(hintText: LocaleKeys.surname.tr()),
                    controller: lastNameCont,
                  ),
                  SizedBox(
                    height: 30.h,
                  ),
                  Row(
                    children: [
                      // Expanded(
                      //   child: MaterialButton(
                      //     onPressed: () async {
                      //       Navigator.pushAndRemoveUntil(
                      //           context,
                      //           MaterialPageRoute(
                      //               builder: (context) => LockPage()),
                      //               (route) => false);
                      //     },
                      //     child: FittedBox(
                      //       fit: BoxFit.scaleDown,
                      //       child: Text(
                      //         "O'tkazib yuborish",
                      //         style: TextStyle(
                      //           fontSize: 14.sp,
                      //           fontFamily: Assets.fontsNunitoSansRegular,
                      //         ),
                      //       ),
                      //     ),
                      //     color: cGrayColor,
                      //     height: 42.h,
                      //     textColor: Colors.white,
                      //     shape: RoundedRectangleBorder(
                      //         borderRadius: BorderRadius.circular(4.r)),
                      //   ),
                      // ),
                      // SizedBox(width: 20.w),
                      Expanded(
                        child: BlocConsumer<InputNameBloc, InputNameState>(
                          listener: (context, state) {
                            if (state.status == InputNameStatus.success) {
                              Navigator.pushAndRemoveUntil(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => LockPage()),
                                  (route) => false);
                            }
                          },
                          builder: (context, state) {
                            return MaterialButton(
                              onPressed: () async {
                                if (firstNameCont.text.isNotEmpty &&
                                    lastNameCont.text.isNotEmpty) {
                                  var firstName = firstNameCont.text;
                                  var lastName = lastNameCont.text;
                                  BlocProvider.of<InputNameBloc>(context).add(
                                      AddNameEvent(
                                          fullName: "$firstName $lastName",
                                          phone_number: widget.phone_number));
                                }
                              },
                              child: _button(state),
                              color: cFirstColor,
                              height: 42.h,
                              textColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.r)),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  Widget _button(InputNameState state) {
    if (state.status == InputNameStatus.loading) {
      return const CupertinoActivityIndicator(color: cWhiteColor);
    } else if (state.status == InputNameStatus.noInternet ||
        state.status == InputNameStatus.failure) {
      return Text(
        LocaleKeys.retry.tr(),
        style: TextStyle(
          fontSize: 14.sp,
          fontFamily: Assets.fontsNunitoSansRegular,
        ),
      );
    } else {
      return Text(
        LocaleKeys.save.tr(),
        style: TextStyle(
          fontSize: 14.sp,
          fontFamily: Assets.fontsNunitoSansRegular,
        ),
      );
    }
  }
}
