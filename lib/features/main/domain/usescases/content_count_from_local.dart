import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/errors/failures.dart';
import 'package:ijrochi/core/usecases/usecase.dart';
import 'package:ijrochi/features/main/data/model/content_count.dart';

import '../reposirories/main_repository.dart';

class ContentCountUsesCases
    extends UseCase<ContentCount, ContentCountLocalParam> {
  final MainRepository mainRepository;

  ContentCountUsesCases({required this.mainRepository});

  @override
  Future<Either<Failure, ContentCount>> call(ContentCountLocalParam params) {
    return mainRepository.getContentCountFromLocal();
  }
}

class ContentCountLocalParam extends Equatable {
  @override
  List<Object> get props => [];
}
