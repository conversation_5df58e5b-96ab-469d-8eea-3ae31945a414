import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/errors/failures.dart';
import 'package:ijrochi/core/usecases/usecase.dart';
import 'package:ijrochi/features/main/data/model/content_count_state_model.dart';
import 'package:ijrochi/features/main/domain/reposirories/main_repository.dart';

class MainUsesCases extends UseCase<ContentCountStateModel, MainParam> {
  final MainRepository mainRepository;

  MainUsesCases({required this.mainRepository});

  @override
  Future<Either<Failure, ContentCountStateModel>> call(MainParam params) {
    return mainRepository.getContentCount(params.refresh);
  }
}

class MainParam extends Equatable {
  final bool refresh;

  MainParam({required this.refresh});

  @override
  List<Object> get props => [refresh];
}
