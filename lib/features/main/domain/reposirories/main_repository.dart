import 'package:dartz/dartz.dart';
import 'package:ijrochi/core/errors/failures.dart';
import 'package:ijrochi/features/main/data/model/content_count.dart';
import 'package:ijrochi/features/main/data/model/content_count_state_model.dart';

abstract class MainRepository {
  Future<Either<Failure, ContentCountStateModel>> getContentCount(bool refresh);
  Future<Either<Failure,ContentCount>> getContentCountFromLocal();
}
