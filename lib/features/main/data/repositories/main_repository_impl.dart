import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:ijrochi/core/database/isar_service.dart';

import 'package:ijrochi/core/errors/failures.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/features/main/data/datasources/main_local_datasources.dart';
import 'package:ijrochi/features/main/data/datasources/main_remote_datasources.dart';

import 'package:ijrochi/features/main/data/model/content_count.dart';
import 'package:ijrochi/features/main/data/model/content_count_state_model.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

import '../../domain/reposirories/main_repository.dart';

class MainRepositoryImpl extends MainRepository {
  final MainRemoteDataSourcesImpl mainRemoteDataSourcesImpl;
  final MainLocalDataSourcesImpl mainLocalDataSourcesImpl;
  final NetworkInfo networkInfo;
  final IsarService isarService;

  MainRepositoryImpl(
      {required this.mainRemoteDataSourcesImpl,
      required this.mainLocalDataSourcesImpl,
      required this.networkInfo,
      required this.isarService});

  @override
  Future<Either<Failure, ContentCountStateModel>> getContentCount(
      bool refresh) async {
    var processCount =await getScheduledNotificationCount(all: true);
    if (await networkInfo.isConnected && refresh) {
      try {
        final result = await mainRemoteDataSourcesImpl.getContentCount();
        mainLocalDataSourcesImpl.setContentCountModel(result.contentCount);
          return Right(ContentCountStateModel(
              contentCount:
              calculateCount(result.contentCount,processCount),
              status_code: result.status_code));
      } on ServerFailure {
        return  Left(ServerFailure(LocaleKeys.error_in_load.tr()));
      }
    } else {
      try {
        final result = await mainLocalDataSourcesImpl.getContentCountModel();
        return Right(ContentCountStateModel(
            contentCount: calculateCount(result, processCount),
            status_code: "200"));
      } on LocalFailure {
        return  Left(ServerFailure(LocaleKeys.error_in_load.tr()));
      }
    }
  }

  @override
  Future<Either<Failure, ContentCount>> getContentCountFromLocal() async {

    var processCount =await getScheduledNotificationCount(all: true);

    try {
      final result = await mainLocalDataSourcesImpl.getContentCountModel();
      return Right(calculateCount(result, processCount));
    } on LocalFailure {
      return  Left(ServerFailure(LocaleKeys.error_in_load.tr()));
    }
  }

  ContentCount calculateCount(ContentCount contentCount, int processCount) {
    return ContentCount(
        taskDone: contentCount.taskDone ?? 0,
        taskError: contentCount.taskError ?? 0,
        taskNew: contentCount.taskNew ?? 0,
        taskProcess: contentCount.taskProcess ?? 0,
        notificationDone: contentCount.notificationDone,
        notificationNew: contentCount.notificationNew,
        notificationProcess: processCount);
  }
}
