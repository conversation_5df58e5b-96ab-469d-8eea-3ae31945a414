import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/features/main/data/model/content_count_state_model.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/utils/api_path.dart';
import '../model/content_count.dart';

abstract class MainRemoteDataSources {
  Future<ContentCountStateModel> getContentCount();
}

class MainRemoteDataSourcesImpl extends MainRemoteDataSources {
  final Dio dio;
  final GetStorage getStorage;

  MainRemoteDataSourcesImpl({required this.dio,required this.getStorage});

  @override
  Future<ContentCountStateModel> getContentCount() async {
    try {
      final response = await dio.get(contentCountPath);
      if (response.statusCode == 200) {
        return ContentCountStateModel(
            contentCount: ContentCount.fromJson(response.data),
            status_code: '200');
      } else if (response.statusCode == 505) {
        if (jsonDecode(response.data)['code'] == 401) {
          return ContentCountStateModel(
              contentCount: ContentCount(), status_code: '401');
        } else {
          return ContentCountStateModel(
              contentCount: ContentCount(), status_code: '0');
        }
      } else {
        return ContentCountStateModel(
            contentCount: ContentCount(), status_code: '0');
      }
    } on InputFormatterFailure {
      return ContentCountStateModel(
          contentCount: ContentCount(), status_code: '0');
    }
  }
}
