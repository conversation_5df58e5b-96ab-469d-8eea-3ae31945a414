import 'package:hive/hive.dart';

import '../../../../core/utils/app_constants.dart';
import '../model/content_count.dart';

abstract class MainLocalDataSources {
  Future<ContentCount> getContentCountModel();

  Future<bool> setContentCountModel(ContentCount contentCount);
}

class MainLocalDataSourcesImpl extends MainLocalDataSources {
  @override
  Future<ContentCount> getContentCountModel() async {
    try {
      final box = Hive.box(contentCountBox);
      final contentCountFromHive = box.get(contentCountBox);
      return contentCountFromHive;
    } catch (e) {
      return ContentCount(
          taskNew: 0,
          taskProcess: 0,
          taskDone: 0,
          taskError: 0,
          notificationNew: 0,
          notificationProcess: 0,
          notificationDone: 0);
    }
  }

  @override
  Future<bool> setContentCountModel(ContentCount contentCount) async {
    try {
      final box = Hive.box(contentCountBox);
      box.put(contentCountBox, contentCount);
      return true;
    } catch (e) {
      return false;
    }
  }
}
