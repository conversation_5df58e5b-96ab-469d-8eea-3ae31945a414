// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'content_count.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ContentCountAdapter extends TypeAdapter<ContentCount> {
  @override
  final int typeId = 1;

  @override
  ContentCount read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ContentCount(
      taskNew: fields[0] as int?,
      taskProcess: fields[1] as int?,
      taskDone: fields[2] as int?,
      taskError: fields[3] as int?,
      notificationNew: fields[4] as int?,
      notificationProcess: fields[5] as int?,
      notificationDone: fields[6] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, ContentCount obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.taskNew)
      ..writeByte(1)
      ..write(obj.taskProcess)
      ..writeByte(2)
      ..write(obj.taskDone)
      ..writeByte(3)
      ..write(obj.taskError)
      ..writeByte(4)
      ..write(obj.notificationNew)
      ..writeByte(5)
      ..write(obj.notificationProcess)
      ..writeByte(6)
      ..write(obj.notificationDone);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ContentCountAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
