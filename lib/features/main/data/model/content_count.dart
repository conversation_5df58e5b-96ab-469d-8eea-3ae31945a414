import 'package:hive/hive.dart';

part 'content_count.g.dart';

@HiveType(typeId: 1)
class ContentCount {
  @HiveField(0)
  int? taskNew;
  @HiveField(1)
  int? taskProcess;
  @HiveField(2)
  int? taskDone;
  @HiveField(3)
  int? taskError;
  @HiveField(4)
  int? notificationNew;
  @HiveField(5)
  int? notificationProcess;
  @HiveField(6)
  int? notificationDone;

  ContentCount({
    this.taskNew,
    this.taskProcess,
    this.taskDone,
    this.taskError,
    this.notificationNew,
    this.notificationProcess,
    this.notificationDone,
  });

  ContentCount.fromJson(dynamic json) {
    taskNew = json['newTasks'];
    taskProcess = json['onProcessTasks'];
    taskDone = json['doneTasks'];
    taskError = json['lateTasks'];
    notificationNew = json['newNotifications'];
    notificationProcess = json['notification_process'];
    notificationDone = json['archivedNotifications'];
  }

  ContentCount copyWith({
    int? taskNew,
    int? taskProcess,
    int? taskDone,
    int? taskError,
    int? notificationNew,
    int? notificationProcess,
    int? notificationDone,
  }) {
    return ContentCount(
      taskNew: taskNew ?? this.taskNew,
      taskProcess: taskProcess ?? this.taskProcess,
      taskDone: taskDone ?? this.taskDone,
      taskError: taskError ?? this.taskError,
      notificationNew: notificationNew ?? this.notificationNew,
      notificationProcess: notificationProcess ?? this.notificationProcess,
      notificationDone: notificationDone ?? this.notificationDone,
    );
  }
}
