import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/errors/failures.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/features/main/data/model/content_count.dart';
import 'package:ijrochi/features/main/data/model/content_count_state_model.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:meta/meta.dart';

import '../../../../core/network/network_info.dart';
import '../../../../di/dependency_injection.dart';
import '../../domain/usescases/content_count_from_local.dart';
import '../../domain/usescases/main_usecases.dart';

part 'main_event.dart';

part 'main_state.dart';

class MainBloc extends Bloc<MainEvent, MainState> {
  final MainUsesCases mainUsesCases;
  final ContentCountUsesCases contentCountUsesCases;
  final NetworkInfo networkInfo = di();

  MainBloc({required this.mainUsesCases, required this.contentCountUsesCases})
      : super(MainInitial(ContentCountStateModel(
            contentCount: ContentCount(
                taskNew: 0,
                taskProcess: 0,
                taskError: 0,
                taskDone: 0,
                notificationProcess: 0,
                notificationNew: 0,
                notificationDone: 0),
            status_code: ''))) {
    on<GetMainContentCountEvent>(getMainContentCount, transformer: droppable());
  }

  FutureOr<void> getMainContentCount(
      GetMainContentCountEvent event, Emitter<MainState> emit) async {
    updateFirebaseToken();

    late ContentCount contentCount;
    // var processCount =
    //     await isarService.isar.scheduledNotificationModels.where().findAll();
    final resultFromLocal =
        await contentCountUsesCases(ContentCountLocalParam());

    resultFromLocal.fold(
        (failure) => {
              emit(MainFailure(
                message: failure.message,
                contentCountStateModel: ContentCountStateModel(
                    contentCount: ContentCount(
                        taskNew: 0,
                        taskProcess: 0,
                        taskError: 0,
                        taskDone: 0,
                        notificationProcess: 0,
                        notificationNew: 0,
                        notificationDone: 0),
                    status_code: ""),
              ))
            },
        (r) => {
              contentCount = r,
              emit(MainLoading(
                  contentCountStateModel: ContentCountStateModel(
                      contentCount:
                          r.copyWith(notificationProcess:0),
                      status_code: '')))
            });

    if (await networkInfo.isConnected) {
      final result = await mainUsesCases(MainParam(refresh: event.refresh));
      result.fold(
          (failure) => {
                if (failure is NoConnectionFailure)
                  {
                    emit(NoInternetState(
                        failure.message,
                        ContentCountStateModel(
                            contentCount: contentCount, status_code: '')))
                  }
                else if (failure is ServerFailure)
                  {
                    emit(MainFailure(
                        message: failure.message,
                        contentCountStateModel: ContentCountStateModel(
                          contentCount: contentCount,
                          status_code: '',
                        )))
                  }
              },
          (r) => {
                if (r.status_code == "200")
                  {emit(MainSuccess(r))}
                else if (r.status_code == "401")
                  {

                    emit(MainFailure(
                        message: LocaleKeys.invalid_code.tr(),
                        contentCountStateModel: r))
                  }
                else{
                    emit(MainFailure(
                        message: LocaleKeys.server_error.tr(),
                        contentCountStateModel: r))
                  }
              });
    } else {
      emit(NoInternetState(
          LocaleKeys.no_internet.tr(),
          ContentCountStateModel(
              contentCount: contentCount, status_code: '')));
    }
  }
}
