part of 'main_bloc.dart';

@immutable
abstract class MainState extends Equatable {}

class MainInitial extends MainState {
  final ContentCountStateModel? contentCountStateModel;

  MainInitial(this.contentCountStateModel);

  @override
  List<Object?> get props => [];
}

class MainLoading extends MainState {
  final ContentCountStateModel? contentCountStateModel;

  MainLoading({this.contentCountStateModel});

  @override
  List<Object?> get props => [contentCountStateModel];
}

class MainSuccess extends MainState {
  final ContentCountStateModel contentCountStateModel;

  MainSuccess(this.contentCountStateModel);

  @override
  List<Object> get props => [contentCountStateModel];
}

class MainFailure extends MainState {
  final String message;
  final ContentCountStateModel contentCountStateModel;

  MainFailure({required this.message, required this.contentCountStateModel});

  @override
  List<Object> get props => [message, contentCountStateModel];
}

class MainInitialLoading extends MainState {
  final ContentCountStateModel contentCountStateModel;

  MainInitialLoading({required this.contentCountStateModel});

  @override
  List<Object> get props => [];
}

class NoInternetState extends MainState {
  final String message;
  final ContentCountStateModel contentCountStateModel;

  NoInternetState(this.message, this.contentCountStateModel);

  @override
  List<Object> get props => [message, contentCountStateModel];
}
