
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/utils/app_constants.dart';

class MainWorkWidget extends StatefulWidget {
  int? number;
  Color? numberBackground;
  String? icon;
  String? name;

  MainWorkWidget(this.number, this.numberBackground, this.icon, this.name);

  @override
  State<MainWorkWidget> createState() => _MainWorkWidgetState();
}

class _MainWorkWidgetState extends State<MainWorkWidget> {
  late bool isDark;

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            alignment: Alignment.topCenter,
            width: 70.w,
            height: 70.h,
            child: Stack(
              children: [
                Positioned(
                  top: 8.h,
                  left: 8.w,
                  child: CircleAvatar(
                    backgroundColor: isDark ? cFirstColorDark : cWhiteColor,
                    radius: 30.r,
                    child: SvgPicture.asset(
                      width: 22.w,
                      height: 22.h,
                      widget.icon.toString(),
                      color: isDark ? cWhiteColor : cMainIconColor,
                    ),
                  ),
                ),
                Positioned(
                    top: 0,
                    right: 5.w,
                    child: CircleAvatar(
                      radius: 15.r,
                      backgroundColor: widget.numberBackground,
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          notificationCountText(widget.number ?? 0),
                          style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white),
                        ),
                      ),
                    ))
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 0.w,top: 10.h),
            child: Text(
              widget.name.toString(),
              style: TextStyle(
                  color: Colors.white, fontFamily: regular, fontSize: 16.sp),
            ),
          )
        ],
      ),
    );

    // MainWorkRectWidget(
    //     widget.number, widget.numberBackground, widget.icon, widget.name)
  }

  String notificationCountText(int number) {
    if (number > 99) {
      return "99+";
    } else {
      return number.toString();
    }
  }
}
