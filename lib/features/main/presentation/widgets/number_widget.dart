import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';

import '../../../../core/utils/app_constants.dart';

class NumberWidget extends StatelessWidget {
  final int? number;

  const NumberWidget({Key? key, required this.number}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
      child: CircleAvatar(
        backgroundColor: themeIdentify(context)
            ? cBlueLight
            : cFirstColor,
        radius: 18.r,
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            notificationCountText(number??0),
            style: TextStyle(fontSize: 16.sp, color: cWhiteColor,fontFamily: regular),
          ),
        ),
      ),
    );
  }
  String notificationCountText(int number){
    if(number>99){
      return "99+";
    }
    else{
      return number.toString();
    }
  }
}
