import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/utils/app_constants.dart';

class MainWorkRectWidget extends StatefulWidget {
  int? number;
  Color? numberBackground;
  String? icon;
  String? name;

  MainWorkRectWidget(this.number, this.numberBackground, this.icon, this.name);

  @override
  State<MainWorkRectWidget> createState() => _MainWorkRectWidgetState();
}

class _MainWorkRectWidgetState extends State<MainWorkRectWidget> {
  late bool isDark;

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      height: 120.h,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 30.h),

                decoration: BoxDecoration(
                    color: isDark ? cFirstColorDark : cWhiteColor,
                    borderRadius: BorderRadius.all(Radius.circular(20.r))),
                child: SvgPicture.asset(
                  width: 22.w,
                  height: 22.h,
                  widget.icon.toString(),
                  color: isDark ? cWhiteColor : cMainIconColor,
                ),
              ),
              Positioned(
                  top: 8.h,
                  right: 5.w,
                  child: CircleAvatar(
                    radius: 18.r,
                    backgroundColor: widget.numberBackground,
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        notificationCountText(widget.number ?? 0),
                        style: TextStyle(
                            fontSize: 12.sp,
                            color: cWhiteColor,
                        fontFamily: regular),
                      ),
                    ),
                  ))
            ],
          ),
          Text(
            widget.name.toString(),
            style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w400,
                fontSize: 16.sp),
          )
        ],
      ),
    );
  }

  String notificationCountText(int number) {
    if (number > 99) {
      return "99+";
    } else {
      return number.toString();
    }
  }
}
