import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/anim/custom_page_route.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/home/<USER>/pages/home.dart';
import 'package:ijrochi/features/main/data/model/content_count_state_model.dart';
import 'package:ijrochi/features/main/presentation/bloc/main_bloc.dart';
import 'package:ijrochi/features/main/presentation/widgets/main_work_widget.dart';
import 'package:ijrochi/features/main/presentation/widgets/number_widget.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/pages/notification_parent_page.dart';
import 'package:ijrochi/features/tasks/task/presentation/archive_parent_page.dart';
import 'package:ijrochi/features/tasks/task/presentation/late_task_page.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/pages/task_parent_page.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class MainTaskWidget extends StatefulWidget {
  final ContentCountStateModel? contentCountStateModel;
  final bool loadingState;
  final VoidCallback callBack;

  const MainTaskWidget(
      {Key? key,
      required this.contentCountStateModel,
      required this.loadingState,
      required this.callBack})
      : super(key: key);

  @override
  State<MainTaskWidget> createState() => _MainTaskWidgetState();
}

class _MainTaskWidgetState extends State<MainTaskWidget> {
  Future _onlineRefresh() async {
    Future block = context.read<MainBloc>().stream.first;
    context.read<MainBloc>().add(GetMainContentCountEvent(refresh: true));
    await block;
    context.findAncestorStateOfType<State<HomePage>>()?.setState(() {});
  }

  late bool isDark = false;
  double _leftValue = 0;
  double _topValue = 0;

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
        statusBarColor: isDark ? cFirstColorDark : cWhiteColor));
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      decoration: const BoxDecoration(
          image: DecorationImage(
              image: AssetImage(
                Assets.imagesBackgroundPic,
              ),
              fit: BoxFit.cover)),
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          SizedBox(
            height: 70.h,
            child: Visibility(
              visible: widget.loadingState,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                      height: 20.h,
                      width: 20.w,
                      child: CircularProgressIndicator(
                        color: cWhiteColor,
                        strokeWidth: 2.w,
                      )),
                  SizedBox(
                    height: 10.h,
                  ),
                  Text(
                    LocaleKeys.loading.tr(),
                    style: TextStyle(color: cWhiteColor),
                  ),
                ],
              ),
            ),
          ),
          AnimatedPositioned(
            duration: Duration(milliseconds: 100),
            left: _leftValue,
            top: _topValue,
            child: GestureDetector(
              onPanEnd: (detail) => setState(() {
                if (_topValue > 0) {
                  _leftValue = 0;
                  _topValue = 0;
                }
              }),
              onPanUpdate: (detail) => setState(() {
                // _leftValue += detail.delta.dx;
                _topValue += detail.delta.dy;
                if (_topValue < -50.h) {
                  _topValue = -50.h;
                }
                if (_topValue > 200) {
                  widget.callBack();
                }
              }),
              child: Container(
                color: Colors.transparent,
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                child: Column(
                  children: [
                    SizedBox(
                      height: 70.h,
                    ),
                    Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              LocaleKeys.notification.tr(),
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white),
                            ),
                          ],
                          mainAxisAlignment: MainAxisAlignment.center,
                        ),
                        SizedBox(
                          height: 16.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            InkWell(
                              child: MainWorkWidget(
                                  widget.contentCountStateModel?.contentCount
                                      .notificationNew,
                                  cPinkColor,
                                  Assets.iconsNewIcon,
                                  LocaleKeys.task_new.tr()),
                              onTap: () {
                                Navigator.push(
                                    context,
                                    CustomPageRoute(
                                        child: NotificationParentPage.screen(
                                      categoryName: "NEW",
                                      title: LocaleKeys.notifications.tr(),
                                      archive: false,
                                    ))).then((value) {
                                  _onlineRefresh();
                                });
                              },
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    CustomPageRoute(
                                        child: NotificationParentPage.screen(
                                      categoryName: "PROCESS",
                                      title:
                                          LocaleKeys.notifications_active.tr(),
                                      archive: null,
                                    ))).then((value) {
                                  _onlineRefresh();
                                });
                              },
                              child: MainWorkWidget(
                                  widget.contentCountStateModel?.contentCount
                                      .notificationProcess,
                                  themeIdentify(context)
                                      ? cBlueLight
                                      : cFirstColor,
                                  Assets.iconsBell,
                                  LocaleKeys.active.tr()),
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    CustomPageRoute(
                                        child: NotificationParentPage.screen(
                                      categoryName: "DONE",
                                      title:
                                          LocaleKeys.archive_notifications.tr(),
                                      archive: true,
                                    ))).then((value) {
                                  _onlineRefresh();
                                  ;
                                });
                              },
                              child: MainWorkWidget(
                                  widget.contentCountStateModel?.contentCount
                                      .notificationDone,
                                  themeIdentify(context)
                                      ? cBlueLight
                                      : cFirstColor,
                                  Assets.iconsArchives,
                                  LocaleKeys.archive.tr()),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 45.h,
                    ),
                    Expanded(
                      flex: 8,
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(25.r),
                              topRight: Radius.circular(25.r)),
                          color: Theme.of(context).brightness == Brightness.dark
                              ? cFirstColorDark
                              : cWhiteColor,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(top: 24.h, left: 20.w),
                              child: Text(LocaleKeys.tasks.tr(),
                                  style: TextStyle(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w500)),
                            ),
                            SizedBox(
                              height: 30.h,
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Column(
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          Navigator.push(
                                                  context,
                                                  CustomPageRoute(
                                                      child: TaskParentPage.screen(
                                                          title: LocaleKeys
                                                              .task_new
                                                              .tr(),
                                                          status:
                                                              TaskCountEnumStatus
                                                                  .news)))
                                              .then(
                                                  (value) => _onlineRefresh());
                                        },
                                        child: Card(
                                          elevation: 4,
                                          child: Container(
                                            height: 171.h,
                                            width: 160.w,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              color: isDark
                                                  ? cCardColorDark
                                                  : cWhiteColor,
                                            ),
                                            child: Stack(
                                              alignment: Alignment.center,
                                              children: [
                                                Positioned(
                                                  top: 30.h,
                                                  child: SvgPicture.asset(
                                                    Assets
                                                        .iconsIcTaskNewIllustriation,
                                                    height: 100.h,
                                                  ),
                                                ),
                                                Positioned(
                                                    top: 10.h,
                                                    right: 15.w,
                                                    child: NumberWidget(
                                                        number: widget
                                                            .contentCountStateModel
                                                            ?.contentCount
                                                            .taskNew)),
                                                Positioned(
                                                    bottom: 15,
                                                    child: Text(
                                                      LocaleKeys.task_new.tr(),
                                                      style: TextStyle(
                                                          color: themeIdentify(
                                                                  context)
                                                              ? cWhiteColor
                                                              : cBlackColor,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          fontSize: 15.sp),
                                                    )),
                                                Positioned(
                                                    left: 0,
                                                    child: Container(
                                                      height: 171.h,
                                                      width: 10.w,
                                                      decoration: BoxDecoration(
                                                          color: themeIdentify(
                                                                  context)
                                                              ? cBlueLight
                                                              : cFirstColor,
                                                          borderRadius:
                                                              BorderRadius.only(
                                                                  topLeft: Radius
                                                                      .circular(
                                                                          10),
                                                                  bottomLeft: Radius
                                                                      .circular(
                                                                          10))),
                                                    )),
                                              ],
                                            ),
                                          ),
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      InkWell(
                                        onTap: () {
                                          Navigator.push(
                                                  context,
                                                  CustomPageRoute(
                                                      child: LateTaskPage.screen()))
                                              .then(
                                                  (value) => _onlineRefresh());
                                        },
                                        child: Card(
                                          elevation: 4,
                                          child: Container(
                                            height: 150.h,
                                            width: 160.w,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              color: isDark
                                                  ? cCardColorDark
                                                  : cWhiteColor,
                                            ),
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceAround,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  widget
                                                          .contentCountStateModel
                                                          ?.contentCount
                                                          .taskError
                                                          .toString() ??
                                                      "0",
                                                  style: TextStyle(
                                                      fontSize: 48.sp,
                                                      color:
                                                          themeIdentify(context)
                                                              ? cBlueLight
                                                              : cFirstColor,
                                                      fontWeight:
                                                          FontWeight.w800),
                                                ),
                                                Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 20.w),
                                                  child: Text(
                                                    textAlign: TextAlign.center,
                                                    LocaleKeys.task_stopped
                                                        .tr(),
                                                    style: TextStyle(
                                                        color: themeIdentify(
                                                                context)
                                                            ? cWhiteColor
                                                            : cBlackColor,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: 15.sp),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                        ),
                                      )
                                    ],
                                  ),
                                  SizedBox(
                                    width: 6.w,
                                  ),
                                  Column(
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          Navigator.push(
                                                  context,
                                                  CustomPageRoute(
                                                      child: TaskParentPage.screen(
                                                          title: LocaleKeys
                                                              .task_process
                                                              .tr(),
                                                          status:
                                                              TaskCountEnumStatus.progress)))
                                              .then(
                                                  (value) => _onlineRefresh());
                                        },
                                        child: Card(
                                          elevation: 4,
                                          child: Container(
                                            height: 150.h,
                                            width: 160.w,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              color: isDark
                                                  ? cCardColorDark
                                                  : cWhiteColor,
                                            ),
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceAround,
                                              children: [
                                                Text(
                                                  widget
                                                          .contentCountStateModel
                                                          ?.contentCount
                                                          .taskProcess
                                                          .toString() ??
                                                      "0",
                                                  style: TextStyle(
                                                      fontSize: 48.sp,
                                                      color:
                                                          themeIdentify(context)
                                                              ? cBlueLight
                                                              : cFirstColor,
                                                      fontWeight:
                                                          FontWeight.w800),
                                                ),
                                                Text(
                                                  LocaleKeys.task_process.tr(),
                                                  style: TextStyle(
                                                      color:
                                                          themeIdentify(context)
                                                              ? cWhiteColor
                                                              : cBlackColor,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: 15.sp),
                                                )
                                              ],
                                            ),
                                          ),
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      InkWell(
                                        onTap: () {
                                          Navigator.push(
                                                  context,
                                                  CustomPageRoute(
                                                      child: ArchiveParentTaskPage.screen()))
                                              .then(
                                                  (value) => _onlineRefresh());
                                        },
                                        child: Card(
                                          elevation: 4,
                                          child: Container(
                                            height: 171.h,
                                            width: 160.w,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              color: isDark
                                                  ? cCardColorDark
                                                  : cWhiteColor,
                                            ),
                                            child: Stack(
                                              alignment: Alignment.center,
                                              children: [
                                                Positioned(
                                                  top: 40.h,
                                                  child: SvgPicture.asset(
                                                    Assets.iconsIcContacts,
                                                    width: 80.w,
                                                    height: 60.h,
                                                  ),
                                                ),
                                                Positioned(
                                                    right: 0,
                                                    child: Container(
                                                      height: 171.h,
                                                      width: 10.w,
                                                      decoration: BoxDecoration(
                                                          color:
                                                              themeIdentify(
                                                                      context)
                                                                  ? cBlueLight
                                                                  : cFirstColor,
                                                          borderRadius:
                                                              BorderRadius.only(
                                                                  topRight: Radius
                                                                      .circular(
                                                                          10),
                                                                  bottomRight: Radius
                                                                      .circular(
                                                                          10))),
                                                    )),
                                                Positioned(
                                                    top: 10.h,
                                                    right: 25.w,
                                                    child: NumberWidget(
                                                        number: widget
                                                            .contentCountStateModel
                                                            ?.contentCount
                                                            .taskDone)),
                                                Positioned(
                                                    bottom: 15.h,
                                                    child: Text(
                                                      LocaleKeys.archive.tr(),
                                                      style: TextStyle(
                                                          color: themeIdentify(
                                                                  context)
                                                              ? cWhiteColor
                                                              : cBlackColor,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          fontSize: 15.sp),
                                                    ))
                                              ],
                                            ),
                                          ),
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
