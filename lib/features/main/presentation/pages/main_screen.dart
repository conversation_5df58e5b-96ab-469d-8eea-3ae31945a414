import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/features/home/<USER>/pages/home.dart';
import 'package:ijrochi/features/main/presentation/bloc/main_bloc.dart';
import 'package:ijrochi/features/main/presentation/widgets/main_task_widget.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../di/dependency_injection.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({Key? key}) : super(key: key);

  static Widget screen() => BlocProvider<MainBloc>(
        create: (context) => di<MainBloc>(),
        child: const MainScreen(),
      );

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  late bool isDark;
  late MainBloc _bloc;
  final Dio dio = di.get();
  final SharedPreferences prefs = di.get();
  int buildCount = 0;

  @override
  void initState() {
    _bloc = BlocProvider.of<MainBloc>(context);
    _handleRefresh(true);
    getModerator();
    super.initState();
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  Future _handleRefresh(bool refresh) async {
    _bloc.add(GetMainContentCountEvent(refresh: refresh));
  }

  Future _onlineRefresh() async {
    Future block = context.read<MainBloc>().stream.first;
    context.read<MainBloc>().add(GetMainContentCountEvent(refresh: true));
    await block;
    context.findAncestorStateOfType<State<HomePage>>()?.setState(() {});

  }

  requestPermision() async {
    var status = await Permission.storage.status;
    if (!status.isGranted) {
      await Permission.storage.request();
    }
  }

  writeFileIOS() async {
    var a = await getApplicationDocumentsDirectory();
    var path = a.path ?? '';

    final file = File(path + "/file.txt");

    if (await file.exists()) {
      await file.delete(recursive: true);
    }

    // Create the file if it doesn't exist
    if (!await file.exists()) {
      await file.create(recursive: true);
    }
    // Write content to the file
    await file.writeAsString('Hello world!');
  }

  FutureOr<String> readFileIOS() async {
    var a = await getApplicationDocumentsDirectory();
    var path = a.path ?? '';

    try {
      final file = File(path + "/file.txt");

      print('Reding from: ' + file.path);

      // Check if the file exists
      if (await file.exists()) {
        // Read the file contents
        final contents = await file.readAsString();
        print(contents);
        return contents;
      } else {
        return 'File not found';
      }
    } catch (e) {
      return 'Error reading file: $e';
    }
  }

  @override
  Widget build(BuildContext context) {
    print("Main build");

    isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      //make appBar transparent
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: cWhiteColor),
        automaticallyImplyLeading: false,
      ),
      resizeToAvoidBottomInset: true,
      body: RefreshIndicator(
        onRefresh: () async {
          _onlineRefresh();
        },
        child: ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(
            dragDevices: {
              PointerDeviceKind.touch,
              PointerDeviceKind.mouse,
            },
          ),
          child: BlocConsumer<MainBloc, MainState>(
            listener: (context, state) {
              if (state is MainFailure) {
                if (state.contentCountStateModel.status_code == "401") {
                  WidgetsBinding.instance.addPostFrameCallback((time) async {
                    await clearAndLogout(context);
                  });
                } else {
                  WidgetsBinding.instance.addPostFrameCallback((time) {
                    Snack(state.message, context, cRedColor);
                  });
                }
              } else if (state is NoInternetState) {
                CustomToast.showToast(state.message);
              }
            },
            builder: (context, state) {
              if (state is MainInitial) {
                return MainTaskWidget(
                  contentCountStateModel: state.contentCountStateModel,
                  loadingState: false,
                  callBack: () {
                    _handleRefresh(true);
                  },
                );
              } else if (state is MainLoading) {
                return MainTaskWidget(
                  contentCountStateModel: state.contentCountStateModel,
                  loadingState: true,
                  callBack: () {
                    _handleRefresh(true);
                  },
                );
              } else if (state is MainSuccess) {
                return MainTaskWidget(
                  contentCountStateModel: state.contentCountStateModel,
                  loadingState: false,
                  callBack: () {
                    _handleRefresh(true);
                  },
                );
              } else if (state is MainFailure) {
                return MainTaskWidget(
                  contentCountStateModel: state.contentCountStateModel,
                  loadingState: false,
                  callBack: () {
                    _handleRefresh(true);
                  },
                );
              } else if (state is NoInternetState) {
                return MainTaskWidget(
                  contentCountStateModel: state.contentCountStateModel,
                  loadingState: false,
                  callBack: () {
                    _handleRefresh(true);
                  },
                );
              } else {
                return Expanded(
                  child: Container(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ClipRect(
                            child: Container(
                              height: 300.h,
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Expanded(
                                      child: Image.asset(
                                    Assets.imagesNoConnection,
                                    height: 140.h,
                                  )),
                                  Padding(
                                      padding: EdgeInsets.only(
                                          top: 10.h,
                                          left: 30.w,
                                          right: 30.w,
                                          bottom: 10.h),
                                      child: Text(
                                        LocaleKeys.no_internet.tr(),
                                        textAlign: TextAlign.center,
                                        style: TextStyle(color: cGrayColor),
                                      )),
                                  CupertinoButton(
                                      child: Text(
                                        LocaleKeys.reload.tr(),
                                        style: TextStyle(color: cGrayColor),
                                      ),
                                      color: cGrayColor.withAlpha(80),
                                      onPressed: () {
                                        _handleRefresh(true);
                                      }),
                                  SizedBox(
                                    height: 20.h,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }
}
