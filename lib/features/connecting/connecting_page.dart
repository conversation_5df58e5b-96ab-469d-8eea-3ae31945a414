import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

class ConnectingPage extends StatefulWidget {
  const ConnectingPage({super.key});

  @override
  State<ConnectingPage> createState() => _ConnectingPageState();
}

class _ConnectingPageState extends State<ConnectingPage> {
  @override
  Widget build(BuildContext context) {
    TextTheme _textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor:
          themeIdentify(context) ? cBackgroundColor : cGrayColor.withAlpha(30),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.h),
            width: MediaQuery.of(context).size.width,
            height: 50.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  LocaleKeys.connect.tr(),
                  style:
                      TextStyle(fontWeight: FontWeight.w600, fontSize: 16.sp),
                ),
                SizedBox(
                  width: 90.w,
                ),
                Container(
                  padding: const EdgeInsets.all(5.0),
                  margin:
                      EdgeInsets.symmetric(vertical: 12.h, horizontal: 15.w),
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(4.r)),
                )
              ],
            ),
            color: themeIdentify(context) ? cFirstColorDark : cWhiteColor,
          ),
          SizedBox(
            height: 8.h,
          ),
          InkWell(
            onTap: () async {
              String tgUsername = TELEGRAM;
              var uri = Uri.parse(tgUsername);
              if (await canLaunchUrl(uri)) {
                launchUrl(uri);
              } else {
                CustomToast.showToast('This action is not supported');
              }
            },
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: 56.h,
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Telegram',
                    style:
                        TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  Text(
                    '@ijrochi_uz',
                    style: _textTheme.displayMedium!
                        .copyWith(fontSize: 14.sp, color: Colors.grey),
                  )
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w,vertical: 2.h),
            child: Container(
              height: 0.4.h,
              color: themeIdentify(context) ? cWhiteColor : cBackDarkColor2,
            ),
          ),
          InkWell(
            onTap: () async {
              String? encodeQueryParameters(Map<String, String> params) {
                return params.entries
                    .map((MapEntry<String, String> e) =>
                        '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
                    .join('&');
              }

              final Uri emailUri = Uri(
                scheme: 'mailto',
                path: EMAIL,
              );
              if (await canLaunchUrl(emailUri)) {
                launchUrl(emailUri);
              } else {
                CustomToast.showToast('This action is not supported');
              }
            },
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: EdgeInsets.symmetric(horizontal: 20.w,vertical: 2.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'E-mail',
                    style:
                        TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  Text(
                    '<EMAIL>',
                    style: TextStyle(fontSize: 14.sp, color: Colors.grey),
                  )
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w,vertical: 2.h),
            child: Container(
              height: 0.4.h,
              color: themeIdentify(context) ? cWhiteColor : cBackDarkColor2,
            ),
          ),
          InkWell(
            onTap: () async {
              final Uri launchUri = Uri(scheme: 'tel', path: SUPPORT_TEL);
              if (await canLaunchUrl(launchUri)) {
                await launchUrl(launchUri);
              } else {
                CustomToast.showToast('This action is not supported');
              }
            },
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: EdgeInsets.symmetric(horizontal: 20.w,vertical: 2.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.telephone.tr(),
                    style:
                        TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  Text(
                    '+999(73) 244-05-35',
                    style: TextStyle(fontSize: 14.sp, color: Colors.grey),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
