import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:equatable/equatable.dart';

import '../../../core/widgets/custom_toast.dart';

part 'task_detail_event.dart';

part 'task_detail_state.dart';

class TaskDetailBloc extends Bloc<TaskDetailEvent, TaskDetailState> {
  final NetworkInfo networkInfo;
  final Dio dio;

  TaskDetailBloc({required this.networkInfo, required this.dio})
      : super(TaskDetailState.initial()) {
    on<ConfirmEvent>(confirmTask);
  }

  confirmTask(ConfirmEvent event, Emitter<TaskDetailState> emit) async {
    if (await networkInfo.isConnected) {
      emit(state.copyWith(status: TaskDetailStatus.loading));
      try {
        var response = await dio.get(updateTaskPath + event.taskId);
        if (response.statusCode == 200) {
          emit(state.copyWith(status: TaskDetailStatus.success));
        } else {
          throw DioException(requestOptions: RequestOptions());
        }
      } on DioException catch (e) {
        CustomToast.showToast(e.response.toString());
        emit(state.copyWith(
            status: TaskDetailStatus.failure,
            message: LocaleKeys.error.tr()));
      } catch (e) {
        emit(state.copyWith(
            status: TaskDetailStatus.failure,
            message: LocaleKeys.error.tr()));
      }
    } else {
      CustomToast.showToast(LocaleKeys.no_internet.tr());
      emit(state.copyWith(
          status: TaskDetailStatus.failure,
          message: LocaleKeys.no_internet.tr()));
    }
  }
}
