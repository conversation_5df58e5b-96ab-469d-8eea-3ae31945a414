part of 'task_detail_bloc.dart';


enum TaskDetailStatus {
 initial,
 loading,
 failure,
 success,
 noInternet,
}

class TaskDetailState extends Equatable {
 final TaskDetailStatus status;
 final String? message;

 TaskDetailState({required this.status, this.message});

 static TaskDetailState initial() => TaskDetailState(
  status: TaskDetailStatus.initial,
 );

 TaskDetailState copyWith(
     {TaskDetailStatus? status, String? message}) =>
     TaskDetailState(
         status: status ?? this.status, message: message ?? this.message);

 @override
 List<Object?> get props => [status, message];
}
