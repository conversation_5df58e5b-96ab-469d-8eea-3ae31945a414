import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_storage/get_storage.dart';
import 'package:html/parser.dart';
import 'package:http_parser/http_parser.dart';
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/functions/date_picker.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/network/stream_socket.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/core/widgets/keyboard_dismissible_widget.dart';
import 'package:ijrochi/core/widgets/permission_dialog.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/home/<USER>/pages/home.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/alarm_cubit/alarm_cubit.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/download_chat_file_cubit/download_chat_file_bloc.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/upload_dialog_bloc/upload_dialog_cubit.dart';
import 'package:ijrochi/features/notification_detail/widgets/appendix_items.dart';
import 'package:ijrochi/features/notification_detail/widgets/progress_dialog.dart';
import 'package:ijrochi/features/task_detail/bloc/task_detail_bloc.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/push_notifications/notification_lock.dart';
import 'package:ijrochi/push_notifications/notification_service.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:mime/mime.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:socket_io_client/socket_io_client.dart';
import 'package:path/path.dart' as path;

class TaskDetailPage extends StatefulWidget {
  final TaskDocs taskDocs;
  final TaskCountEnumStatus? status;
  final String? pushKey;
  final bool shouldLock;

  static Widget screen(
      {required TaskDocs taskDocs,
      TaskCountEnumStatus? status,
      bool shouldLock = false,
      String? pushKey}) {
    return MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => di<TaskDetailBloc>()),
          BlocProvider(create: (context) => di<AlarmCubit>()),
          BlocProvider(create: (context) => di<DownloadChatFileBloc>()),
          BlocProvider(create: (context) => UploadDialogCubit()),
        ],
        child: TaskDetailPage(
            taskDocs: taskDocs,
            status: status,
            shouldLock: shouldLock,
            pushKey: pushKey));
  }

  const TaskDetailPage(
      {super.key,
      required this.taskDocs,
      required this.status,
      required this.shouldLock,
      this.pushKey});

  @override
  State<TaskDetailPage> createState() => _TaskDetailPageState();
}

class _TaskDetailPageState extends State<TaskDetailPage> {
  final DateFormat formatterHour = DateFormat('HH:mm');
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  final DateFormat formatterCommentDate = DateFormat('dd.MM.yyyy HH:mm');
  StreamSocket streamSocket = StreamSocket();
  List<Map<String, dynamic>> chats = [];
  late bool isDark;
  bool? confirm = false;
  bool isTyped = true;
  late Socket socket;
  final ScrollController _sc = ScrollController();
  final ScrollController _sc_chat = ScrollController();
  TextEditingController chatController = TextEditingController();
  late AndroidDeviceInfo? androidInfo;
  final GetStorage getStorage = di();
  bool shouldLock = false;

  static bool canPop(BuildContext context) {
    final NavigatorState? navigator = Navigator.maybeOf(context);
    return navigator != null && navigator.canPop();
  }

  int duration1(DateTime startDate, DateTime endDate) {
    Duration difference = endDate.difference(startDate);
    return difference.inDays;
  }

  int duration2(DateTime endDate) {
    Duration difference2 = endDate.difference(DateTime.now());
    int lastCount = difference2.inDays;
    return lastCount;
  }

  void scrollDown() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _sc.animateTo(_sc.position.maxScrollExtent,
          duration: const Duration(milliseconds: 200),
          curve: Curves.fastOutSlowIn);
      _sc_chat.animateTo(_sc_chat.position.maxScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.fastOutSlowIn);
    });
  }

  @override
  void dispose() {
    socket.disconnect();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    shouldLock = widget.shouldLock;

    BlocProvider.of<AlarmCubit>(context)
        .alarmText(id: widget.taskDocs.taskId ?? "", type: TASK);
    connectAndListen();
    if (Platform.isAndroid) {
      androidInfo = di();
    } else {
      androidInfo = null;
    }
    if (widget.pushKey == ACCEPT) {
      BlocProvider.of<TaskDetailBloc>(context)
          .add(ConfirmEvent(taskId: widget.taskDocs.taskId ?? ""));
    }
  }

  connectAndListen() {
    try {
      var socketLog = io(
          socketUrl,
          OptionBuilder()
              .setAuth({"token": getStorage.read(BEARER_TOKEN)})
              .setTransports(['websocket'])
              .disableAutoConnect()
              .build());

      socketLog.dispose();
      socket = socketLog;
      socket.connect();
      socket.onConnect((_) {
        chats.clear();
        socket.emit("message", {"id": widget.taskDocs.taskId, "type": "task"});
      });
      socket.on("chats", (data) {
        streamSocket.addResponse(data);
        if (data?.containsKey("chats") == true) {
          List<dynamic>? rawList = data?['chats'];
          if (rawList != null) {
            for (var item in rawList) {
              if (item is Map<String, dynamic>) {
                chats.add(item);
              }
            }
          }
        } else {
          print("chat data:${data}");
          chats.add(data ?? Map());
        }
        scrollDown();
      });

      socket.on("tasks", (data) {
        print(data);
        streamSocket.addResponse(data);
        if (data?.containsKey("chats") == true) {
          List<dynamic>? rawList = data?['chats'];
          if (rawList != null) {
            for (var item in rawList) {
              if (item is Map<String, dynamic>) {
                chats.add(item);
                print(item);
              }
            }
          }
        } else {
          print(data);
          chats.add(data ?? Map());
        }
      });
    } catch (e) {
      print("SOCKETS:${e}");
    }
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return shouldLock
        ? LockPageNotification(
            onAuthenticated: () {
              setState(() {
                shouldLock = false;
              });
            },
          )
        : WillPopScope(
            onWillPop: () async {
              Navigator.pop(context, confirm);
              return true;
            },
            child: Scaffold(
                resizeToAvoidBottomInset: true,
                backgroundColor: isDark ? cBackDarkColor2 : cWhiteColor,
                appBar: AppBar(
                  elevation: 0,
                  centerTitle: true,
                  automaticallyImplyLeading: false,
                  title: Container(
                    width: double.infinity,
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Visibility(
                            visible: canPop(context),
                            child: InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: Icon(Icons.arrow_back_sharp),
                            ),
                          ),
                          Spacer(),
                          Text(
                            LocaleKeys.task.tr(),
                            style: TextStyle(
                                fontWeight: FontWeight.w600, fontSize: 16.sp),
                          ),
                          Spacer()
                        ],
                      ),
                    ),
                  ),
                ),
                body: KeyboardDismissibleWidget(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: ListView(
                          controller: _sc,
                          children: [
                            Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20.w, vertical: 8.h),
                                decoration: BoxDecoration(
                                    color:
                                        isDark ? cBackDarkColor2 : cWhiteColor),
                                child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Container(
                                            alignment: Alignment.center,
                                            child: Text(
                                              '${LocaleKeys.task_number.tr()}',
                                              style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: cGrayTextColor),
                                            ),
                                          ),
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 12.w,
                                                vertical: 4.h),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                                border: Border.all(
                                                    color:
                                                        themeIdentify(context)
                                                            ? cBlueLight
                                                            : cFirstColor,
                                                    width: 1.w),
                                                borderRadius:
                                                    BorderRadius.circular(6.r)),
                                            child: Text(
                                              widget.taskDocs.task?.serialNumber
                                                      .toString() ??
                                                  "",
                                              style: TextStyle(
                                                  color: themeIdentify(context)
                                                      ? cWhiteColor
                                                      : cBlackColor,
                                                  fontSize: 18.sp,
                                                  fontWeight: FontWeight.w400),
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 20.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                              "${LocaleKeys.notification_time.tr()}",
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cGrayTextColor,
                                              )),
                                          Text(
                                            formatterHour.format(DateTime.parse(
                                                widget.taskDocs.date ??
                                                    "1970-00-00")),
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              color: isDark
                                                  ? cWhiteColor
                                                  : Colors.black,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                              "${LocaleKeys.notification_date.tr()}",
                                              style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: cGrayTextColor)),
                                          Text(
                                            formatterDate.format(DateTime.parse(
                                                widget.taskDocs.date ?? "")),
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: isDark
                                                    ? cWhiteColor
                                                    : Colors.black),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text("${LocaleKeys.term.tr()}",
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cGrayTextColor,
                                              )),
                                          Text(
                                            "${formatterDate.format(DateTime.parse(widget.taskDocs.endDate ?? "1970-00-00"))} (${duration1(DateTime.parse(widget.taskDocs.date ?? "1970-00-00"), DateTime.parse(widget.taskDocs.endDate ?? "1970-00-00"))} ${LocaleKeys.day.tr()})",
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              color: isDark
                                                  ? cWhiteColor
                                                  : cBlackColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 20.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text("${LocaleKeys.time_left.tr()}",
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cGrayTextColor,
                                              )),
                                          Text(
                                            "${duration2(DateTime.parse(widget.taskDocs.endDate ?? "1970-00-00"))}",
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              color: duration2(DateTime.parse(
                                                          widget.taskDocs
                                                                  .endDate ??
                                                              "1970-00-00")) >=
                                                      0
                                                  ? themeIdentify(context)
                                                      ? cWhiteColor
                                                      : cBlackColor
                                                  : cRedColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Visibility(
                                        visible: widget.taskDocs.task
                                                ?.documentType?.title !=
                                            null,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'Hujjat turi:',
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cGrayTextColor,
                                              ),
                                            ),
                                            Flexible(
                                              child: Text(
                                                "${widget.taskDocs.task?.documentType?.title ?? '-'}",
                                                style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: isDark
                                                      ? cWhiteColor
                                                      : cBlackColor,
                                                ),
                                                textAlign: TextAlign.right,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Visibility(
                                        visible: widget
                                                .taskDocs.task?.documentDate !=
                                            null,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'Hujjat sanasi:',
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cGrayTextColor,
                                              ),
                                            ),
                                            Flexible(
                                              child: Text(
                                                "${widget.taskDocs.task?.documentDate ?? '-'}",
                                                style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: isDark
                                                      ? cWhiteColor
                                                      : cBlackColor,
                                                ),
                                                textAlign: TextAlign.right,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Visibility(
                                        visible: widget.taskDocs.task
                                                ?.documentNumber !=
                                            null,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'Hujjat raqami:',
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cGrayTextColor,
                                              ),
                                            ),
                                            Flexible(
                                              child: Text(
                                                "${widget.taskDocs.task?.documentNumber?.title ?? '-'}",
                                                style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: isDark
                                                      ? cWhiteColor
                                                      : cBlackColor,
                                                ),
                                                textAlign: TextAlign.right,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Visibility(
                                        visible:
                                            widget.taskDocs.task?.headNumber !=
                                                null,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'Band raqami:',
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cGrayTextColor,
                                              ),
                                            ),
                                            Flexible(
                                              child: Text(
                                                "${widget.taskDocs.task?.headNumber ?? '-'}",
                                                style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: isDark
                                                      ? cWhiteColor
                                                      : cBlackColor,
                                                ),
                                                textAlign: TextAlign.right,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                      SizedBox(height: 16.h),
                                      Divider(
                                        thickness: 1.h,
                                        color: cGrayTextColor,
                                        indent: 4.w,
                                        endIndent: 4.w,
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 16.h),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                                "${LocaleKeys.send_organisation.tr()}",
                                                style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: cGrayTextColor,
                                                )),
                                            Expanded(
                                              child: Padding(
                                                padding:
                                                    EdgeInsets.only(left: 4.w),
                                                child: Text(
                                                  textAlign: TextAlign.right,
                                                  "${widget.taskDocs.moderator?.fullName ?? LocaleKeys.unknown.tr()}",
                                                  style: TextStyle(
                                                    fontSize: 16.sp,
                                                    color: isDark
                                                        ? cWhiteColor
                                                        : Colors.black,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Divider(
                                        thickness: 1.h,
                                        color: cGrayTextColor,
                                        indent: 4.w,
                                        endIndent: 4.w,
                                      ),
                                      SizedBox(
                                        height: 18.h,
                                      ),
                                      Text(
                                        widget.taskDocs.task?.title ??
                                            LocaleKeys.unknown.tr(),
                                        style: TextStyle(
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      SizedBox(
                                        height: 12.h,
                                      ),
                                      Text(
                                        parse(widget.taskDocs.task?.content ??
                                                    "<html lang='en'>...</html>")
                                                .documentElement
                                                ?.text ??
                                            '',
                                        style: TextStyle(fontSize: 16.sp),
                                      ),
                                      SizedBox(
                                        height: 20.h,
                                      ),
                                      AppendixItems(
                                          items: widget.taskDocs.task?.files),
                                      BlocConsumer<TaskDetailBloc,
                                          TaskDetailState>(
                                        listener: (context, state) {},
                                        builder: (context, state) {
                                          if (state.status ==
                                              TaskDetailStatus.success) {
                                            return Column(
                                              children: [
                                                SizedBox(
                                                  height: 14.h,
                                                ),
                                                InkWell(
                                                  onTap: () {
                                                    showDateTimePicker(context,
                                                        onPressedOK: (DateTime
                                                            val) async {
                                                      await addScheduledTask(
                                                        dateTime: val,
                                                        data: widget.taskDocs
                                                                .task ??
                                                            Task(),
                                                      );
                                                      BlocProvider.of<
                                                                  AlarmCubit>(
                                                              context)
                                                          .alarmText(
                                                              id: widget
                                                                      .taskDocs
                                                                      .taskId ??
                                                                  "",
                                                              type: TASK);
                                                      CustomToast.showToast(
                                                          LocaleKeys.alarm_added
                                                              .tr());
                                                      confirm = true;
                                                      Navigator.pop(context);
                                                    });
                                                  },
                                                  child: Container(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 10.w,
                                                              vertical: 14.h),
                                                      decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      8.r),
                                                          border: Border.all(
                                                              color: themeIdentify(
                                                                      context)
                                                                  ? cBlueLight
                                                                  : cFirstColor)),
                                                      child: BlocBuilder<
                                                          AlarmCubit,
                                                          AlarmState>(
                                                        builder:
                                                            (context, state) {
                                                          print(state);
                                                          if (state
                                                              is AlarmPuttedTime) {
                                                            return Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                Text(
                                                                  "${formatterCommentDate.format(state.dateTime!)}",
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          16.sp,
                                                                      fontFamily:
                                                                          regular),
                                                                ),
                                                                InkWell(
                                                                  child: Icon(
                                                                    Icons.close,
                                                                    size: 20.w,
                                                                    color: themeIdentify(
                                                                            context)
                                                                        ? cBlueLight
                                                                        : cFirstColor,
                                                                  ),
                                                                  onTap: () {
                                                                    cancelAndDeleteScheduledTaskById(
                                                                            actualId: widget.taskDocs.taskId ??
                                                                                "")
                                                                        .then(
                                                                            (value) {
                                                                      BlocProvider.of<AlarmCubit>(context).alarmText(
                                                                          id: widget.taskDocs.taskId ??
                                                                              "",
                                                                          type:
                                                                              TASK);
                                                                    });
                                                                    confirm =
                                                                        true;
                                                                    CustomToast
                                                                        .showToast(
                                                                            '${LocaleKeys.cancel_alarm.tr()}: №${widget.taskDocs.task?.serialNumber ?? ""}');
                                                                  },
                                                                )
                                                              ],
                                                            );
                                                          } else {
                                                            return Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                Text(
                                                                  "${LocaleKeys.add_reminder.tr()}",
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        16.sp,
                                                                  ),
                                                                ),
                                                                SvgPicture
                                                                    .asset(
                                                                  Assets
                                                                      .iconsAddAlert,
                                                                  width: 25.w,
                                                                  height: 25.h,
                                                                  color: themeIdentify(
                                                                          context)
                                                                      ? cBlueLight
                                                                      : cFirstColor,
                                                                )
                                                              ],
                                                            );
                                                          }
                                                        },
                                                      )),
                                                ),
                                              ],
                                            );
                                          } else {
                                            return Visibility(
                                              visible: widget.taskDocs.confirmed ==
                                                      false
                                                  ? (widget.pushKey == OPEN ||
                                                          widget.status ==
                                                              TaskCountEnumStatus
                                                                  .news)
                                                      ? false
                                                      : true
                                                  : true,
                                              child: Column(
                                                children: [
                                                  SizedBox(
                                                    height: 14.h,
                                                  ),
                                                  BlocBuilder<AlarmCubit,
                                                      AlarmState>(
                                                    builder: (context, state) {
                                                      print(state);
                                                      if (state
                                                          is AlarmPuttedTime) {
                                                        return Container(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        10.w,
                                                                    vertical:
                                                                        14.h),
                                                            decoration: BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(8
                                                                            .r),
                                                                border: Border.all(
                                                                    color: themeIdentify(
                                                                            context)
                                                                        ? cBlueLight
                                                                        : cFirstColor)),
                                                            child: Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                Text(
                                                                  "${formatterCommentDate.format(state.dateTime!)}",
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          16.sp,
                                                                      fontFamily:
                                                                          regular),
                                                                ),
                                                                InkWell(
                                                                  child: Icon(
                                                                    Icons.close,
                                                                    size: 20.w,
                                                                    color: themeIdentify(
                                                                            context)
                                                                        ? cBlueLight
                                                                        : cFirstColor,
                                                                  ),
                                                                  onTap: () {
                                                                    cancelAndDeleteScheduledTaskById(
                                                                            actualId: widget.taskDocs.taskId ??
                                                                                "")
                                                                        .then(
                                                                            (value) {
                                                                      BlocProvider.of<AlarmCubit>(context).alarmText(
                                                                          id: widget.taskDocs.taskId ??
                                                                              "",
                                                                          type:
                                                                              TASK);
                                                                    });
                                                                    confirm =
                                                                        true;
                                                                    CustomToast
                                                                        .showToast(
                                                                            '${LocaleKeys.cancel_alarm.tr()}: №${widget.taskDocs.task?.serialNumber ?? ""}');
                                                                  },
                                                                )
                                                              ],
                                                            ));
                                                      } else {
                                                        return InkWell(
                                                          onTap: () {
                                                            showDateTimePicker(
                                                                context,
                                                                onPressedOK:
                                                                    (DateTime
                                                                        val) async {
                                                              await addScheduledTask(
                                                                  dateTime: val,
                                                                  data: widget
                                                                          .taskDocs
                                                                          .task ??
                                                                      Task());
                                                              BlocProvider.of<
                                                                          AlarmCubit>(
                                                                      context)
                                                                  .alarmText(
                                                                      id: widget
                                                                              .taskDocs
                                                                              .taskId ??
                                                                          "",
                                                                      type:
                                                                          TASK);
                                                              CustomToast.showToast(
                                                                  LocaleKeys
                                                                      .alarm_added
                                                                      .tr());
                                                              confirm = true;
                                                              Navigator.pop(
                                                                  context);
                                                            });
                                                          },
                                                          child: Container(
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      horizontal:
                                                                          10.w,
                                                                      vertical:
                                                                          14.h),
                                                              decoration: BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(8
                                                                              .r),
                                                                  border: Border.all(
                                                                      color: themeIdentify(
                                                                              context)
                                                                          ? cBlueLight
                                                                          : cFirstColor)),
                                                              child: Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Text(
                                                                    "${LocaleKeys.add_reminder.tr()}",
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          16.sp,
                                                                    ),
                                                                  ),
                                                                  SvgPicture
                                                                      .asset(
                                                                    Assets
                                                                        .iconsAddAlert,
                                                                    width: 25.w,
                                                                    height:
                                                                        25.h,
                                                                    color: themeIdentify(
                                                                            context)
                                                                        ? cBlueLight
                                                                        : cFirstColor,
                                                                  )
                                                                ],
                                                              )),
                                                        );
                                                      }
                                                    },
                                                  ),
                                                ],
                                              ),
                                            );
                                          }
                                        },
                                      ),
                                      SizedBox(
                                        height: 14.h,
                                      ),
                                      BlocConsumer<TaskDetailBloc,
                                          TaskDetailState>(
                                        listener: (context, state) {
                                          if (state.status ==
                                              TaskDetailStatus.success) {
                                            confirm = true;
                                          }
                                        },
                                        builder: (context, state) {
                                          if (state.status ==
                                              TaskDetailStatus.success) {
                                            return MaterialButton(
                                              height: 36.h,
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          8.r)),
                                              onPressed: () {
                                                generateTaskPdf(
                                                    name: getStorage
                                                        .read(FULL_NAME)
                                                        .toString(),
                                                    id: widget.taskDocs.task
                                                            ?.serialNumber
                                                            .toString() ??
                                                        "",
                                                    time:
                                                        widget.taskDocs.date ??
                                                            "",
                                                    organisation: widget
                                                            .taskDocs
                                                            .moderator
                                                            ?.fullName ??
                                                        "",
                                                    title: widget.taskDocs.task
                                                            ?.title ??
                                                        "",
                                                    content: widget.taskDocs
                                                            .task?.content ??
                                                        "");
                                              },
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                    Assets.iconsShare,
                                                    color: themeIdentify(
                                                            context)
                                                        ? themeIdentify(context)
                                                            ? cBlueLight
                                                            : cFirstColor
                                                        : cFirstColor,
                                                  ),
                                                  SizedBox(
                                                    width: 10.w,
                                                  ),
                                                  Text(LocaleKeys.share.tr(),
                                                      style: TextStyle(
                                                          fontSize: 16.sp,
                                                          color: themeIdentify(
                                                                  context)
                                                              ? themeIdentify(
                                                                      context)
                                                                  ? cBlueLight
                                                                  : cFirstColor
                                                              : cFirstColor,
                                                          fontFamily: medium))
                                                ],
                                              ),
                                            );
                                          } else if (state.status ==
                                              TaskDetailStatus.loading) {
                                            return MaterialButton(
                                              height: 36.h,
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          8.r)),
                                              onPressed: null,
                                              disabledColor: cFirstColor.withOpacity(0.5),
                                              child: Center(
                                                child:
                                                    CupertinoActivityIndicator(),
                                              ),
                                            );
                                          } else {
                                            if (widget.taskDocs.confirmed == false
                                                ? widget.status ==
                                                        TaskCountEnumStatus
                                                            .news ||
                                                    widget.pushKey == OPEN
                                                : false) {
                                              return MaterialButton(
                                                height: 36.h,
                                                color: cFirstColor,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4.r)),
                                                onPressed: () async {
                                                  BlocProvider.of<
                                                              TaskDetailBloc>(
                                                          context)
                                                      .add(ConfirmEvent(
                                                          taskId: widget
                                                                  .taskDocs
                                                                  .taskId ??
                                                              ""));
                                                },
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Text(
                                                        LocaleKeys
                                                            .verification_title_up
                                                            .tr(),
                                                        style: TextStyle(
                                                            fontSize: 16.sp,
                                                            color: cWhiteColor,
                                                            fontFamily: medium))
                                                  ],
                                                ),
                                              );
                                            } else {
                                              return MaterialButton(
                                                height: 36.h,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.r)),
                                                onPressed: () {
                                                  generateTaskPdf(
                                                      name: getStorage
                                                          .read(FULL_NAME)
                                                          .toString(),
                                                      id: widget.taskDocs.task
                                                              ?.serialNumber
                                                              .toString() ??
                                                          "",
                                                      time: widget
                                                              .taskDocs.date ??
                                                          "",
                                                      organisation: widget
                                                              .taskDocs
                                                              .moderator
                                                              ?.fullName ??
                                                          "",
                                                      title: widget.taskDocs
                                                              .task?.title ??
                                                          "",
                                                      content: widget.taskDocs
                                                              .task?.content ??
                                                          "");
                                                },
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    SvgPicture.asset(
                                                      Assets.iconsShare,
                                                      color:
                                                          themeIdentify(context)
                                                              ? themeIdentify(
                                                                      context)
                                                                  ? cBlueLight
                                                                  : cFirstColor
                                                              : cFirstColor,
                                                    ),
                                                    SizedBox(
                                                      width: 10.w,
                                                    ),
                                                    Text(LocaleKeys.share.tr(),
                                                        style: TextStyle(
                                                            fontSize: 16.sp,
                                                            color: themeIdentify(
                                                                    context)
                                                                ? themeIdentify(
                                                                        context)
                                                                    ? cBlueLight
                                                                    : cFirstColor
                                                                : cFirstColor,
                                                            fontFamily: medium))
                                                  ],
                                                ),
                                              );
                                            }
                                          }
                                        },
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      Divider(
                                        thickness: 1.h,
                                        color: cGrayTextColor,
                                        indent: 20.w,
                                        endIndent: 20.w,
                                      ),
                                      SizedBox(
                                        height:
                                            MediaQuery.of(context).size.height /
                                                3,
                                        child:
                                            StreamBuilder<Map<String, dynamic>>(
                                                stream:
                                                    streamSocket.getResponse,
                                                builder: (context, snapshot) {
                                                  if (snapshot
                                                          .connectionState ==
                                                      ConnectionState.waiting) {
                                                    return SizedBox(
                                                      width:
                                                          MediaQuery.of(context)
                                                              .size
                                                              .width,
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          CupertinoActivityIndicator(
                                                            color: isDark
                                                                ? cWhiteColor
                                                                : cBlackColor,
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  } else {
                                                    ///https://medium.com/@enguerrand.arminjon/flutter-scroll-parent-when-listview-reach-bottom-646a2ca2ef16
                                                    return NotificationListener<
                                                        OverscrollNotification>(
                                                      onNotification:
                                                          (OverscrollNotification
                                                              value) {
                                                        if (value.overscroll <
                                                                0 &&
                                                            _sc.offset +
                                                                    value
                                                                        .overscroll <=
                                                                0) {
                                                          if (_sc.offset != 0) {
                                                            _sc.jumpTo(0);
                                                          }
                                                        } else if (_sc.offset +
                                                                value
                                                                    .overscroll >=
                                                            _sc.position
                                                                .maxScrollExtent) {
                                                          if (_sc.offset !=
                                                              _sc.position
                                                                  .maxScrollExtent) {
                                                            _sc.jumpTo(_sc
                                                                .position
                                                                .maxScrollExtent);
                                                          }
                                                        } else {
                                                          _sc.jumpTo(_sc
                                                                  .offset +
                                                              value.overscroll);
                                                        }
                                                        return true;
                                                      },
                                                      child: ListView.builder(
                                                          itemCount:
                                                              chats.length,
                                                          controller: _sc_chat,
                                                          itemBuilder:
                                                              (context, index) {
                                                            return Row(
                                                              children: [
                                                                Expanded(
                                                                    flex: chats[index]['sender'] ==
                                                                            true
                                                                        ? 7
                                                                        : 3,
                                                                    child: Visibility(
                                                                        visible: chats[index]['sender'] ==
                                                                                true
                                                                            ? true
                                                                            : false,
                                                                        child: ChatItemOthers(
                                                                            chats[index]))),
                                                                SizedBox(
                                                                  width: 10.w,
                                                                ),
                                                                Expanded(
                                                                    flex: chats[index]['sender'] ==
                                                                            false
                                                                        ? 7
                                                                        : 3,
                                                                    child: Visibility(
                                                                        visible: chats[index]['sender'] ==
                                                                                false
                                                                            ? true
                                                                            : false,
                                                                        child: ChatItemMe(
                                                                            chats[index]))),
                                                              ],
                                                            );
                                                          }),
                                                    );
                                                  }
                                                }),
                                      ),
                                    ]))
                          ],
                        ),
                      ),
                      BlocBuilder<TaskDetailBloc, TaskDetailState>(
                        builder: (context, state) {
                          if (state.status == TaskDetailStatus.success) {
                            return Container(
                              height: 60.h,
                              color: isDark
                                  ? cBlueDarkColor
                                  : cGrayBackgroundColor,
                              child: Row(
                                children: [
                                  Expanded(
                                      child: TextField(
                                    controller: chatController,
                                    decoration: InputDecoration(
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.symmetric(
                                            horizontal: 15.w),
                                        labelText:
                                            LocaleKeys.write_comment.tr(),
                                        labelStyle:
                                            TextStyle(color: cGrayColor)),
                                  )),
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: () async {
                                          permissionWallForFile(
                                              context: context,
                                              notificationId:
                                                  widget.taskDocs.taskId ?? "");
                                        },
                                        icon: SvgPicture.asset(
                                            Assets.iconsDocument,
                                            color: cGrayColor),
                                        splashColor: Colors.transparent,
                                        highlightColor: Colors.transparent,
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          sendText(
                                            notificationId:
                                                widget.taskDocs.taskId ?? "",
                                            text: chatController.text,
                                          );
                                        },
                                        icon:
                                            SvgPicture.asset(Assets.iconsSend),
                                        splashColor: Colors.transparent,
                                        highlightColor: Colors.transparent,
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            );
                          } else {
                            return Visibility(
                              visible: widget.taskDocs.confirmed == false
                                  ? (widget.status ==
                                              TaskCountEnumStatus.news ||
                                          widget.pushKey == OPEN)
                                      ? false
                                      : true
                                  : true,
                              child: Container(
                                height: 50.h,
                                color: isDark
                                    ? cBlueDarkColor
                                    : cGrayBackgroundColor,
                                child: Row(
                                  children: [
                                    Expanded(
                                        child: TextField(
                                      controller: chatController,
                                      decoration: InputDecoration(
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 15.w),
                                          labelText:
                                              LocaleKeys.write_comment.tr(),
                                          labelStyle:
                                              TextStyle(color: cGrayColor)),
                                      onChanged: (value) {
                                        setState(() {
                                          if (value.isNotEmpty) {
                                            isTyped = true;
                                          } else {
                                            isTyped = true;
                                          }
                                        });
                                      },
                                    )),
                                    IconButton(
                                      onPressed: () async {
                                        permissionWallForFile(
                                            context: context,
                                            notificationId:
                                                widget.taskDocs.taskId ?? "");
                                      },
                                      icon: SvgPicture.asset(
                                          Assets.iconsDocument,
                                          color: cGrayColor),
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                    ),
                                    isTyped
                                        ? IconButton(
                                            onPressed: () {
                                              sendText(
                                                notificationId:
                                                    widget.taskDocs.taskId ??
                                                        "",
                                                text: chatController.text,
                                              );
                                            },
                                            icon: SvgPicture.asset(
                                              Assets.iconsSend,
                                              color: cBlueLight,
                                            ),
                                            splashColor: Colors.transparent,
                                            highlightColor: Colors.transparent,
                                          )
                                        : IconButton(
                                            onPressed: () {
                                              showModalBottomSheet(
                                                  context: context,
                                                  builder:
                                                      (BuildContext context) {
                                                    return GestureDetector(
                                                      onTap: () {
                                                        Navigator.pop(context);
                                                      },
                                                      child: Container(
                                                        width: MediaQuery.of(
                                                                context)
                                                            .size
                                                            .width,
                                                        height: 200.h,
                                                        child: Column(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: [
                                                            SvgPicture.asset(
                                                              Assets
                                                                  .iconsMicrophone,
                                                              width: 40.w,
                                                              height: 40.w,
                                                            ),
                                                            SizedBox(
                                                                height: 10.h),
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .center,
                                                              children: [
                                                                Icon(Icons
                                                                    .play_arrow),
                                                              ],
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                    );
                                                  });
                                            },
                                            icon: SvgPicture.asset(
                                              Assets.iconsMicrophone,
                                              color: cBlueLight,
                                              width: 30.w,
                                              height: 24.h,
                                            ),
                                            splashColor: Colors.transparent,
                                            highlightColor: Colors.transparent,
                                          )
                                  ],
                                ),
                              ),
                            );
                          }
                        },
                      )
                    ],
                  ),
                )),
          );
  }

  Future<void> sendText(
      {required String notificationId, required String text}) async {
    final Dio dio = di();
    var response = await dio.put(sendTaskChatPath + notificationId,
        data: FormData.fromMap({"message": text}));
    if (response.statusCode == 200) {
      chatController.clear();
      scrollDown();
    } else {
      CustomToast.showToast("Xabar jo'natishda xatolik!");
    }
  }

  permissionWallForFile(
      {required BuildContext context, required String notificationId}) async {
    if (Platform.isAndroid) {
      var sdkInt = androidInfo?.version.sdkInt ?? 0;
      bool storage = await Permission.storage.shouldShowRequestRationale;
      Map<Permission, PermissionStatus> statuses = {};
      statuses = await requestPermissionsForFile();
      print(statuses[Permission.storage]);
      if (sdkInt < 33
          ? statuses[Permission.storage] != PermissionStatus.granted
          : false) {
        if (((sdkInt < 33
                ? statuses[Permission.storage] ==
                    PermissionStatus.permanentlyDenied
                : false) &&
            !storage)) {
          showDialog(
              context: context,
              builder: (context) {
                return PermissionDialog(
                  icon: Icons.folder,
                  text: LocaleKeys.givePermission.tr(),
                );
              });
        }
      } else {
        pickCommentFileAndSend(
            notificationId: notificationId, context: context);
      }
    } else {
      PermissionStatus status = await Permission.photos.request();
      if (status != PermissionStatus.granted) {
        if (status == PermissionStatus.permanentlyDenied) {
          showDialog(
              context: context,
              builder: (context) {
                return PermissionDialog(
                  icon: Icons.folder,
                  text: LocaleKeys.givePermission.tr(),
                );
              });
        }
      } else {
        pickCommentFileAndSend(
            notificationId: notificationId, context: context);
      }
    }
  }

  pickCommentFileAndSend(
      {required BuildContext context, required String notificationId}) async {
    final Dio dio = di();
    FilePickerResult? result = await FilePicker.platform.pickFiles();
    FormData formData;
    late BuildContext dialogContext;

    if (result != null) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            dialogContext = context;
            return ProgressDialog.screen();
          });

      File file = File(result.files.single.path ?? "");

      MultipartFile imageFile = await MultipartFile.fromFile(
        file.path,
        filename: path.basename(file.path),
        contentType: MediaType(
            lookupMimeType(file.path)?.split("/").first ?? "",
            lookupMimeType(file.path)?.split("/").last ?? ""),
      );

      formData = FormData.fromMap({"file": imageFile});
      print(lookupMimeType(file.path)?.split("/").first);

      try {
        Options options = Options(
          receiveDataWhenStatusError: true,
          headers: {
            "Content-Type": "multipart/form-data",
            "Accept": "application/json",
          },
          receiveTimeout: Duration(seconds: 60),
          sendTimeout: Duration(seconds: 60),
        );
        final response = await dio.put(sendTaskChatPath + notificationId,
            data: formData, onSendProgress: (int sent, int total) {
          BlocProvider.of<UploadDialogCubit>(context)
              .updateNumber((sent / total * 100).round() - 1);
        }, options: options);

        if (response.statusCode == 200) {
          Navigator.pop(dialogContext); // <<----
        } else {
          Navigator.pop(dialogContext); // <<----
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            try {
              CustomToast.showToast("Xatolik: ${e.response?.data}");
            } catch (e) {
              CustomToast.showToast('Parsing error of RAW data');
            }
          }
          return;
        }
        if (e.type == DioExceptionType.connectionTimeout) {
          CustomToast.showToast("Connection timeout");
          print('check your connection');
          Navigator.pop(dialogContext); // <<----

          return;
        }
        if (e.type == DioExceptionType.receiveTimeout) {
          CustomToast.showToast("Receive timeout");
          print('unable to connect to the server');
          Navigator.pop(dialogContext); // <<----
          return;
        }

        if (e.type == DioExceptionType.unknown) {
          CustomToast.showToast("Something went wrong");
          print('Something went wrong');
          return;
        }

        if (e.response?.statusCode == 400) {
          try {
            CustomToast.showToast("${e.response?.data['message']}");
          } catch (e) {
            CustomToast.showToast("Something went wrong");
            print('Something went wrong');
          }
        }
      } catch (e) {
        CustomToast.showToast("Error");
      }
    }
  }
}
