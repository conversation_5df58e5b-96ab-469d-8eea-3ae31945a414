import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/resources/color_manager.dart';

class CustomIntroButton extends StatelessWidget {
  final String? title;
  final double? width;
  final double? height;
  final VoidCallback? tap;
  final State? state;

  const CustomIntroButton(
      {Key? key, this.title, this.width, this.height, this.tap, this.state})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: tap,
      child: Container(
        width: width ?? 290.w,
        height: height ?? 56.h,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: ColorManager.primary),
        alignment: Alignment.center,
        child: Text(
          title.toString(),
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}
