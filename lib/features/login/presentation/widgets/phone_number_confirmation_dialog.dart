import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class PhoneNumberConfirmationDialog extends StatelessWidget {
  final String phoneNumber;
  final VoidCallback yesButton;
  final VoidCallback editButton;

  const PhoneNumberConfirmationDialog(
      {super.key,
      required this.phoneNumber,
      required this.yesButton,
      required this.editButton});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(cRadius12.r)),
      child: Container(
          color: isDark() == true ? cBackDarkColor2 : cWhiteColor,
        padding: EdgeInsets.all(14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(LocaleKeys.is_correct_number.tr(),style: TextStyle(fontSize: 14.sp),),
            SizedBox(
              height: 10.h,
            ),
            Text(phoneNumber,style: TextStyle(fontSize: 16.sp),),
            SizedBox(
              height: 10.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(onPressed: () {
                  editButton();
                }, child: Text(LocaleKeys.edit.tr(),style: TextStyle(color: isDark()?cFirstColor:cFirstColor),)),
                TextButton(onPressed: () {
                  yesButton();
                }, child: Text(LocaleKeys.yes.tr(),style: TextStyle(color: isDark()?cFirstColor:cFirstColor),))
              ],
            )
          ],
        ),
      ),
    );
  }
}
