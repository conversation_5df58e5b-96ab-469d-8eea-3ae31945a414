part of 'login_bloc.dart';

enum LoginStatus { initial, loading, success, failure }

class LoginState extends Equatable {
  final LoginStatus status;
  final String? response;
  final String? message;

  LoginState({required this.status, this.response, this.message});

  static LoginState initial() => LoginState(status: LoginStatus.initial);

  LoginState copyWith(
      {LoginStatus? status, String? response, String? message}) =>
      LoginState(
          status: status ?? this.status,
          response: response ?? this.response,
          message: message ?? this.message);

  @override
  List<Object?> get props => [status, response, message];
}
