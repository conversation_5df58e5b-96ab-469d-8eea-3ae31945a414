import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

part 'login_event.dart';

part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final Dio dio;
  final NetworkInfo networkInfo;

  LoginBloc({required this.dio, required this.networkInfo})
      : super(LoginState.initial()) {
    on<SendLoginEvent>(_sendLogin, transformer: sequential());
  }

  FutureOr<void> _sendLogin(
      SendLoginEvent event, Emitter<LoginState> emit) async {
    emit(state.copyWith(status: LoginStatus.loading));
    if (await networkInfo.isConnected) {
      try {
        var response = await dio.post(loginPath,data: {"phone":event.tel});
        if (response.data['message'] == 'success') {
          emit(state.copyWith(status: LoginStatus.success));
        } else {
          emit(state.copyWith(
              status: LoginStatus.failure, message: "Bad Response"));
        }

      } on DioException catch (e) {
        if (e.response?.statusCode == 404) {
          emit(state.copyWith(
              status: LoginStatus.failure,
              message: LocaleKeys.no_user.tr()));
        } else if (e.type == DioExceptionType.connectionTimeout) {
          emit(state.copyWith(
              status: LoginStatus.failure, message: "Time out exception!"));
        } else {
          emit(state.copyWith(
              status: LoginStatus.failure,
              message: "${LocaleKeys.unknown_error.tr()}"));
        }
      } catch (e) {
        emit(state.copyWith(
            status: LoginStatus.failure,
            message: LocaleKeys.unknown_error.tr()));
      }
    } else {
      emit(state.copyWith(status: LoginStatus.loading));
      emit(state.copyWith(
          status: LoginStatus.failure,
          message: LocaleKeys.no_internet.tr()));
    }
  }
}
