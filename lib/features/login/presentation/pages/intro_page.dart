import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/features/login/presentation/pages/login_page.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/utils/app_constants.dart';
import '../../../../di/dependency_injection.dart';
import '../widgets/cutom_intro_button.dart';

class IntroPage extends StatefulWidget {
  const IntroPage({Key? key}) : super(key: key);

  @override
  State<IntroPage> createState() => _IntroPageState();
}

class _IntroPageState extends State<IntroPage> {
  final SharedPreferences sharedPreferences = di();

  void setLanguage(String lang) async {
    await sharedPreferences.setString(language_pref, lang);
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            width: MediaQuery.of(context).size.width,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 100.h),
                  child: Text(
                    LocaleKeys.website_name.tr(),
                    style:
                        TextStyle(fontSize: 32.sp, fontWeight: FontWeight.w700),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 32.h),
                  child: Container(
                    width: 260.w,
                    child: Text(
                      textAlign: TextAlign.center,
                      "There are many variations of passages of Lorem Ipsum available, but the majority have ",
                      style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.black),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 44.h),
                  child: Image(image: AssetImage(Assets.imagesIntroImage)),
                ),
                SizedBox(
                  height: 70.h,
                ),
                CustomIntroButton(
                  width: 226.w,
                  height: 46.h,
                  title: LocaleKeys.lang_uz.tr(),
                  tap: () async {
                    setLanguage('uz');
                    await context.setLocale(Locale('uz', 'latin'));
                    Navigator.pushReplacement(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => LoginPage.screen()));
                  },
                ),
                SizedBox(
                  height: 16.h,
                ),
                CustomIntroButton(
                  width: 226.w,
                  height: 46.h,
                  title: LocaleKeys.lang_ru.tr(),
                  tap: () async {
                    setLanguage('ru');
                    await context.setLocale(Locale("ru"));
                    Navigator.pushReplacement(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => LoginPage.screen()));
                  },
                ),
                SizedBox(
                  height: 20.h,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
