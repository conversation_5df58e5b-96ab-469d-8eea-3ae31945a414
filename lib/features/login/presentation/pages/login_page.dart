import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/widgets/keyboard_dismissible_widget.dart';
import 'package:ijrochi/core/widgets/language_dialog.dart';
import 'package:ijrochi/demo/demo_auth_page.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/utils/app_constants.dart';
import '../../../../core/widgets/custom_toast.dart';
import '../../../../di/dependency_injection.dart';
import '../../../auth/presentation/pages/auth_page.dart';
import '../bloc/login_bloc.dart';
import '../widgets/phone_number_confirmation_dialog.dart';

class LoginPage extends StatefulWidget {
  final bool? updateState;

  const LoginPage({Key? key, this.updateState}) : super(key: key);

  static Widget screen([bool? updateState]) => MultiBlocProvider(
    providers: [
      BlocProvider(create: (context) => di<LoginBloc>()),
    ],
    child: LoginPage(
      updateState: updateState,
    ),
  );

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  var maskFormatter = MaskTextInputFormatter(mask: '## ###-##-##');
  TextEditingController tel = TextEditingController();
  final SharedPreferences sharedPreferences = di();
  bool isDark = false;
  bool isOpened = false;
  late LoginBloc _bloc;
  int numberCount = 0;
  late BuildContext dialogContext;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of<LoginBloc>(context);
    getSelectedLanguage();
  }

  String _verticalGroupValue = "O\'zbekcha";

  getSelectedLanguage() async {
    String lang = await sharedPreferences.getString(language_pref) ?? 'uz';
    switch (lang) {
      case 'uz':
        {
          _verticalGroupValue = 'O\'zbekcha';
          break;
        }
      case 'ru':
        {
          _verticalGroupValue = 'Русский';
          break;
        }
      case 'cr':
        {
          _verticalGroupValue = 'Ўзбекча';
          break;
        }
      default:
        {
          _verticalGroupValue = 'O\'zbekcha';
        }
    }
  }

  @override
  void dispose() {
    tel.dispose();
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: cWhiteColor,
      body: KeyboardDismissibleWidget(
        child: Container(
          child: BlocConsumer<LoginBloc, LoginState>(
            listener: (context, state) {
              if (state.status == LoginStatus.failure) {
                CustomToast.showToast(state.message.toString());
                Navigator.pop(dialogContext);
              }
              if (state.status == LoginStatus.success) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Navigator.push(
                    context,
                    CupertinoPageRoute(
                        builder: (context) =>
                            AuthPage.screen(maskFormatter.getUnmaskedText())),
                  );
                });
              }
            },
            builder: (context, state) {
              return SafeArea(
                child: Container(
                  padding:
                  EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          SizedBox(
                            height: 15.h,
                          ),
                          Align(
                            alignment: Alignment.topRight,
                            child: InkWell(
                              onTap: () {
                                showDialog(
                                    context: context,
                                    builder: (BuildContext context1) {
                                      return LanguageDialog.screen(
                                          onSelectCallBack: () {
                                            setState(() {
                                              getSelectedLanguage();
                                              dismissDialog(context1);
                                            });
                                          });
                                    });
                              },
                              child: Padding(
                                padding: EdgeInsets.all(8.w),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      _verticalGroupValue,
                                      style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontFamily: "interMedium"),
                                    ),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                    SvgPicture.asset(
                                      Assets.iconsIcLanguage,
                                      width: 22.w,
                                      height: 22.w,
                                      color: cFirstColor,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 48.h,
                          ),
                          Align(
                            alignment: Alignment.topLeft,
                            child: Text(LocaleKeys.welcome.tr(),
                                style: TextStyle(
                                  fontSize: 38.sp,
                                  color: cBlackColor,
                                  fontWeight: FontWeight.w800,
                                )),
                          ),
                          Align(
                            alignment: Alignment.topLeft,
                            child: Text(LocaleKeys.welcome_2.tr(),
                                style: TextStyle(
                                    fontSize: 38.sp,
                                    fontFamily: interFont,
                                    color: cFirstColor,
                                    fontWeight: FontWeight.bold)),
                          ),
                          SizedBox(
                            height: 4.h,
                          ),
                          Align(
                            alignment: Alignment.topLeft,
                            child: Text(
                                textAlign: TextAlign.start,
                                LocaleKeys.login_desc.tr(),
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    fontFamily: regular,
                                    color: cGrayTextColor1)),
                          ),
                          SizedBox(
                            height: 18.h,
                          ),
                          Container(
                            width: MediaQuery.of(context).size.width,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  height: 48.h,
                                  child: TextField(
                                    decoration: InputDecoration(
                                      isDense: true,
                                      enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              width: 1.4, color: cFirstColor)),
                                      border: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              width: 2, color: cFirstColor)),
                                      focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              width: 2, color: cFirstColor)),
                                      disabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              width: 2, color: cFirstColor)),
                                      label: Text(
                                        LocaleKeys.login_phone.tr(),
                                        style: TextStyle(
                                            fontFamily: medium,
                                            color: cFirstColor,
                                            fontSize: 14.sp),
                                      ),
                                      prefix: Text("+998 ",
                                          style: TextStyle(
                                            color: cFirstColor,
                                            fontSize: 14.sp,
                                            fontFamily: medium,
                                          )),
                                    ),
                                    style: TextStyle(
                                        fontSize: 14.sp, fontFamily: medium),
                                    inputFormatters: [maskFormatter],
                                    keyboardType: TextInputType.phone,
                                    controller: tel,
                                    onChanged: (value) {
                                      setState(() {
                                        numberCount = value
                                            .replaceAll("-", '')
                                            .replaceAll(" ", '')
                                            .length;
                                      });
                                    },
                                  ),
                                ),
                                SizedBox(
                                  height: 4.h,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Text(
                                      "${numberCount}/9",
                                      style: TextStyle(
                                          color: cFirstColor,
                                          fontSize: 12.sp,
                                          fontFamily: "interMedium"),
                                    ),
                                    SizedBox(
                                      width: 10.w,
                                    )
                                  ],
                                ),
                                SizedBox(
                                  height: 10.h,
                                ),
                                MaterialButton(
                                  height: 40.h,
                                  onPressed:state.status == LoginStatus.loading?null: () {
                                    if(state.status!=LoginStatus.loading){
                                      if (numberCount == 9) {
                                        showDialog(
                                            barrierDismissible: true,
                                            context: context,
                                            builder: (context) {
                                              dialogContext = context;
                                              return PhoneNumberConfirmationDialog(
                                                phoneNumber:
                                                '+998${maskFormatter.getMaskedText()}',
                                                yesButton: () async{
                                                  Navigator.pop(context);
                                                  await sendPhoneNumber();
                                                },
                                                editButton: () {
                                                  Navigator.pop(context);
                                                },
                                              );
                                            });
                                      } else {
                                        CustomToast.showToast(
                                            LocaleKeys.no_phone_number.tr());
                                      }
                                    }
                                  },
                                  minWidth: MediaQuery.of(context).size.width,
                                  child: _button(state),
                                  color: cFirstColor,
                                  disabledColor: cFirstColor.withOpacity(0.5),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                      Column(
                        children: [
                          Text(
                            LocaleKeys.help.tr(),
                            style: TextStyle(
                                color: cGrayTextColor1,
                                fontSize: 14.sp,
                                fontFamily: regular),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(
                            height: 10.h,
                          ),
                          Center(
                              child: TextButton(
                            onPressed: () async {
                              final Uri launchUri =
                                  Uri(scheme: 'tel', path: SUPPORT_TEL);
                              if (await canLaunchUrl(launchUri)) {
                                await launchUrl(launchUri);
                              } else {
                                CustomToast.showToast(
                                    'This action is not supported');
                              }
                            },
                            child: Text(LocaleKeys.connect_us.tr(),
                                style: TextStyle(
                                    color: cFirstColor,
                                    fontSize: 14.sp,
                                    fontFamily: Assets.fontsInterSemiBold)),
                          )),
                          SizedBox(
                            height: 30.h,
                          )
                        ],
                      )
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  sendPhoneNumber() {
    var phone = maskFormatter.getUnmaskedText();
    if (phone.length > 8) {
      _bloc.add(SendLoginEvent(tel: phone));
    } else {
      Snack(LocaleKeys.no_phone_number.tr(), context, cRedColor);
    }
  }

  Widget _button(LoginState state) {
    if (state.status == LoginStatus.loading) {
      return const CupertinoActivityIndicator(
        color: cWhiteColor,
      );
    } else {
      return Text(
        LocaleKeys.send.tr(),
        style: TextStyle(color: cWhiteColor),
      );
    }
  }
}
