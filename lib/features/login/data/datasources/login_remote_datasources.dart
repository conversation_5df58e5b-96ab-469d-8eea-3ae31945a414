import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/utils/api_path.dart';
import '../../../../core/utils/app_constants.dart';
import '../../../../di/dependency_injection.dart';

abstract class LoginRemoteDatasource {
  Future<dynamic> setData(String tel);
}

class LoginRemoteDatasourceImpl implements LoginRemoteDatasource {
  final http.Client client;
  final SharedPreferences sharedPreferences = di();

  LoginRemoteDatasourceImpl({required this.client});

  @override
  Future<dynamic> setData(String tel) async {
    //String? appSignature = await AndroidSmsRetriever.getAppSignature();
    // CustomToast.showToast('Mac: ' + macAddress + 'signature' + appSignature.toString());
    try {
      var body = {"phone": tel};
      final response = await client.post(
        Uri.parse(baseUrl + loginPath),
        body: jsonEncode(body),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          'lang': '${sharedPreferences.getString(language_pref)}',
          'Bearer': ' ${sharedPreferences.getString(BEARER_TOKEN)}'
        },
      );
      if (response.statusCode == 200) {
        final parsed = json.decode(response.body);
        if (parsed["status"] == "success") {
          return "1";
        }
      } else if (response.statusCode == 400) {
        return "2";
      } else {
        return "0";
      }
    } catch (e) {
      print('Login remote exception: ' + e.toString());
      return "2";
    }
    // on InputFormatterFailure {
    //   return "500";
    // }
  }
}
