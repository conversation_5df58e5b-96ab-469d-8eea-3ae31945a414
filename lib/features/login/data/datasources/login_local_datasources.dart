import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/errors/failures.dart';
import '../models/user_model.dart';

abstract class LoginLocalDataSource {
  Future<String> setDataLocal(UserModel list);
}

class LoginLocalDataSourceImpl implements LoginLocalDataSource {
  final SharedPreferences sharedPreferences;

  LoginLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<String> setDataLocal(UserModel user) async {
    try {
      ///Not used
      // sharedPreferences.setString("id", user.id.toString());
      // sharedPreferences.setString("name", user.name.toString());
      // sharedPreferences.setString("phone", user.phone.toString());
      // sharedPreferences.setString("email", user.email.toString());
      // sharedPreferences.setString("mobile_token", user.mobileToken.toString());
      return "2";
    } on LocalFailure {
      return "500";
    }
  }
}
