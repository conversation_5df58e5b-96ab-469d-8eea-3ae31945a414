class UserModel {
  UserModel({
      this.userId,
      this.fullName,
      this.photo,
      this.phone, 
      this.date,
      this.endDate,});

  UserModel.fromJson(dynamic json) {
    userId = json['userId'];
    fullName = json['fullName'];
    photo = json['photo'];
    phone = json['phone'];
    date = json['date'];
    endDate = json['endDate'];
  }
  int? userId;
  String? fullName;
  String? photo;
  String? phone;
  String? date;
  String? endDate;
}