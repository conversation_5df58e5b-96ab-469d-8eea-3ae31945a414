import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repository/login_repository.dart';

class LoginData extends UseCase<dynamic, LoginParams> {
  final LoginRepository loginRepository;

  LoginData({required this.loginRepository});

  @override
  Future<Either<Failure, dynamic>> call(LoginParams params) {
    return loginRepository.sendLogin(params.tel);
  }
}

class LoginParams extends Equatable {
  final String tel;

  @override
  List<Object?> get props => [tel];

  const LoginParams(this.tel);
}
