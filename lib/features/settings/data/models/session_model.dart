import 'package:isar/isar.dart';
part 'session_model.g.dart';

List<SessionModel> listSessionModelFromJson(dynamic data) {
  return List<SessionModel>.from(data.map((e) => SessionModel.fromJson(e)))
      .toList();
}
@collection
@Name("session_model")
class SessionModel {
  SessionModel({
    this.id,
    this.performer,
    this.macAddress,
    this.deviceName,
    this.deviceVersion,
    this.lastActive,
    this.date,
    this.createdAt,
    this.updatedAt,
  });

  SessionModel.fromJson(dynamic json) {
    id = json['_id'];
    performer = json['performer'];
    macAddress = json['macAddress'];
    deviceName = json['deviceName'];
    deviceVersion = json['deviceVersion'];
    lastActive = json['lastActive'];
    date = json['date'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Id localId = Isar.autoIncrement;
  String? id;
  String? performer;
  String? macAddress;
  String? deviceName;
  String? deviceVersion;
  String? lastActive;
  String? date;
  String? createdAt;
  String? updatedAt;

}
