
import 'package:dio/dio.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/features/settings/data/models/session_model.dart';

abstract class SettingsRemoteDataSources {
  Future<List<SessionModel>> getSessionModels();
}

class SettingsRemoteDataSourcesImpl extends SettingsRemoteDataSources {
  final Dio dio;

  SettingsRemoteDataSourcesImpl({required this.dio});

  @override
  Future<List<SessionModel>> getSessionModels() async {
    var response = await dio.get(getSessions);
    if (response.statusCode == 200) {
      return listSessionModelFromJson(response.data);
    } else {
      throw DioException(
          requestOptions: response.requestOptions, response: response);
    }
  }
}
