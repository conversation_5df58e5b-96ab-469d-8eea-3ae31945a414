import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/features/settings/data/models/session_model.dart';
import 'package:isar/isar.dart';


abstract class SettingsLocalDataSources {
  Future<List<SessionModel>> getSessionModel();

  Future<void> setSessionModel(List<SessionModel> list);
}

class SettingsLocalDataSourcesImpl extends SettingsLocalDataSources {
  final IsarService isarService;

  SettingsLocalDataSourcesImpl({required this.isarService});

  @override
  Future<List<SessionModel>> getSessionModel() async {
    try {
      List<SessionModel> list =await isarService.isar.sessionModels.where().findAll();
      print("list:${list}");
      return list;
    } catch (e) {
      throw Exception();
    }
  }

  @override
  Future<void> setSessionModel(List<SessionModel> list) async {
    try {
      await isarService.isar.writeTxn(() async {
        await isarService.isar.sessionModels.where().deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        await isarService.isar.sessionModels.putAll(list);
      });
    } catch (e) {
      throw Exception();
    }
  }
}
