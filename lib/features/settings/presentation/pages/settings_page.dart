import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:group_radio_button/group_radio_button.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/features/settings/presentation/bloc/settings_bloc.dart';
import 'package:ijrochi/features/settings/presentation/pages/pin_code_change_page.dart';
import 'package:ijrochi/features/settings/presentation/pages/sessions_page.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/utils/app_constants.dart';
import '../../../../core/widgets/custom_toast.dart';
import '../../../../di/dependency_injection.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  static Widget screen() => BlocProvider<SettingsBloc>(
        create: (context) => di<SettingsBloc>(),
        child: SettingsPage(),
      );

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final SharedPreferences prefs = di();
  var maskFormatter = MaskTextInputFormatter(mask: '## ###-##-##');
  bool isDark = false;
  late bool useBiometric;
  late SettingsBloc _bloc;
  bool isOpened = false;
  String selectedOptionForNotification = FCM_SWITCH;
  final notificationStatus = [FCM_SWITCH, SOCKET_SWITCH];
  BuildContext? dcontext;
  final GetStorage getStorage=di();

  @override
  void initState() {
    useBiometric = prefs.getBool('use_biometric') ?? true;
    selectedOptionForNotification = prefs.getString(SWITCH_PUSH) ?? FCM_SWITCH;
    isDark = prefs.getString(theme_pref) == 'ThemeMode.dark' ? true : false;
    _bloc = BlocProvider.of<SettingsBloc>(context);
    super.initState();
  }

  String _verticalGroupValue = "O\'zbekcha";
  final _status = [
    "O\'zbekcha",
    "Ўзбекча",
    "Русский",
  ];

  dismissDailog() {
    if (dcontext != null) {
      Navigator.pop(dcontext!);
    }
  }

  String languageText() {
    String lang = prefs.getString(language_pref) ?? 'uz';
    print(lang);
    switch (lang) {
      case 'uz':
        {
          return 'O\'zbekcha';
        }
      case 'ru':
        {
          return 'Русский';
        }
      case 'cr':
        {
          return 'Ўзбекча';
        }
      default:
        {
          return 'O\'zbekcha';
        }
    }
  }

  void setLanguage(String lang) async {
    await prefs.setString(language_pref, lang);
  }

  changeLanguage(String? value) async {
    _verticalGroupValue = value ?? '';
    if (_verticalGroupValue == 'O\'zbekcha') {
      await context.setLocale(Locale('uz', 'latin'));
      setLanguage('uz');
    } else if (_verticalGroupValue == 'Русский') {
      await context.setLocale(Locale('ru'));
      setLanguage('ru');
    } else {
      await context.setLocale(Locale('uz', "cyrillic"));
      setLanguage('cr');
    }
    dismissDailog();
  }

  @override
  Widget build(BuildContext context) {
    _verticalGroupValue = languageText();
    TextTheme _textTheme = Theme.of(context).textTheme;
    return Scaffold(
      backgroundColor: isDark == true ? cBackDarkColor2 : cWhiteColor,
      appBar: AppBar(
        elevation: 0,
        title: Padding(
          padding: EdgeInsets.symmetric(horizontal: 70.w),
          child: Text(
            LocaleKeys.settings.tr(),
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {},
        child: SingleChildScrollView(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 18.w),
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 30.h),
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        getStorage.read(FULL_NAME),
                        style: TextStyle(
                            fontSize: 20.sp, fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      Row(
                        children: [
                          Text(
                            LocaleKeys.telephone.tr() + ":",
                            style: TextStyle(
                                fontSize: 16.sp, fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                              "+998" +  maskFormatter.maskText(
                                  getStorage.read(PHONE_NUMBER)),
                              style: TextStyle(fontSize: 16.sp))
                        ],
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: 36.h,
                ),
                InkWell(
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          dcontext = context;
                          return StatefulBuilder(builder: (context, setState) {
                            return Dialog(
                              shape: RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(4.r))),
                              child: Container(
                                decoration: BoxDecoration(
                                    color: isDark == true
                                        ? cBackDarkColor2
                                        : cWhiteColor,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(4.r))),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          left: 20, top: 20),
                                      child: Text(
                                        LocaleKeys.select_language.tr(),
                                        style: TextStyle(
                                            fontSize: 16.sp,
                                            fontFamily: medium),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 6.h,
                                    ),
                                    RadioGroup<String>.builder(
                                      groupValue: _verticalGroupValue,
                                      onChanged: (value) {
                                        changeLanguage(value);
                                      },
                                      items: _status,
                                      textStyle: TextStyle(
                                          fontSize: 16.sp,
                                          color: themeIdentify(context)
                                              ? cGrayTextColor
                                              : cBlackColor),
                                      itemBuilder: (item) => RadioButtonBuilder(
                                        item,
                                      ),
                                      fillColor: Colors.blue,
                                    ),
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          });
                          // return BlocBuilder<SettingsBloc, SettingsState>(
                          //   builder: (context, state) {
                          //   },
                          // );
                        });
                  },
                  child: BlocConsumer<SettingsBloc, SettingsState>(
                    listener: (context, state) {},
                    builder: (context, state) {
                      if (state is SaveSelectedLang) {
                        return Container(
                          width: MediaQuery.of(context).size.width,
                          height: 58.h,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                LocaleKeys.app_language.tr(),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                height: 5.h,
                              ),
                              Text(
                                _verticalGroupValue,
                                style: TextStyle(
                                    fontSize: 16.sp, color: Colors.grey),
                              )
                            ],
                          ),
                        );
                      } else if (state is SettingsInitial) {
                        return Container(
                          width: MediaQuery.of(context).size.width,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                LocaleKeys.app_language.tr(),
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                height: 5.h,
                              ),
                              Text(
                                _verticalGroupValue,
                                style: TextStyle(
                                    fontSize: 14.sp, color: Colors.grey),
                              )
                            ],
                          ),
                        );
                      } else {
                        return Container(
                          width: MediaQuery.of(context).size.width,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                LocaleKeys.app_language.tr(),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                height: 5.h,
                              ),
                              Text(
                                _verticalGroupValue,
                                style: TextStyle(
                                    fontSize: 16.sp, color: Colors.grey),
                              )
                            ],
                          ),
                        );
                      }
                    },
                  ),
                ),
                Container(
                  height: 0.4.h,
                  color: isDark ? cWhiteColor : cBackDarkColor2,
                ),
                InkWell(
                  onTap: () {
                    Navigator.push(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => SessionsPage.screen()));
                  },
                  child: Container(
                    alignment: Alignment.centerLeft,
                    width: MediaQuery.of(context).size.width,
                    height: 56.h,
                    child: Text(
                      LocaleKeys.active_session.tr(),
                      style: TextStyle(
                          fontSize: 16.sp, fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
                ///TODO remove if unnecessary
                // Visibility(
                //   visible: Platform.isAndroid ? true : false,
                //   child: Column(
                //     children: [
                //       Container(
                //         height: 0.4.h,
                //         color: isDark ? cWhiteColor : cBackDarkColor2,
                //       ),
                //       ExpansionTile(
                //         backgroundColor:
                //             isDark == true ? cBackDarkColor2 : cWhiteColor,
                //         tilePadding: EdgeInsets.only(left: 0.w, right: 10.w),
                //         title: Text(
                //           LocaleKeys.notification_settings.tr(),
                //           style: TextStyle(
                //               fontSize: 16.sp, fontWeight: FontWeight.w500),
                //         ),
                //         trailing: Icon(
                //           isOpened == false
                //               ? Icons.keyboard_arrow_down
                //               : Icons.keyboard_arrow_up,
                //           color: cGrayColor,
                //         ),
                //         onExpansionChanged: (bool expanded) {
                //           setState(() {
                //             isOpened = expanded;
                //           });
                //         },
                //         children: [
                //           RadioGroup<String>.builder(
                //             groupValue: selectedOptionForNotification,
                //             onChanged: (value) {
                //               setState(() {
                //                 selectedOptionForNotification =
                //                     value ?? FCM_SWITCH;
                //                 prefs.setString(
                //                     SWITCH_PUSH, value ?? FCM_SWITCH);
                //                 if (value == SOCKET_SWITCH) {
                //                 } else {
                //                   // FlutterBackgroundService()
                //                   //     .invoke("stopService");
                //                 }
                //               });
                //             },
                //             items: notificationStatus,
                //             textStyle: TextStyle(
                //                 fontSize: 16.sp,
                //                 color: isDark ? cGrayTextColor : cBlackColor),
                //             itemBuilder: (item) => RadioButtonBuilder(
                //               item,
                //             ),
                //             fillColor: Colors.blue,
                //           ),
                //         ],
                //       ),
                //     ],
                //   ),
                // ),
                Container(
                  height: 0.4.h,
                  color: isDark ? cWhiteColor : cBackDarkColor2,
                ),
                InkWell(
                  onTap: () {
                    Navigator.push(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => PinCodeChangePage.screen()));
                  },
                  child: Container(
                    alignment: Alignment.centerLeft,
                    width: MediaQuery.of(context).size.width,
                    height: 58.h,
                    child: Text(
                      LocaleKeys.change_pin_code.tr(),
                      style: TextStyle(
                          fontSize: 16.sp, fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
                Container(
                  height: 0.4.h,
                  color: isDark ? cWhiteColor : cBackDarkColor2,
                ),
                InkWell(
                  onTap: () {
                    setState(() {
                      useBiometric = !useBiometric;
                      prefs.setBool('use_biometric', useBiometric); 
                    });
                  },
                  child: Container(
                    alignment: Alignment.centerLeft,
                    width: MediaQuery.of(context).size.width,
                    height: 56.h,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(
                            LocaleKeys.enter_fingerprint.tr(),
                            style: TextStyle(
                                fontSize: 16.sp, fontWeight: FontWeight.w500),
                          ),
                        ),
                        Checkbox(
                            value: useBiometric,
                            onChanged: (value) {
                              setState(() {
                                useBiometric = value ?? true;
                                prefs.setBool('use_biometric', value ?? true);
                              });
                            })
                      ],
                    ),
                  ),
                ),
                Container(
                  height: 0.4.h,
                  color: isDark ? cWhiteColor : cBackDarkColor2,
                ),
                ExpansionTile(
                  backgroundColor:
                      isDark == true ? cBackDarkColor2 : cWhiteColor,
                  tilePadding: EdgeInsets.only(left: 0.w, right: 10.w),
                  title: Text(
                    LocaleKeys.retry_communication.tr(),
                    style:
                        TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
                  ),
                  trailing: Icon(
                    isOpened == false
                        ? Icons.keyboard_arrow_down
                        : Icons.keyboard_arrow_up,
                    color: cGrayColor,
                  ),
                  onExpansionChanged: (bool expanded) {
                    setState(() {
                      isOpened = expanded;
                    });
                  },
                  children: [
                    Container(
                      height: 0.4.h,
                      color: isDark ? cWhiteColor : cBackDarkColor2,
                    ),
                    InkWell(
                      onTap: () async {
                        String tgUsername = TELEGRAM;
                        var uri = Uri.parse(tgUsername);
                        if (await canLaunchUrl(uri)) {
                          launchUrl(uri);
                        } else {
                          CustomToast.showToast('This action is not supported');
                        }
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        color: isDark ? backgroundDark : cWhiteColor,
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Telegram',
                              style: TextStyle(
                                  fontSize: 16.sp, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              '@ijrochi_uz',
                              style: _textTheme.displayMedium!.copyWith(
                                  fontSize: 14.sp, color: Colors.grey),
                            )
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: Container(
                        height: 0.4.h,
                        color: isDark ? cWhiteColor : cBackDarkColor2,
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        String? encodeQueryParameters(
                            Map<String, String> params) {
                          return params.entries
                              .map((MapEntry<String, String> e) =>
                                  '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
                              .join('&');
                        }

                        final Uri emailUri = Uri(
                          scheme: 'mailto',
                          path: EMAIL,
                        );
                        if (await canLaunchUrl(emailUri)) {
                          launchUrl(emailUri);
                        } else {
                          CustomToast.showToast('This action is not supported');
                        }
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        color: isDark ? backgroundDark : cWhiteColor,
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'E-mail',
                              style: TextStyle(
                                  fontSize: 16.sp, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              '<EMAIL>',
                              style: TextStyle(
                                  fontSize: 14.sp, color: Colors.grey),
                            )
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: Container(
                        height: 0.4.h,
                        color: isDark ? cWhiteColor : cBackDarkColor2,
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        final Uri launchUri =
                            Uri(scheme: 'tel', path: SUPPORT_TEL);
                        if (await canLaunchUrl(launchUri)) {
                          await launchUrl(launchUri);
                        } else {
                          CustomToast.showToast('This action is not supported');
                        }
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        color: isDark ? backgroundDark : cWhiteColor,
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              LocaleKeys.telephone.tr(),
                              style: TextStyle(
                                  fontSize: 16.sp, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              '+999(73) 244-05-35',
                              style: TextStyle(
                                  fontSize: 14.sp, color: Colors.grey),
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                Visibility(
                  visible: isOpened ? false : true,
                  child: Container(
                    height: 0.4.h,
                    color: isDark ? cWhiteColor : cBackDarkColor2,
                  ),
                ),
                Container(
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.app_version.tr(),
                        style: TextStyle(
                            fontSize: 16.sp, fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        height: 5.h,
                      ),
                      Text(
                        '$APP_VERSION',
                        style: TextStyle(fontSize: 14.sp, color: Colors.grey),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 0.4.h,
                  color: isDark ? cWhiteColor : cBackDarkColor2,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
