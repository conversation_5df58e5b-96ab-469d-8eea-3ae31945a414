import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import '../../../../di/dependency_injection.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/utils/app_constants.dart';
import '../../../../resources/color_manager.dart';
import '../../../lock/presentation/widgets/password_textfield.dart';
import '../bloc/settings_bloc.dart';

class PinCodeChangePage extends StatefulWidget {
  const PinCodeChangePage({Key? key}) : super(key: key);

  static Widget screen() => BlocProvider<SettingsBloc>(
        create: (context) => di<SettingsBloc>(),
        child: PinCodeChangePage(),
      );

  @override
  State<PinCodeChangePage> createState() => _PinCodeChangePageState();
}

class _PinCodeChangePageState extends State<PinCodeChangePage> {
  final SharedPreferences sharedPreferences = di();
  TextEditingController _currentPinCodeController = TextEditingController();
  TextEditingController _newPinCodeController = TextEditingController();
  TextEditingController _confirmPinCodeController = TextEditingController();
  late SettingsBloc _bloc;
  bool isDark = false;

  @override
  void initState() {
    _bloc = BlocProvider.of<SettingsBloc>(context);
    isDark = sharedPreferences.getString(theme_pref) == 'ThemeMode.dark'
        ? true
        : false;
    super.initState();
  }

  BuildContext? dcontext;

  dismissDailog() {
    if (dcontext != null) {
      _bloc.add(CheckPinCodeInitialEvent());
      Navigator.pop(dcontext!);
    }
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    TextTheme _textTheme = Theme.of(context).textTheme;
    return Scaffold(
      backgroundColor: isDark ? cBackDarkColor2 : cWhiteColor,
      appBar: AppBar(
        elevation: 0,
        title: Padding(
          padding:  EdgeInsets.only(left: 60.w),
          child: Text(LocaleKeys.change_pin_code.tr(),style: TextStyle(fontSize: 16.sp),),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: BlocConsumer<SettingsBloc, SettingsState>(
            listener: (context, state) {
              if (state is AllPinCodesMatched) {
                showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      dcontext = context;
                      return AlertDialog(
                        backgroundColor:
                            isDark == true ? cFirstColorDark : cWhiteColor,
                        title: Text(LocaleKeys.change_pin_code.tr()),
                        content: Text(LocaleKeys.going_to_change_pin_code.tr()),
                        actions: [
                          TextButton(
                              onPressed: () {
                                setState(() {
                                  dismissDailog();
                                });
                              },
                              child: Text(
                                LocaleKeys.cancel.tr(),
                                style: _textTheme.displayMedium!
                                    .copyWith(fontSize: 16.sp),
                              )),
                          TextButton(
                              onPressed: () {
                                _bloc.add(SaveNewPinEvent(
                                    _newPinCodeController.value.text));
                              },
                              child: Text(
                                'OK',
                                style: _textTheme.displayMedium!
                                    .copyWith(fontSize: 16.sp),
                              )),
                        ],
                      );
                    }).then((value) {
                  _bloc.add(CheckPinCodeInitialEvent());
                });
              }
              if (state is SaveNewPin) {
                dismissDailog();
                Navigator.pop(context);
              }
            },
            builder: (context, state) {
              if (state is SettingsInitial) {
                return Column(
                  children: [
                    SizedBox(
                      height: 50.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.current_pin_code.tr(),
                      controller: _currentPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.new_pin_code.tr(),
                      controller: _newPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.confirm_pin_code.tr(),
                      controller: _confirmPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        _bloc.add(CheckPinCodesEvent(
                            _currentPinCodeController.value.text,
                            _newPinCodeController.value.text,
                            _confirmPinCodeController.value.text));
                      },
                      child: _button(State),
                      color: themeIdentify(context)?cBlueLight:cFirstColor,
                      minWidth: 360.w,
                      height: 44.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r)),
                    ),
                  ],
                );
              } else if (state is CurrentPiCodeMismatch) {
                return Column(
                  children: [
                    SizedBox(
                      height: 30.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.current_pin_code.tr(),
                      controller: _currentPinCodeController,
                      isError: true,
                      errorMessage: LocaleKeys.current_pin_code_wring.tr(),
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.new_pin_code.tr(),
                      controller: _newPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.confirm_pin_code.tr(),
                      controller: _confirmPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 15.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        _bloc.add(CheckPinCodesEvent(
                            _currentPinCodeController.value.text,
                            _newPinCodeController.value.text,
                            _confirmPinCodeController.value.text));
                      },
                      child: _button(State),
                      color: ColorManager.primary,
                      minWidth: 360.w,
                      height: 50.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r)),
                    ),
                  ],
                );
              } else if (state is NewPinCodeMismatch) {
                return Column(
                  children: [
                    SizedBox(
                      height: 30.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.current_pin_code.tr(),
                      controller: _currentPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.new_pin_code.tr(),
                      controller: _newPinCodeController,
                      isError: true,
                      errorMessage: state.message,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.confirm_pin_code.tr(),
                      controller: _confirmPinCodeController,
                      isError: true,
                      errorMessage: state.message,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 15.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        _bloc.add(CheckPinCodesEvent(
                            _currentPinCodeController.value.text,
                            _newPinCodeController.value.text,
                            _confirmPinCodeController.value.text));
                      },
                      child: _button(State),
                      color: ColorManager.primary,
                      minWidth: 360.w,
                      height: 50.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r)),
                    ),
                  ],
                );
              } else if (state is AllPinCodesMatched) {
                return Column(
                  children: [
                    SizedBox(
                      height: 30.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.current_pin_code.tr(),
                      controller: _currentPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.new_pin_code.tr(),
                      controller: _newPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.confirm_pin_code.tr(),
                      controller: _confirmPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 15.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        _bloc.add(CheckPinCodesEvent(
                            _currentPinCodeController.value.text,
                            _newPinCodeController.value.text,
                            _confirmPinCodeController.value.text));
                      },
                      child: _button(State),
                      color: ColorManager.primary,
                      minWidth: 360.w,
                      height: 50.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r)),
                    ),
                  ],
                );
              } else if (state is PinCodeFieldEmpty) {
                return Column(
                  children: [
                    SizedBox(
                      height: 30.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.current_pin_code.tr(),
                      controller: _currentPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.new_pin_code.tr(),
                      controller: _newPinCodeController,
                      isError: state.isError1,
                      errorMessage: state.message1,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.confirm_pin_code.tr(),
                      controller: _confirmPinCodeController,
                      isError: state.isError2,
                      errorMessage: state.message2,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 15.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        _bloc.add(CheckPinCodesEvent(
                            _currentPinCodeController.value.text,
                            _newPinCodeController.value.text,
                            _confirmPinCodeController.value.text));
                      },
                      child: _button(State),
                      color: ColorManager.primary,
                      minWidth: 360.w,
                      height: 50.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r)),
                    ),
                  ],
                );
              } else if (state is CheckPinCodeInitial) {
                return Column(
                  children: [
                    SizedBox(
                      height: 30.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.current_pin_code.tr(),
                      controller: _currentPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.new_pin_code.tr(),
                      controller: _newPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.confirm_pin_code.tr(),
                      controller: _confirmPinCodeController,
                      bloc: _bloc,
                    ),
                    SizedBox(
                      height: 15.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        _bloc.add(CheckPinCodesEvent(
                            _currentPinCodeController.value.text,
                            _newPinCodeController.value.text,
                            _confirmPinCodeController.value.text));
                      },
                      child: _button(State),
                      color: ColorManager.primary,
                      minWidth: 360.w,
                      height: 50.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r)),
                    ),
                  ],
                );
              } else {
                return Container(
                  child: Text('Empty'),
                );
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _button(state) {
    if (state is State) {
      return const CupertinoActivityIndicator();
    } else {
      return Text(
        "OK",
        style: TextStyle(
          fontSize: 14.sp,
          fontFamily: Assets.fontsNunitoSansRegular,
        ),
      );
    }
  }
}
