import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../di/dependency_injection.dart';
import '../bloc/settings_bloc.dart';

class SessionsPage extends StatefulWidget {
  const SessionsPage({Key? key}) : super(key: key);

  static Widget screen() => BlocProvider<SettingsBloc>(
        create: (context) => di<SettingsBloc>(),
        child: SessionsPage(),
      );

  @override
  State<SessionsPage> createState() => _SessionsPageState();
}

class _SessionsPageState extends State<SessionsPage> {
  late SettingsBloc _bloc;
  final SharedPreferences sharedPreferences = di();
  bool isDark = false;
  List<String> ids = [];

  String deviceName = 'unknown';

  @override
  void initState() {
    getDeviceName().then((value) {
      setState(() {
        deviceName = value;
      });
    });

    _bloc = BlocProvider.of<SettingsBloc>(context);
    isDark = sharedPreferences.getString(theme_pref) == 'ThemeMode.dark'
        ? true
        : false;

    _handleRefresh(true);

    super.initState();
  }

  Future _handleRefresh(bool refresh) async {
    _bloc.add(GetSessionEvent(refesh: refresh));
  }

  Future _onlineRefresh() async {
    _bloc.add(GetSessionEvent(refesh: true));
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      backgroundColor: isDark == true ? cBackDarkColor2 : cWhiteColor,
      appBar: AppBar(
        elevation: 0,
        title: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w),
          child: Text(
            LocaleKeys.active_session.tr(),
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _onlineRefresh,
        child: ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(
            dragDevices: {
              PointerDeviceKind.touch,
              PointerDeviceKind.mouse,
            },
          ),
          child: ListView(
            children: [
              SizedBox(
                height: 35.h,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 18.w),
                child: Text(
                  LocaleKeys.current_session.tr(),
                  style: TextStyle(
                      fontSize: 18.sp,
                      color: isDark ? cBlueLight : cFirstColor,
                      fontWeight: FontWeight.w600),
                ),
              ),
              SizedBox(
                height: 18.h,
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 20.w),
                height: 60.h,
                width: MediaQuery.of(context).size.width,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      deviceName,
                      style: TextStyle(
                          fontSize: 16.sp, fontWeight: FontWeight.w600),
                    ),
                    Text(
                      getPlatformName(),
                      style: TextStyle(
                        fontSize: 14.sp,
                      ),
                    ),
                    Container(
                      height: 0.2.h,
                      color: isDark ? cWhiteColor : cBackDarkColor2,
                    ),
                  ],
                ),
              ),
              BlocConsumer<SettingsBloc, SettingsState>(
                listener: (context, state) {
                  if (state is SessionSuccess) {
                  }
                },
                builder: (context, state) {
                  if (state is SessionLoading) {
                    return Container(
                      margin: EdgeInsets.symmetric(horizontal: 20.w),
                      height: 58.h,
                      width: MediaQuery.of(context).size.width,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  } else if (state is SessionSuccess) {
                    return InkWell(
                      onTap: () {
                        showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                backgroundColor: isDark == true
                                    ? cFirstColorDark
                                    : cWhiteColor,
                                title: Text(
                                  LocaleKeys.stop_other_sessions.tr(),
                                  style: TextStyle(
                                      color:
                                          isDark ? cGrayTextColor : cBlackColor,
                                      fontSize: 16.sp),
                                ),
                                content: Text(
                                  LocaleKeys.stop_alert.tr(),
                                  style: TextStyle(
                                      color: cGrayTextColor, fontSize: 14.sp),
                                ),
                                actions: [
                                  TextButton(
                                      onPressed: () {
                                       Navigator.pop(context);
                                      },
                                      child: Text(
                                        LocaleKeys.cancel.tr(),
                                        style: TextStyle(
                                            fontSize: 16.sp,
                                            color: isDark
                                                ? cWhiteColor
                                                : cBlackColor),
                                      )),
                                  TextButton(
                                      onPressed: () {
                                        ids.clear();
                                        state.list.forEach((element) {
                                          ids.add(element.id ?? '');
                                        });
                                        Navigator.pop(context);
                                        _bloc.add(DeactivateUsersEvent(ids));
                                      },
                                      child: Text('OK',
                                          style: TextStyle(
                                              fontSize: 16.sp,
                                              color: cFirstColor))),
                                ],
                              );
                            }).then((value) {
                          //_bloc.add(CheckPinCodeInitialEvent());
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 20.w),
                        height: 54.h,
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.block,
                              color: cRedColor,
                            ),
                            SizedBox(
                              width: 20.w,
                            ),
                            Text(
                              LocaleKeys.stop_other_sessions.tr(),
                              style:
                                  TextStyle(color: cRedColor, fontSize: 16.sp),
                            )
                          ],
                        ),
                      ),
                    );
                  } else {
                    return InkWell(
                      onTap: () {
                        showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                backgroundColor: isDark == true
                                    ? cFirstColorDark
                                    : cWhiteColor,
                                title: Text(
                                  LocaleKeys.stop_other_sessions.tr(),
                                  style: TextStyle(
                                      color:
                                          isDark ? cGrayTextColor : cBlackColor,
                                      fontSize: 16.sp),
                                ),
                                content: Text(
                                  LocaleKeys.stop_alert.tr(),
                                  style: TextStyle(
                                      color: cGrayTextColor, fontSize: 14.sp),
                                ),
                                actions: [
                                  TextButton(
                                      onPressed: () {
                                        setState(() {
                                          Navigator.pop(context);
                                        });
                                      },
                                      child: Text(
                                        LocaleKeys.cancel.tr(),
                                        style: TextStyle(
                                            fontSize: 16.sp,
                                            color: isDark
                                                ? cWhiteColor
                                                : cBlackColor),
                                      )),
                                  TextButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                      child: Text('OK',
                                          style: TextStyle(
                                              fontSize: 16.sp,
                                              color: cFirstColor))),
                                ],
                              );
                            }).then((value) {
                          //_bloc.add(CheckPinCodeInitialEvent());
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 20.w),
                        height: 54.h,
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.block,
                              color: cRedColor,
                            ),
                            SizedBox(
                              width: 20.w,
                            ),
                            Text(
                              LocaleKeys.stop_other_sessions.tr(),
                              style:
                                  TextStyle(color: cRedColor, fontSize: 16.sp),
                            )
                          ],
                        ),
                      ),
                    );
                  }
                },
              ),
              SizedBox(
                height: 2.h,
              ),
              Container(
                height: 30.h,
                width: MediaQuery.of(context).size.width,
                color: isDark ? backgroundDark : cGrayColor.withAlpha(40),
              ),
              SizedBox(
                height: 20.h,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 18.w),
                child: Text(
                  LocaleKeys.active_session.tr(),
                  style: TextStyle(
                      fontSize: 18.sp,
                      color: isDark ? cBlueLight : cFirstColor,
                      fontWeight: FontWeight.w600),
                ),
              ),
              BlocConsumer<SettingsBloc, SettingsState>(
                listener: (context, state) {
                  if (state is DeactivateUserSuccessState) {
                    _handleRefresh(true);
                  } else if (state is DeactivateUserAllSuccess) {
                    _handleRefresh(true);
                  }
                },
                builder: (context, state) {
                  if (state is SessionSuccess) {
                    return ListView.separated(
                        padding: EdgeInsets.symmetric(horizontal: 18.w),
                        separatorBuilder: (context, index) {
                          return Container(
                            height: 0.2.h,
                            color: isDark ? cWhiteColor : cBackDarkColor2,
                          );
                        },
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: state.list.length,
                        itemBuilder: (BuildContext context, int index) {
                          return InkWell(
                            onTap: () {
                              showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return AlertDialog(
                                      backgroundColor: isDark == true
                                          ? cBackDarkColor2
                                          : cWhiteColor,
                                      title: Text(
                                        LocaleKeys.stop_session.tr(),
                                        style: TextStyle(
                                            color: isDark
                                                ? cGrayTextColor
                                                : cBlackColor,
                                            fontSize: 16.sp),
                                      ),
                                      content: Text(
                                        LocaleKeys.stop_this_session_alert.tr(),
                                        style: TextStyle(
                                            color: cGrayTextColor,
                                            fontSize: 14.sp),
                                      ),
                                      actions: [
                                        TextButton(
                                            onPressed: () {
                                              Navigator.pop(context);
                                            },
                                            child: Text(
                                              LocaleKeys.cancel.tr(),
                                              style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: isDark
                                                      ? cWhiteColor
                                                      : cBlackColor),
                                            )),
                                        TextButton(
                                            onPressed: () {
                                              Navigator.pop(context);
                                              ids.add(
                                                  state.list[index].id ?? "");
                                              _bloc.add(
                                                  DeactivateUsersEvent(ids));
                                            },
                                            child: Text('OK',
                                                style: TextStyle(
                                                    fontSize: 16.sp,
                                                    color: cFirstColor))),
                                      ],
                                    );
                                  }).then((value) {
                                //_bloc.add(CheckPinCodeInitialEvent());
                              });
                            },
                            child: Container(
                              height: 70.h,
                              width: MediaQuery.of(context).size.width,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    state.list[index].deviceName.toString(),
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w600),
                                  ),
                                  Text(
                                    state.list[index].deviceVersion.toString(),
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        });
                  } else if (state is SessionLoading) {
                    return Center(
                        child: CupertinoActivityIndicator(
                      color: cFirstColor,
                    ));
                  } else {
                    return SizedBox();
                  }
                },
              )
            ],
          ),
        ),
      ),
    ));
  }
}
