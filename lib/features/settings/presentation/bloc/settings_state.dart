part of 'settings_bloc.dart';

@immutable
abstract class SettingsState extends Equatable {}

class SettingsInitial extends SettingsState {
  @override
  List<Object> get props => [];
}

class SaveSelectedLang extends SettingsState {
  final String selectedLanguage;

  SaveSelectedLang(this.selectedLanguage);

  @override
  List<Object> get props => [selectedLanguage];
}

class CheckPinCodeInitial extends SettingsState {
  @override
  List<Object> get props => [];
}

class CheckPinCodes extends SettingsState {
  final String currentPinCode;
  final String newPinCode;
  final String confirmPinCode;

  CheckPinCodes(this.currentPinCode, this.newPinCode, this.confirmPinCode);

  @override
  List<Object> get props => [currentPinCode, newPinCode, confirmPinCode];
}

class CurrentPiCodeMismatch extends SettingsState {
  @override
  List<Object> get props => [];
}

class NewPinCodeMismatch extends SettingsState {
  String message;

  NewPinCodeMismatch(this.message);

  @override
  List<Object> get props => [];
}

class PinCodeFieldEmpty extends SettingsState {
  String message1;
  String message2;
  bool isError1;
  bool isError2;

  PinCodeFieldEmpty(this.message1, this.message2, this.isError1, this.isError2);

  @override
  List<Object> get props => [];
}

class AllPinCodesMatched extends SettingsState {
  @override
  List<Object> get props => [];
}

class SaveNewPin extends SettingsState {
  final bool isSuccess;

  SaveNewPin(this.isSuccess);

  @override
  List<Object> get props => [isSuccess];
}

///loading
class SessionLoading extends SettingsState {
  @override
  List<Object> get props => [];
}

class SessionSuccess extends SettingsState {
  final List<SessionModel> list;

  SessionSuccess({required this.list});

  @override
  List<Object> get props => [list];
}

class SessionFailure extends SettingsState {
  final String message;

  SessionFailure({required this.message});

  @override
  List<Object> get props => [message];
}

class DeactivateUserState extends SettingsState {
  final int id;

  DeactivateUserState(this.id);

  @override
  List<Object> get props => [id];
}

class DeactivateUserSuccessState extends SettingsState {
  @override
  List<Object> get props => [];
}

class DeactivateFailure extends SettingsState {
  final String message;

  DeactivateFailure(this.message);

  @override
  List<Object> get props => [message];
}

class DeactivateUserAll extends SettingsState {
  @override
  List<Object> get props => [];
}

class DeactivateUserAllSuccess extends SettingsState {
  @override
  List<Object> get props => [];
}

class DeactivateUserAllFailure extends SettingsState {
  @override
  List<Object> get props => [];
}
