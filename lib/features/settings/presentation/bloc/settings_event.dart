part of 'settings_bloc.dart';

@immutable
abstract class SettingsEvent extends Equatable {}

class SaveSelectedLangEvent extends SettingsEvent {
  final String selectedLanguage;

  SaveSelectedLangEvent(this.selectedLanguage);

  @override
  List<Object> get props => [selectedLanguage];
}

class CheckPinCodeInitialEvent extends SettingsEvent {
  @override
  List<Object> get props => [];
}

class CheckPinCodesEvent extends SettingsEvent {
  final String currentPinCode;
  final String newPinCode;
  final String confirmPinCode;

  CheckPinCodesEvent(this.currentPinCode, this.newPinCode, this.confirmPinCode);

  @override
  List<Object> get props => [currentPinCode, newPinCode, confirmPinCode];
}

class PinCodeFieldEmptyEvent extends SettingsEvent {
  String message1;
  String message2;
  bool isError1;
  bool isError2;

  PinCodeFieldEmptyEvent(
      this.message1, this.message2, this.isError1, this.isError2);

  @override
  List<Object> get props => [];
}

class SaveNewPinEvent extends SettingsEvent {
  final String newPin;

  SaveNewPinEvent(this.newPin);

  @override
  List<Object> get props => [newPin];
}

class GetSessionEvent extends SettingsEvent {
  final bool refesh;

  GetSessionEvent({required this.refesh});

  @override
  List<Object> get props => [refesh];
}

class DeactivateUsersEvent extends SettingsEvent {
  final List<String> ids;

  DeactivateUsersEvent(this.ids);

  @override
  List<Object> get props => [ids];
}
