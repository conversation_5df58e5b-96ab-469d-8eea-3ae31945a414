import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/errors/failures.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/features/settings/data/datasources/settings_local_datasources.dart';
import 'package:ijrochi/features/settings/data/datasources/settings_remote_datasources.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:meta/meta.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/utils/app_constants.dart';
import '../../data/models/session_model.dart';

part 'settings_event.dart';

part 'settings_state.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  final SettingsLocalDataSources settingsLocalDataSources;
  final SettingsRemoteDataSources settingsRemoteDataSources;
  final NetworkInfo networkInfo;
  final SharedPreferences sharedPreferences;
  final Dio dio;

  SettingsBloc(
      {required this.settingsLocalDataSources,
      required this.settingsRemoteDataSources,
      required this.networkInfo,
      required this.sharedPreferences,
      required this.dio})
      : super(SettingsInitial()) {
    on<SettingsEvent>((event, emit) {});
    on<CheckPinCodeInitialEvent>((event, emit) {
      emit(CheckPinCodeInitial());
    });
    on<CheckPinCodesEvent>(checkPinCodes, transformer: droppable());
    on<SaveNewPinEvent>(saveNewPin, transformer: droppable());
    on<GetSessionEvent>(getSession, transformer: droppable());
    on<DeactivateUsersEvent>(deactivateSession, transformer: droppable());
  }

  FutureOr<void> checkPinCodes(
      CheckPinCodesEvent event, Emitter<SettingsState> emit) async {
    String savedPinCode = sharedPreferences.getString(pin_code_pref) ?? '';
    if (savedPinCode != event.currentPinCode) {
      emit(CurrentPiCodeMismatch());
    } else if (event.newPinCode.isEmpty) {
      emit(PinCodeFieldEmpty(LocaleKeys.need_to_fill.tr(), '', true, false));
    } else if (event.newPinCode.length < 4) {
      emit(PinCodeFieldEmpty(LocaleKeys.need_to_fill.tr(), '', true, false));
    } else if (event.confirmPinCode.isEmpty) {
      emit(PinCodeFieldEmpty('', LocaleKeys.need_to_fill.tr(), false, true));
    } else if (event.confirmPinCode.length < 4) {
      emit(PinCodeFieldEmpty('', LocaleKeys.need_to_fill.tr(), false, true));
    } else if (event.newPinCode != event.confirmPinCode) {
      emit(NewPinCodeMismatch(LocaleKeys.pin_codes_mismatch.tr()));
    } else {
      emit(AllPinCodesMatched());
    }
  }

  FutureOr<void> saveNewPin(
      SaveNewPinEvent event, Emitter<SettingsState> emit) async {
    try {
      await sharedPreferences.setString(pin_code_pref, event.newPin);
      emit(SaveNewPin(true));
    } on LocalFailure {
      emit(SaveNewPin(false));
    }
  }

  ///loading sessions
  FutureOr<void> getSession(
      GetSessionEvent event, Emitter<SettingsState> emit) async {
    emit(SessionLoading());

    if (await networkInfo.isConnected) {
      try {
        List<SessionModel> list =
            await settingsRemoteDataSources.getSessionModels();
        settingsLocalDataSources.setSessionModel(list);
        emit(SessionSuccess(list: list));
      } on DioException catch (e) {
        emit(SessionFailure(message: LocaleKeys.error.tr()));
      } catch (e) {
        emit(SessionFailure(message: LocaleKeys.error.tr()));
      }
    } else {
      List<SessionModel> list =
          await settingsLocalDataSources.getSessionModel();
      print(list);
      emit(SessionSuccess(list: list));
    }
  }

  deactivateSession(
      DeactivateUsersEvent event, Emitter<SettingsState> emit) async {
    emit(SessionLoading());
    if (await networkInfo.isConnected) {
      try {
        var response =
            await dio.post(deactivateSessions, data: {"sessionIds": event.ids});
        if (response.statusCode == 200) {
          add(GetSessionEvent(refesh: true));
        }
      } on DioException catch (e) {
        emit(SessionFailure(message: LocaleKeys.error.tr()));
      } catch (e) {
        emit(SessionFailure(message: LocaleKeys.error.tr()));
      }
    } else {
      CustomToast.showToast(LocaleKeys.no_internet.tr());
      List<SessionModel> list =
          await settingsLocalDataSources.getSessionModel();
      emit(SessionSuccess(list: list));
    }
  }
}
