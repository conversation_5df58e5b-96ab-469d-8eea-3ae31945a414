import 'dart:async';
import 'dart:io';

import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:system_tray/system_tray.dart';

const backgroundStartColor = cFirstColor;
const backgroundEndColor = cSecondColor;

class TitleBar extends StatelessWidget {
  const TitleBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WindowTitleBarBox(
      child: Container(
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(width: 0.5.w, color: cFirstColor)),
          gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [backgroundStartColor, backgroundEndColor],
              stops: [0.0, 1.0]),
        ),
        child: Row(
          children: [
            Expanded(
              child: MoveWindow(
                onDoubleTap: () {},
                child: Scaffold(
                    backgroundColor: Colors.transparent,
                    body: Center(child: Text("\"Ижрочи\" - www.ijrochi.uz"))),
              ),
            ),
            const WindowButtons()
          ],
        ),
      ),
    );
  }
}

final buttonColors = WindowButtonColors(
    iconNormal: const Color(0xFF132A33),
    mouseOver: const Color(0xFF3A61A1),
    mouseDown: const Color(0xFF2C4272),
    iconMouseOver: Colors.white,
    iconMouseDown: const Color(0xFF366383));

final closeButtonColors = WindowButtonColors(
    mouseOver: const Color(0xFFD32F2F),
    mouseDown: const Color(0xFFB71C1C),
    iconNormal: const Color(0xFF132A33),
    iconMouseOver: Colors.white);

class WindowButtons extends StatelessWidget {
  const WindowButtons({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        MinimizeWindowButton(colors: buttonColors),
        // MaximizeWindowButton(colors: buttonColors),
        CloseWindowButton(
          colors: closeButtonColors,
          onPressed: () {
            showDialog<void>(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('Dasturni yopmoqchimisiz?'),
                  content: const Text(
                      ('Dastur fon rejimida ishlashni davom etadi, chiqish uchun treydan dasturni yoping.')),
                  actions: <Widget>[
                    TextButton(
                      child: const Text('OK'),
                      onPressed: () {
                        Navigator.of(context).pop();
                        appWindow.hide();
                      },
                    ),
                  ],
                );
              },
            );
          },
        ),
      ],
    );
  }
}

Future<void> initSystemTray(Timer? _timer) async {
  ///For Desktop
  final SystemTray _systemTray = SystemTray();
  final AppWindow _appWindow = AppWindow();
  final Menu _menuSimple = Menu();

  // We first init the systray menu and then add the menu entries
  await _systemTray.initSystemTray(iconPath: getTrayImagePath('app_icon'));
  _systemTray.setTitle("Ijrochi servis");
  _systemTray.setToolTip("Ijrochi fonda ishlamoqda...");

  // handle system tray event
  _systemTray.registerSystemTrayEventHandler((eventName) {
    debugPrint("eventName: $eventName");
    if (eventName == kSystemTrayEventClick) {
      Platform.isWindows ? _appWindow.show() : _systemTray.popUpContextMenu();
    } else if (eventName == kSystemTrayEventRightClick) {
      Platform.isWindows ? _systemTray.popUpContextMenu() : _appWindow.show();
    }
  });

  await _menuSimple.buildFrom([
    MenuItemLabel(
        label: 'Ochish',
        image: getImagePath('app_icon'),
        onClicked: (menuItem) => _appWindow.show()),
    MenuItemLabel(
        label: 'Yashirish',
        image: getImagePath('app_icon'),
        onClicked: (menuItem) => _appWindow.hide()),
    MenuItemLabel(
      label: 'Chiqish',
      image: getImagePath('app_icon'),
      onClicked: (menuItem) => _appWindow.close(),
    ),
  ]);

  _systemTray.setContextMenu(_menuSimple);
}
