import 'package:dio/dio.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/features/notifications/notification_count/models/notification_count.dart';

abstract class NotificationCountRemoteDatasource {
  Future<NotificationCount> getNotification(bool archive);
}

class NotificationCountRemoteDatasourceImpl extends NotificationCountRemoteDatasource {
  final Dio dio;

  NotificationCountRemoteDatasourceImpl({required this.dio});

  @override
  Future<NotificationCount> getNotification(bool archive) async {
    var response = await dio
        .get(getNotificationCountPath, queryParameters: {"archive": archive});
    if (response.statusCode == 200) {
      return NotificationCount.fromJson(
          response.data, archiveToStatus(archive));
    } else {
      throw DioException(
          requestOptions: response.requestOptions, response: response);
    }
  }
}
