import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/features/notifications/notification_count/models/notification_count.dart';
import 'package:isar/isar.dart';

abstract class NotificationCountLocalDatasource {
  Future<NotificationCount?> getNotification(bool? archive);

  Future<void> setNotification(NotificationCount notificationCount);
}

class NotificationCountLocalDatasourceImpl extends NotificationCountLocalDatasource {
  final IsarService isarService;

  NotificationCountLocalDatasourceImpl({required this.isarService});

  @override
  Future<NotificationCount?> getNotification(bool? archive) {
    final notificationCount = isarService.isar.notificationCounts
        .filter()
        .statusEqualTo(archiveToStatus(archive))
        .findFirst();
    return notificationCount;
  }

  @override
  Future<void> setNotification(NotificationCount notificationCount) async {
    try {
      await isarService.isar.writeTxn(() async {
        await isarService.isar.notificationCounts
            .filter()
            .statusEqualTo(notificationCount.status)
            .deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        await isarService.isar.notificationCounts.put(notificationCount);
      });
    } catch (e) {
      throw Exception();
    }
  }
}
