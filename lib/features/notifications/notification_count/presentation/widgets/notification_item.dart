import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:html/parser.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:intl/intl.dart';

class NotificationItem extends StatefulWidget {
  final NotificationDocs notificationDoc;
  final bool visible;
  final Function(NotificationDocs notificationDoc) onTapItem;

  const NotificationItem(
      {Key? key,
      required this.notificationDoc,
      required this.visible,
      required this.onTapItem})
      : super(key: key);

  @override
  State<NotificationItem> createState() => _NotificationItemState();
}

class _NotificationItemState extends State<NotificationItem> {
  final DateFormat formatterHour = DateFormat('HH:mm');
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  late bool isDark;

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return InkWell(
      onTap: () {
        widget.onTapItem(widget.notificationDoc);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Visibility(
            visible: widget.visible,
            child: Container(
              margin: EdgeInsets.only(top: 10.h),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: isDark ? cDateBackgroundDark : cDateBackgroundLight,
                  borderRadius: BorderRadius.circular(30.r)),
              width: 100.w,
              height: 26.h,
              child: Text(
                formatterDate.format(DateTime.parse(
                    widget.notificationDoc.createdAt.toString())),
                style: TextStyle(
                  color: themeIdentify(context) ? cWhiteColor : cBlackColor,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Card(
            margin: EdgeInsets.symmetric(horizontal: 15.w),
            elevation: 1,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r)),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: themeIdentify(context) ? cBackDarkColor2 : cWhiteColor,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: 15.w,
                        ),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
                          decoration: BoxDecoration(color:  statusToColor(widget.notificationDoc.status), borderRadius: BorderRadius.all(Radius.circular(15.r))),
                          child: Text(
                            '${LocaleKeys.notification.tr()} № ${widget.notificationDoc.notification?.serialNumber}',
                            style: TextStyle(
                                fontSize: 12.sp,
                                color:
                                   cWhiteColor,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(right: 15.w, top: 8.sp),
                        child: Row(
                          children: [
                            Text(
                              formatterHour.format(DateTime.parse(
                                      widget.notificationDoc.createdAt ?? "")
                                  .add(Duration(hours: 5))),
                              style: TextStyle(
                                  color: cGrayTextColor, fontSize: 14.sp),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${LocaleKeys.send_organisation.tr()}:',
                          style:
                              TextStyle(fontSize: 14.sp, color: cGrayTextColor),
                        ),
                        Flexible(
                          child: Text(
                            "${widget.notificationDoc.notification?.moderator?.fullName}",
                            style: TextStyle(
                              fontSize: 14.sp,
                            ),
                            textAlign: TextAlign.right,
                          ),
                        )
                      ],
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: cGrayColor,
                    indent: 14.w,
                    endIndent: 14.w,
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 14.w, top: 12.h),
                    child: Text(
                      widget.notificationDoc.notification?.title ??
                          LocaleKeys.unknown.tr(),
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? cWhiteColor
                              : cFirstColorDark,
                          fontSize: 16.sp),
                    ),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Padding(
                      padding: EdgeInsets.only(left: 14.w, right: 16.w),
                      child: Text(
                        parse(widget.notificationDoc.notification?.content??"<html lang='en'>...</html>")
                                .documentElement
                                ?.text ??
                            "",
                        style: TextStyle(
                            fontSize: 14.sp,
                            overflow: TextOverflow.ellipsis,
                            color: cGrayTextColor),
                        maxLines: 3,
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
