import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/notifications/notification/model/notification_enum.dart';
import 'package:ijrochi/features/notifications/notification/presentation/bloc/notification_bloc.dart';
import 'package:ijrochi/features/notifications/notification/presentation/page/notification_page.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/notification_bloc/notification_count_bloc.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/pages/filter_page.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

class NotificationParentPage extends StatefulWidget {
  final String categoryName;
  final String title;
  final bool? archive;

  const NotificationParentPage(
      {Key? key,
      required this.categoryName,
      required this.title,
      required this.archive})
      : super(key: key);

  static Widget screen(
      {required String categoryName,
      required String title,
      required bool? archive}) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => di<NotificationCountBloc>()),
        BlocProvider(create: (context) => di<NotificationBloc>())
      ],
      child: NotificationParentPage(
        categoryName: categoryName,
        title: title,
        archive: archive,
      ),
    );
  }

  @override
  State<NotificationParentPage> createState() => _NotificationParentPageState();
}

class _NotificationParentPageState extends State<NotificationParentPage>
    with SingleTickerProviderStateMixin {
  late bool isDark;
  late TabController _tabController;
  List<Widget> pages = [];
  late NotificationBloc notificationBloc;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(vsync: this, initialIndex: 0, length: 5);

    notificationBloc = BlocProvider.of<NotificationBloc>(context);
    pages.add(NotificationPage(
      status: NotificationEnumStatus.all,
      archive: widget.archive,
      bloc: notificationBloc,
      statusNumber: null,
    ));
    pages.add(NotificationPage(
      status: NotificationEnumStatus.fast,
      archive: widget.archive,
      bloc: notificationBloc,
      statusNumber: 1,
    ));
    pages.add(NotificationPage(
      status: NotificationEnumStatus.simple,
      archive: widget.archive,
      bloc: notificationBloc,
      statusNumber: 2,
    ));
    pages.add(NotificationPage(
      status: NotificationEnumStatus.important,
      archive: widget.archive,
      bloc: notificationBloc,
      statusNumber: 3,
    ));
    pages.add(NotificationPage(
      status: NotificationEnumStatus.veryImportant,
      archive: widget.archive,
      bloc: notificationBloc,
      statusNumber: 4,
    ));
  }

  @override
  Widget build(BuildContext context) {
    BlocProvider.of<NotificationCountBloc>(context)
        .add(GetNotificationCountEvent(archive: widget.archive));
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
        appBar: AppBar(
            elevation: 2,
            title: Center(
              child: Column(
                children: [
                  Text(
                    widget.title,
                    style:
                        TextStyle(fontWeight: FontWeight.w500, fontSize: 16.sp),
                  ),
                ],
              ),
            ),
            actions: [
              InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () async {
                  showModalBottomSheet(
                      context: context,
                      backgroundColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                              topRight: Radius.circular(20.r),
                              topLeft: Radius.circular(20.r))),
                      builder: (BuildContext context) {
                        return FilterPage(
                          onFilterTap: (value) {
                            notificationBloc.add(FilterEvent(
                                archive: widget.archive,
                                status: indexToStatus(_tabController.index),
                                date: value.date,
                                moderator: value.moderator?.id,
                                statusNumber: null));
                          },
                        );
                      });
                },
                child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 15.w),
                    child: Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: SvgPicture.asset(
                        Assets.iconsFilter,
                        color: isDark ? cWhiteColor : cFirstColor,
                      ),
                    )),
              )
            ],
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(40.h),
              child:
                  BlocConsumer<NotificationCountBloc, NotificationCountState>(
                listener: (context, state) {
                  // TODO: implement listener
                },
                builder: (context, state) {
                  if (state.status == NotificationCountStatus.success) {
                    return TabBar(
                        isScrollable: true,
                        controller: _tabController,
                        tabs: [
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.all.tr()}(${state.notificationCount?.all ?? 0})',
                              style: TextStyle(
                                  color: isDark ? cWhiteColor : cBlackColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.fast.tr()}(${state.notificationCount?.fast ?? 0})',
                              style: TextStyle(
                                  color: fastTabColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.simple.tr()}(${state.notificationCount?.simple ?? 0})',
                              style: TextStyle(
                                  color: simpleTabColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.important.tr()}(${state.notificationCount?.important ?? 0})',
                              style: TextStyle(
                                  color: importantTabColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.very_important.tr()}(${state.notificationCount?.veryImportant ?? 0})',
                              style: TextStyle(
                                  color: veryImportantTabColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                        ]);
                  } else if (state.status == NotificationCountStatus.loading) {
                    return Shimmer(
                      color: cBackDarkColor2,
                        child: Container(
                          height: 40.h,
                          color: isDark
                              ?cBackDarkColor2: cGrayColor.withOpacity(.2),
                        ),
                        interval: Duration(seconds: 5));
                  } else if (state.status == NotificationCountStatus.failure) {
                    return Text(state.message.toString());
                  } else {
                    return SizedBox(
                      height: 40.h,
                    );
                  }
                },
              ),
            )),
        body: TabBarView(
          children: pages,
          controller: _tabController,
        ));
  }
}
