part of 'notification_count_bloc.dart';

enum NotificationCountStatus { initial, loading, failure, success,noInternet}

class NotificationCountState extends Equatable {
  final NotificationCountStatus status;
  final String? message;
  final NotificationCount? notificationCount;

  NotificationCountState(
      {required this.status, this.message, this.notificationCount});

  static NotificationCountState initial() =>
      NotificationCountState(status: NotificationCountStatus.initial);

  NotificationCountState copyWith({NotificationCountStatus? status, String? message,
    NotificationCount? notificationCount}) =>
      NotificationCountState(
          status: status ?? this.status,
          message: message ?? this.message,
          notificationCount: notificationCount ?? this.notificationCount);

  @override
  List<Object?> get props => [status, message, notificationCount];
}
