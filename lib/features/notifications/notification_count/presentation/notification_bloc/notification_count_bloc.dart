import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/features/notifications/notification_count/datasources/notification_count_local_datasource.dart';
import 'package:ijrochi/features/notifications/notification_count/datasources/notification_count_remote_datasource.dart';
import 'package:ijrochi/features/notifications/notification_count/models/notification_count.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

part 'notification_count_event.dart';

part 'notification_count_state.dart';

class NotificationCountBloc
    extends Bloc<NotificationCountEvent, NotificationCountState> {
  final NotificationCountLocalDatasource notificationLocalDatasource;
  final NotificationCountRemoteDatasource notificationRemoteDatasource;
  final NetworkInfo networkInfo;

  NotificationCountBloc(
      {required this.notificationLocalDatasource,
      required this.notificationRemoteDatasource,
      required this.networkInfo})
      : super(NotificationCountState.initial()) {
    on<GetNotificationCountEvent>(getNotificationCounts,
        transformer: droppable());
  }

  FutureOr<void> getNotificationCounts(GetNotificationCountEvent event,
      Emitter<NotificationCountState> emit) async {
    if (event.archive == null) {
      NotificationCount notificationCount = NotificationCount(
          all: await getScheduledNotificationCount(all: true),
          fast: await getScheduledNotificationCount(all: false, status: 1),
          simple: await getScheduledNotificationCount(all: false, status: 2),
          important: await getScheduledNotificationCount(all: false, status: 3),
          veryImportant:
              await getScheduledNotificationCount(all: false, status: 4));
      emit(state.copyWith(
          status: NotificationCountStatus.success,
          notificationCount: notificationCount));
    } else {
      if (await networkInfo.isConnected) {
        emit(state.copyWith(status: NotificationCountStatus.loading));
        try {
          NotificationCount notificationCount =
              await notificationRemoteDatasource
                  .getNotification(event.archive ?? true);
          notificationLocalDatasource.setNotification(notificationCount);
          emit(state.copyWith(
              status: NotificationCountStatus.success,
              notificationCount: notificationCount));
        } on DioException catch (e) {
          emit(state.copyWith(
              status: NotificationCountStatus.failure,
              message: LocaleKeys.error.tr()));
        } catch (e) {
          emit(state.copyWith(
              status: NotificationCountStatus.failure,
              message: LocaleKeys.error.tr()));
        }
      } else {
        try {
          NotificationCount? notificationCount =
              await notificationLocalDatasource.getNotification(event.archive);
          emit(state.copyWith(
              status: NotificationCountStatus.success,
              notificationCount: notificationCount));
        } catch (e) {
          emit(state.copyWith(
              status: NotificationCountStatus.noInternet,
              message: LocaleKeys.no_internet.tr()));
        }
      }
    }
  }
}
