// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_count.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetNotificationCountCollection on Isar {
  IsarCollection<NotificationCount> get notificationCounts => this.collection();
}

const NotificationCountSchema = CollectionSchema(
  name: r'notification_count',
  id: 4541162402887597743,
  properties: {
    r'all': PropertySchema(
      id: 0,
      name: r'all',
      type: IsarType.long,
    ),
    r'fast': PropertySchema(
      id: 1,
      name: r'fast',
      type: IsarType.long,
    ),
    r'important': PropertySchema(
      id: 2,
      name: r'important',
      type: IsarType.long,
    ),
    r'simple': PropertySchema(
      id: 3,
      name: r'simple',
      type: IsarType.long,
    ),
    r'status': PropertySchema(
      id: 4,
      name: r'status',
      type: IsarType.string,
    ),
    r'veryImportant': PropertySchema(
      id: 5,
      name: r'veryImportant',
      type: IsarType.long,
    )
  },
  estimateSize: _notificationCountEstimateSize,
  serialize: _notificationCountSerialize,
  deserialize: _notificationCountDeserialize,
  deserializeProp: _notificationCountDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _notificationCountGetId,
  getLinks: _notificationCountGetLinks,
  attach: _notificationCountAttach,
  version: '3.1.0+1',
);

int _notificationCountEstimateSize(
  NotificationCount object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.status;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _notificationCountSerialize(
  NotificationCount object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.all);
  writer.writeLong(offsets[1], object.fast);
  writer.writeLong(offsets[2], object.important);
  writer.writeLong(offsets[3], object.simple);
  writer.writeString(offsets[4], object.status);
  writer.writeLong(offsets[5], object.veryImportant);
}

NotificationCount _notificationCountDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = NotificationCount(
    all: reader.readLongOrNull(offsets[0]),
    fast: reader.readLongOrNull(offsets[1]),
    important: reader.readLongOrNull(offsets[2]),
    simple: reader.readLongOrNull(offsets[3]),
    veryImportant: reader.readLongOrNull(offsets[5]),
  );
  object.id = id;
  object.status = reader.readStringOrNull(offsets[4]);
  return object;
}

P _notificationCountDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    case 3:
      return (reader.readLongOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _notificationCountGetId(NotificationCount object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _notificationCountGetLinks(
    NotificationCount object) {
  return [];
}

void _notificationCountAttach(
    IsarCollection<dynamic> col, Id id, NotificationCount object) {
  object.id = id;
}

extension NotificationCountQueryWhereSort
    on QueryBuilder<NotificationCount, NotificationCount, QWhere> {
  QueryBuilder<NotificationCount, NotificationCount, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension NotificationCountQueryWhere
    on QueryBuilder<NotificationCount, NotificationCount, QWhereClause> {
  QueryBuilder<NotificationCount, NotificationCount, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension NotificationCountQueryFilter
    on QueryBuilder<NotificationCount, NotificationCount, QFilterCondition> {
  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      allIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'all',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      allIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'all',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      allEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'all',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      allGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'all',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      allLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'all',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      allBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'all',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      fastIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'fast',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      fastIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'fast',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      fastEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fast',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      fastGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fast',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      fastLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fast',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      fastBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fast',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      importantIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'important',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      importantIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'important',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      importantEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'important',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      importantGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'important',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      importantLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'important',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      importantBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'important',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      simpleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'simple',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      simpleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'simple',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      simpleEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'simple',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      simpleGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'simple',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      simpleLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'simple',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      simpleBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'simple',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'status',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      statusIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'status',
        value: '',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      veryImportantIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'veryImportant',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      veryImportantIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'veryImportant',
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      veryImportantEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'veryImportant',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      veryImportantGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'veryImportant',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      veryImportantLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'veryImportant',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterFilterCondition>
      veryImportantBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'veryImportant',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension NotificationCountQueryObject
    on QueryBuilder<NotificationCount, NotificationCount, QFilterCondition> {}

extension NotificationCountQueryLinks
    on QueryBuilder<NotificationCount, NotificationCount, QFilterCondition> {}

extension NotificationCountQuerySortBy
    on QueryBuilder<NotificationCount, NotificationCount, QSortBy> {
  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy> sortByAll() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'all', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByAllDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'all', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByFast() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fast', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByFastDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fast', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByImportant() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'important', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByImportantDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'important', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortBySimple() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'simple', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortBySimpleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'simple', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByVeryImportant() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veryImportant', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      sortByVeryImportantDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veryImportant', Sort.desc);
    });
  }
}

extension NotificationCountQuerySortThenBy
    on QueryBuilder<NotificationCount, NotificationCount, QSortThenBy> {
  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy> thenByAll() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'all', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByAllDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'all', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByFast() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fast', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByFastDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fast', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByImportant() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'important', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByImportantDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'important', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenBySimple() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'simple', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenBySimpleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'simple', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByVeryImportant() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veryImportant', Sort.asc);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QAfterSortBy>
      thenByVeryImportantDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veryImportant', Sort.desc);
    });
  }
}

extension NotificationCountQueryWhereDistinct
    on QueryBuilder<NotificationCount, NotificationCount, QDistinct> {
  QueryBuilder<NotificationCount, NotificationCount, QDistinct>
      distinctByAll() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'all');
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QDistinct>
      distinctByFast() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fast');
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QDistinct>
      distinctByImportant() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'important');
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QDistinct>
      distinctBySimple() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'simple');
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QDistinct>
      distinctByStatus({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotificationCount, NotificationCount, QDistinct>
      distinctByVeryImportant() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'veryImportant');
    });
  }
}

extension NotificationCountQueryProperty
    on QueryBuilder<NotificationCount, NotificationCount, QQueryProperty> {
  QueryBuilder<NotificationCount, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<NotificationCount, int?, QQueryOperations> allProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'all');
    });
  }

  QueryBuilder<NotificationCount, int?, QQueryOperations> fastProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fast');
    });
  }

  QueryBuilder<NotificationCount, int?, QQueryOperations> importantProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'important');
    });
  }

  QueryBuilder<NotificationCount, int?, QQueryOperations> simpleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'simple');
    });
  }

  QueryBuilder<NotificationCount, String?, QQueryOperations> statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }

  QueryBuilder<NotificationCount, int?, QQueryOperations>
      veryImportantProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'veryImportant');
    });
  }
}
