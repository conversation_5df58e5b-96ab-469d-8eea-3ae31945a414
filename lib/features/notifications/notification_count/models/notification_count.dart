
import 'package:isar/isar.dart';

part 'notification_count.g.dart';

@Collection()
@Name("notification_count")
class NotificationCount {
  NotificationCount({
    this.all,
    this.fast,
    this.simple,
    this.important,
    this.veryImportant,
  });

  NotificationCount.fromJson(dynamic json, String status) {
    all = json['all'];
    fast = json['fast'];
    simple = json['simple'];
    important = json['important'];
    veryImportant = json['veryImportant'];
    this.status = status;
  }

  Id id = Isar.autoIncrement; // you can also use id = null to auto increment
  int? all;
  int? fast;
  int? simple;
  int? important;
  int? veryImportant;
  String? status;
}
