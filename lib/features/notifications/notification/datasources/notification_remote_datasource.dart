import 'package:dio/dio.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/notifications/notification/model/notification_enum.dart';

abstract class NotificationRemoteDatasource {
  Future<NotificationModel> getNotifications(
      {required bool? archive,
      required NotificationEnumStatus status,
      required int page,
      String? date,
      String? moderator});
}

class NotificationRemoteDatasourceImpl extends NotificationRemoteDatasource {
  final Dio dio;

  NotificationRemoteDatasourceImpl({required this.dio});

  @override
  Future<NotificationModel> getNotifications({
    required bool? archive,
    required NotificationEnumStatus status,
    required int page,
    String? date,
    String? moderator,
  }) async {
    var query = {"archive": archive, "status": status.name, "page": page};
    if (date != null) {
      query['date'] = date;
    }
    if (moderator != null) {
      query['moderator'] = moderator;
    }

    var response =
        await dio.get(getAllNotificationPath, queryParameters: query);
    if (response.statusCode == 200) {
      NotificationModel list =
          NotificationModel.fromJson(response.data, status);
      return list;
    } else {
      throw DioException(
          requestOptions: response.requestOptions, response: response);
    }
  }
}
