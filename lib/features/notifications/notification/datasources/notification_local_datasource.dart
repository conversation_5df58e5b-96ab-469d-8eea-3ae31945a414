import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/notifications/notification/model/notification_enum.dart';
import 'package:isar/isar.dart';

abstract class NotificationLocalDatasource {
  Future<List<NotificationDocs>> getNotifications(
      {required bool? archive,
      required NotificationEnumStatus status,
      required int page,
      String? date,
      String? moderator,
      required int? statusNumber});

  Future setNotifications(
      {required List<NotificationDocs>? notifications,
      required bool? archive,
      required NotificationEnumStatus status,
      required bool reset});
}

class NotificationLocalDatasourceImpl extends NotificationLocalDatasource {
  final IsarService isarService;

  NotificationLocalDatasourceImpl({required this.isarService});

  @override
  Future<List<NotificationDocs>> getNotifications(
      {required bool? archive,
      required NotificationEnumStatus status,
      required int page,
      String? date,
      String? moderator,
      required int? statusNumber}) async {
    print(archive);
    if (archive == null) {
      if (status == NotificationEnumStatus.all) {
        List<NotificationDocs> list = await isarService.isar.notificationDocs
            .filter()
            .scheduledTimeIsNotNull()
            .sortByDateDesc()
            .findAll();
        return list;
      } else {
        List<NotificationDocs> list = await isarService.isar.notificationDocs
            .filter()
            .statusEqualTo(statusNumber)
            .scheduledTimeIsNotNull()
            .sortByDateDesc()
            .findAll();
        return list;
      }
    } else {
      List<NotificationDocs> list = await isarService.isar.notificationDocs
          .filter()
          .activeEqualTo(archive == true ? false : true)
          .localStatusEqualTo(status)
          .scheduledTimeIsNull()
          .sortByDateDesc()
          .findAll();
      print(list);
      return list;
    }
  }

  @override
  Future setNotifications(
      {required List<NotificationDocs>? notifications,
      required bool? archive,
      required NotificationEnumStatus status,
      required bool reset}) async {
    if (reset) {
      await isarService.isar.writeTxn(() async {
        await isarService.isar.notificationDocs
            .filter()
            .localStatusEqualTo(status)
            .activeEqualTo(archive == true ? false : true)
            .scheduledTimeIsNull()
            .deleteAll();
      });

      await isarService.isar.writeTxn(() async {
        isarService.isar.notificationDocs.putAll(notifications ?? []);
      });
    } else {
      await isarService.isar.writeTxn(() async {
        isarService.isar.notificationDocs.putAll(notifications ?? []);
      });
    }
  }
}
