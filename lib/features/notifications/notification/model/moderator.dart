import 'package:isar/isar.dart';

part 'moderator.g.dart';

List<Moderator> listFromModerator(dynamic data) {
  return List<Moderator>.from(data.map((e) => Moderator.fromJson(e))).toList();
}

@collection
@Name("moderator")
class Moderator {
  Moderator({
    this.id,
    this.fullName,
  });

  Moderator.fromJson(dynamic json) {
    id = json['_id'];
    fullName = json['fullName'];
  }
  Id localId = Isar.autoIncrement;
  String? id;
  String? fullName;
}
