import 'package:ijrochi/features/notifications/notification/model/notification_enum.dart';
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:isar/isar.dart';

part 'notification.g.dart';

class NotificationModel {
  NotificationModel(
      {this.docs,
      this.totalDocs,
      this.limit,
      this.totalPages,
      this.page,
      this.pagingCounter,
      this.hasPrevPage,
      this.hasNextPage,
      this.prevPage,
      this.nextPage,
      this.localStatus});

  NotificationModel.fromJson(dynamic json,
      [NotificationEnumStatus? localStatus]) {
    if (json['docs'] != null) {
      docs = [];
      json['docs'].forEach((v) {
        docs?.add(NotificationDocs.fromJson(v, localStatus));
      });
    }
    totalDocs = json['totalDocs'];
    limit = json['limit'];
    totalPages = json['totalPages'];
    page = json['page'];
    pagingCounter = json['pagingCounter'];
    hasPrevPage = json['hasPrevPage'];
    hasNextPage = json['hasNextPage'];
    prevPage = json['prevPage'];
    nextPage = json['nextPage'];
    this.localStatus = localStatus;
  }

  List<NotificationDocs>? docs;
  int? totalDocs;
  int? limit;
  int? totalPages;
  int? page;
  int? pagingCounter;
  bool? hasPrevPage;
  bool? hasNextPage;
  dynamic prevPage;
  dynamic nextPage;
  NotificationEnumStatus? localStatus;
}

@Collection()
@Name("notification")
class NotificationDocs {
  NotificationDocs({
    this.unRead,
    this.notificationId,
    this.notification,
    this.status,
    this.active,
    this.sendByFirebase,
    this.isRead,
    this.date,
    this.chats,
    this.createdAt,
    this.updatedAt,
    this.scheduledTime,
    required this.localStatus,
  });

  NotificationDocs.fromJson(dynamic json,
      [NotificationEnumStatus? localStatus]) {
    unRead = json['unRead'];
    notificationId = json['_id'];
    notification = json['notification'] != null
        ? Notification.fromJson(
        json['notification'], json['_id'], json['status'])
        : null;
    status = json['status'];
    active = json['active'];
    sendByFirebase = json['sendByFirebase'];
    isRead = json['isRead'];
    date = json['date'];
    if (json['chats'] != null) {
      chats = [];
      json['chats'].forEach((v) {
        chats?.add(Chat.fromJson(v));
      });
    }
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    this.localStatus = localStatus ?? NotificationEnumStatus.all;
  }

  Id id = Isar.autoIncrement; // you can also use id = null to auto increment
  int? unRead;
  String? notificationId;
  Notification? notification;
  int? status;
  bool? active;
  String? sendByFirebase;
  bool? isRead;
  String? date;
  List<Chat>? chats;
  String? createdAt;
  String? updatedAt;
  DateTime? scheduledTime;
  @enumerated
  late NotificationEnumStatus localStatus;

  NotificationDocs copyWith({
    int? unRead,
    String? notificationId,
    Notification? notification,
    int? status,
    bool? active,
    String? sendByFirebase,
    bool? isRead,
    String? date,
    List<Chat>? chats,
    String? createdAt,
    String? updatedAt,
    DateTime? scheduledTime,
    NotificationEnumStatus? localStatus,
  }) {
    return NotificationDocs(
      unRead: unRead ?? this.unRead,
      notificationId: notificationId ?? this.notificationId,
      notification: notification ?? this.notification,
      status: status ?? this.status,
      active: active ?? this.active,
      sendByFirebase: sendByFirebase ?? this.sendByFirebase,
      isRead: isRead ?? this.isRead,
      date: date ?? this.date,
      chats: chats ?? this.chats,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      localStatus: localStatus ?? this.localStatus,
    );
  }
}
