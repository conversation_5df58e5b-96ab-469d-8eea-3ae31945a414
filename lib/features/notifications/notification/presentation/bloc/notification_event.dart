part of 'notification_bloc.dart';

abstract class NotificationEvent extends Equatable {
  const NotificationEvent();
}

class GetNotificationEvent extends NotificationEvent {
  final bool? archive;
  final NotificationEnumStatus status;
  final int page;
  final bool refresh;
  final bool reset;
  final String? date;
  final String? moderator;
  final int? statusNumber;

  GetNotificationEvent(
      {required this.archive,
      required this.status,
      required this.page,
      required this.refresh,
      required this.reset,
      required this.date,
      required this.moderator,
      required this.statusNumber});

  @override
  List<Object?> get props => [archive, status, page, date, moderator,status];
}

class FilterEvent extends NotificationEvent {
  final bool? archive;
  final NotificationEnumStatus status;
  final String? date;
  final String? moderator;
  final int? statusNumber;

  FilterEvent(
      {required this.archive,
      required this.status,
      required this.date,
      required this.moderator,
      required this.statusNumber});

  @override
  List<Object?> get props => [archive, date, status, moderator,statusNumber];
}
