part of 'notification_bloc.dart';

enum NotificationStatus {
  initial,
  loading,
  failure,
  success,
  noInternet,
  filtered
}

class NotificationState extends Equatable {
  final NotificationStatus status;
  final String? message;
  final NotificationModel? notificationModel;
  final String? moderator;
  final String? date;
  final NotificationEnumStatus? notificationEnumStatus;
  final bool? archive;

  NotificationState({required this.status,
    this.message,
    this.notificationModel,
    this.moderator,
    this.date,
    this.notificationEnumStatus,
    this.archive});

  static NotificationState initial() =>
      NotificationState(status: NotificationStatus.initial);

  NotificationState copyWith({NotificationStatus? status,
    String? message,
    NotificationModel? notificationModel,
    String? moderator,
    String? date,
    NotificationEnumStatus? notificationEnumStatus,
    bool? archive}) =>
      NotificationState(
          status: status ?? this.status,
          message: message ?? this.message,
          notificationModel: notificationModel ?? this.notificationModel,
          moderator: moderator ?? this.moderator,
          date: date ?? this.date,
          notificationEnumStatus:
          notificationEnumStatus ?? this.notificationEnumStatus,
          archive: archive ?? this.archive);

  @override
  List<Object?> get props =>
      [
        status,
        message,
        notificationModel,
        moderator,
        date,
        notificationEnumStatus,
        archive,
      ];
}
