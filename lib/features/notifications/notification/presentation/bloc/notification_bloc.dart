import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/features/notifications/notification/datasources/notification_local_datasource.dart';
import 'package:ijrochi/features/notifications/notification/datasources/notification_remote_datasource.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/notifications/notification/model/notification_enum.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

part 'notification_event.dart';

part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationRemoteDatasource notificationRemoteDatasource;
  final NotificationLocalDatasource notificationLocalDatasource;
  final NetworkInfo networkInfo;

  NotificationBloc(
      {required this.notificationRemoteDatasource,
      required this.notificationLocalDatasource,
      required this.networkInfo})
      : super(NotificationState.initial()) {
    on<GetNotificationEvent>(getNotification, transformer: droppable());
    on<FilterEvent>(filter, transformer: droppable());
  }

  getNotification(
      GetNotificationEvent event, Emitter<NotificationState> emit) async {
    if(event.archive==null){
      List<NotificationDocs> list =
      await notificationLocalDatasource.getNotifications(
        archive: event.archive,
        status: event.status,
        page: 1,
        statusNumber: event.statusNumber
      );
      emit(state.copyWith(
          status: NotificationStatus.success,
          notificationModel:
          NotificationModel(docs: list, totalPages: 1, page: 1)));

    }
    else{
      if (await networkInfo.isConnected) {
        if(event.reset){
          emit(state.copyWith(status: NotificationStatus.loading));
        }
        try {
          NotificationModel notificationModel =
          await notificationRemoteDatasource.getNotifications(
              archive: event.archive,
              status: event.status,
              page: event.page,
              date: event.date,
              moderator: event.moderator);

          notificationLocalDatasource.setNotifications(
              notifications: notificationModel.docs,
              archive: event.archive,
              status: event.status,
              reset: event.reset);

          emit(state.copyWith(
              status: NotificationStatus.success,
              notificationModel: notificationModel));
        } on DioException catch (e) {
          emit(state.copyWith(
              status: NotificationStatus.failure, message: LocaleKeys.unknown_error.tr()));
        } catch (e) {
          emit(state.copyWith(
              status: NotificationStatus.failure, message: LocaleKeys.unknown_error.tr()));
        }
      } else {
        List<NotificationDocs> list =
        await notificationLocalDatasource.getNotifications(
          archive: event.archive,
          status: event.status,
          page: 1,
          statusNumber: event.statusNumber
        );
        emit(state.copyWith(
            status: NotificationStatus.success,
            message: LocaleKeys.no_internet.tr(),
            notificationModel:
            NotificationModel(docs: list, totalPages: 1, page: 1)));
      }
    }

  }

  filter(FilterEvent event, Emitter<NotificationState> emit) {
    emit(state.copyWith(
        status: NotificationStatus.filtered,
        date: event.date,
        moderator: event.moderator));
  }
}
