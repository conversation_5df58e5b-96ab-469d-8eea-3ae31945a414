import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/core/widgets/empty_list_widget.dart';
import 'package:ijrochi/core/widgets/failure_widget.dart';
import 'package:ijrochi/features/notification_detail/presentation/page/notification_detailed_page.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/notifications/notification/model/notification_enum.dart';
import 'package:ijrochi/features/notifications/notification/presentation/bloc/notification_bloc.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/pages/notification_parent_page.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/widgets/notification_item.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

import '../../../../../core/widgets/no_more_widget.dart';

class NotificationPage extends StatefulWidget {
  final NotificationEnumStatus status;
  final bool? archive;
  final NotificationBloc bloc;
  final int? statusNumber;

  const NotificationPage(
      {super.key,
      required this.status,
      required this.archive,
      required this.bloc,
      required this.statusNumber});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage>
    with AutomaticKeepAliveClientMixin {
  final PagingController<int, NotificationDocs> _pagingController =
      PagingController(firstPageKey: 1);
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');

  late bool isDark;
  bool reset = false;
  bool refresh = true;
  String? moderator;
  String? date;
  List<NotificationDocs> list = [];
  var headerTimes = Map<String, String>();
  var prevPageKey = 0;

  pagingComponent() {
    _pagingController.addPageRequestListener((pageKey) {
      if (pageKey == 1) {
        widget.bloc.add(GetNotificationEvent(
            archive: widget.archive,
            status: widget.status,
            page: 1,
            refresh: refresh,
            reset: true,
            date: date,
            moderator: moderator,
            statusNumber: widget.statusNumber));
      } else {
        widget.bloc.add(GetNotificationEvent(
            archive: widget.archive,
            status: widget.status,
            page: pageKey,
            refresh: refresh,
            reset: false,
            date: date,
            moderator: moderator,
            statusNumber: widget.statusNumber));
      }
    });
    if (refresh) {
      ///First event when online
      _pagingController.notifyPageRequestListeners(1);
    } else {
      ///First event when offline
      widget.bloc.add(GetNotificationEvent(
          archive: widget.archive,
          status: widget.status,
          page: 1,
          refresh: refresh,
          reset: reset,
          date: date,
          moderator: moderator,
          statusNumber: widget.statusNumber));
    }
  }

  handleRefresh(bool refresh) async {
    reset = true;
    this.refresh = refresh;
    this.moderator = null;
    this.date = null;

    ///Prevent adding duplicate appends at once with one page key
    prevPageKey = 0;
    _pagingController.refresh();
    headerTimes.clear();
    context.findAncestorStateOfType<State<NotificationParentPage>>()?.setState(() {});

  }

  filter({required String? moderatorId, required String? date}) async {
    ///Prevent adding duplicate appends at once with one page key
    print(date);
    prevPageKey = 0;
    this.refresh = true;
    this.moderator = moderatorId;
    this.date = date;
    headerTimes.clear();
    _pagingController.refresh();
  }

  @override
  void initState() {
    super.initState();
    pagingComponent();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      backgroundColor: isDark ? cBackgroundColor : cGrayColor.withAlpha(30),
      body: BlocConsumer<NotificationBloc, NotificationState>(
        buildWhen: (previous, current) {
          if (current.status == NotificationStatus.filtered) {
            filter(
              moderatorId: current.moderator,
              date: current.date,
            );
            return false;
          }
          return true;
        },
        listener: (context, state) {
          if (state.status == NotificationStatus.success) {
            if (state.message != null) {
              CustomToast.showToast(state.message.toString());
            }
            list = state.notificationModel?.docs ?? [];
            bool isLastPage = state.notificationModel?.totalPages ==
                state.notificationModel?.page;
            int? currentPage = state.notificationModel?.page ?? 0;
            final _next = currentPage + 1;

            if (prevPageKey != currentPage) {
              if (isLastPage) {
                _pagingController.appendLastPage(list);
              } else {
                _pagingController.appendPage(list, _next);
              }
              prevPageKey = currentPage;
            }
          }
          else if(state.status==NotificationStatus.failure){
            _pagingController.error=state.message;
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () async {
              handleRefresh(true);
            },
            child: PagedListView(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                pagingController: _pagingController,
                physics: BouncingScrollPhysics(),
                builderDelegate: PagedChildBuilderDelegate<NotificationDocs>(
                    noItemsFoundIndicatorBuilder: (_) => EmptyListWidget(
                      onTap: () {
                        handleRefresh(true);
                      },
                      title: LocaleKeys.empty_list.tr(),
                      isDark: isDark,
                    ),
                    noMoreItemsIndicatorBuilder: (_) => NoMoreWidget(
                      onTap: () {
                        handleRefresh(true);
                      },
                    ),
                    newPageProgressIndicatorBuilder: (_)=> SizedBox(height: 200.h,child: CupertinoActivityIndicator(radius: 20.r,),),
                    firstPageProgressIndicatorBuilder: (_)=>Center(child: CupertinoActivityIndicator(radius: 20.r,),),
                    firstPageErrorIndicatorBuilder: (_)=>FailureWidget(
                      onTap: () {
                        handleRefresh(true);
                      },
                      title: state.message ?? LocaleKeys.unknown_error.tr(),
                      isDark: isDark,
                    ),
                    newPageErrorIndicatorBuilder: (_)=>FailureWidget(
                      onTap: () {
                        handleRefresh(true);
                      },
                      title: state.message ?? LocaleKeys.unknown_error.tr(),
                      isDark: isDark,
                    ),
                    itemBuilder: (context, item, index) {
                      String time = formatterDate
                          .format(DateTime.parse(item.createdAt.toString()));
                      String id = item.notificationId ?? "";
                      if ((!headerTimes.containsKey(time)) ||
                          headerTimes.containsValue(id)) {
                        headerTimes[time] = id;
                        return NotificationItem(
                          notificationDoc: item,
                          visible: true,
                          onTapItem: (NotificationDocs notificationDocs) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        NotificationDetailedPage.screen(
                                            notificationDocs: item,
                                            archive: widget.archive)))
                                .then((value) {
                              if (value == true) {
                                handleRefresh(true);
                              }
                            });
                          },
                        );
                      } else {
                        return NotificationItem(
                          notificationDoc: item,
                          visible: false,
                          onTapItem: (NotificationDocs notificationDocs) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        NotificationDetailedPage.screen(
                                          notificationDocs: item,
                                          archive: widget.archive,
                                        ))).then((value) {
                              if (value == true) {
                                handleRefresh(true);
                              }
                            });
                          },
                        );
                      }
                    })),
          );
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
