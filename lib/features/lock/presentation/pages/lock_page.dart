import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/widgets/auth.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/core/widgets/keyboard_dismissible_widget.dart';
import 'package:ijrochi/core/widgets/language_dialog.dart';
import 'package:ijrochi/features/app.dart';
import 'package:ijrochi/features/lock/presentation/widgets/num_pad.dart';
import 'package:ijrochi/features/lock/presentation/widgets/password_textfield.dart';
import 'package:ijrochi/features/payments/lock_switcher.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import '../../../../core/utils/app_constants.dart';
import '../../../../di/dependency_injection.dart';

class LockPage extends StatefulWidget {
  const LockPage({Key? key}) : super(key: key);

  @override
  State<LockPage> createState() => _LockPageState();
}

class _LockPageState extends State<LockPage> {
  final SharedPreferences sharedPreferences = di();
  final http.Client client = di();

  late bool isLoggedIn;
  late bool useBiometric;
  late bool isDark;
  String? pin_code = '';
  String _verticalGroupValue = "O\'zbekcha";

  savePinCode() {
    sharedPreferences.setString(pin_code_pref, password.value.text);
    pin_code = sharedPreferences.getString(pin_code_pref) ?? '';
    isLoggedIn = !(pin_code == '');
  }

  @override
  void initState() {
    super.initState();
    pin_code = sharedPreferences.getString(pin_code_pref) ?? '';
    useBiometric = sharedPreferences.getBool('use_biometric') ?? true;
    isLoggedIn = !(pin_code == '');
    getSelectedLanguage();
    if (useBiometric && isLoggedIn) {
      authenticate();
    }
  }

  TextEditingController password = TextEditingController();
  TextEditingController password_retry = TextEditingController();
  final TextEditingController _pinPutController = TextEditingController();

  getSelectedLanguage() async {
    String lang = await sharedPreferences.getString(language_pref) ?? 'uz';
    switch (lang) {
      case 'uz':
        {
          _verticalGroupValue = 'O\'zbekcha';
          break;
        }
      case 'ru':
        {
          _verticalGroupValue = 'Русский';
          break;
        }
      case 'cr':
        {
          _verticalGroupValue = 'Ўзбекча';
          break;
        }
      default:
        {
          _verticalGroupValue = 'O\'zbekcha';
        }
    }
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return !isLoggedIn
        ? Scaffold(
        resizeToAvoidBottomInset: true,
        body: KeyboardDismissibleWidget(
          child: SingleChildScrollView(
            child: SafeArea(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 20.w),
                width: MediaQuery.of(context).size.width,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 20.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          _verticalGroupValue,
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        SizedBox(
                          width: 5.w,
                        ),
                        InkWell(
                          onTap: () {
                            showDialog(
                                context: context,
                                builder: (BuildContext context1) {
                                  return LanguageDialog.screen(
                                      onSelectCallBack: () {
                                        setState(() {
                                          getSelectedLanguage();
                                          dismissDialog(context1);
                                        });
                                      });
                                });
                          },
                          child: SvgPicture.asset(
                            Assets.iconsIcLanguage,
                            width: 22.w,
                            height: 22.w,
                            color: cFirstColor,
                          ),
                        )
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 60.h),
                      child: Text(
                        LocaleKeys.pin_code.tr(),
                        style: TextStyle(
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w800,
                            fontFamily: interFont,
                            color: cFirstColor),
                      ),
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    Text(
                        textAlign: TextAlign.start,
                        LocaleKeys.insatall_pin_code.tr(),
                        style: TextStyle(
                            fontSize: 14.sp,
                            fontFamily: regular,
                            color: cGrayTextColor1)),
                    SizedBox(
                      height: 14.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.pin_code.tr(),
                      controller: password,
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    PasswordTextField(
                      hintText: LocaleKeys.re_enter_pin_code.tr(),
                      controller: password_retry,
                    ),
                    SizedBox(
                      height: 30.h,
                    ),
                    MaterialButton(
                      onPressed: () async {
                        if (password.value.text ==
                            password_retry.value.text) {
                          savePinCode();
                          Navigator.pushAndRemoveUntil(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => LockProvider()),
                                  (route) => false);
                        } else {
                          Snack(LocaleKeys.pin_code_no_same.tr(), context,
                              cRedColor);
                        }
                      },
                      child: _button(State),
                      color: cFirstColor,
                      minWidth: MediaQuery.of(context).size.width,
                      height: 42.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r)),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ))
        : Container(
      color: isDark ? cFirstColorDark : cFirstColor,
      child: SafeArea(
        bottom: false,
        child: Scaffold(
          body: Column(
            children: [
              Spacer(
                flex: 2,
              ),
              Text(
                LocaleKeys.entering_pin_code.tr(),
                style: TextStyle(
                    color: cFirstColor,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w500),
              ),
              SizedBox(
                height: 30.h,
              ),
              SizedBox(
                width: 160.w,
                height: 30.h,
                child: PinCodeTextField(
                  animationType: AnimationType.fade,
                  animationCurve: Curves.easeInCirc,
                  useHapticFeedback: true,
                  showCursor: false,
                  appContext: context,
                  controller: _pinPutController,
                  length: 4,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  obscuringWidget: Container(
                    height: 30.h,
                    width: 30.w,
                    decoration: BoxDecoration(
                      color: cFirstColor,
                      borderRadius: BorderRadius.circular(50.r),
                    ),
                  ),
                  enableActiveFill: true,
                  enablePinAutofill: true,
                  pinTheme: PinTheme(
                    fieldHeight: 25.h,
                    fieldWidth: 25.w,
                    borderWidth: 1.5.w,
                    shape: PinCodeFieldShape.circle,
                    activeColor: cFirstColor,
                    inactiveColor: cFirstColor,
                    disabledColor: cWhiteColor,
                    activeFillColor: cWhiteColor,
                    selectedFillColor: cWhiteColor,
                    inactiveFillColor: cWhiteColor,
                    errorBorderColor: cWhiteColor,
                  ),
                  onChanged: (v) {},
                  onCompleted: (v) async {
                    if (pin_code == v) {
                      shouldLockStatic = false;
                      Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(
                              builder: (context) => LockProvider()),
                              (route) => false);
                    } else {
                      Snack(LocaleKeys.pin_code_no_same.tr(), context,
                          cRedColor);
                      _pinPutController.clear();
                    }
                  },
                ),
              ),
              SizedBox(
                height: 10.h,
              ),
              Divider(
                color: themeIdentify(context) ? cWhiteColor : cBlackColor,
                thickness: 1.3.h,
                indent: 30.w,
                endIndent: 30.w,
              ),
              Spacer(
                flex: 2,
              ),
              numPad(_pinPutController, context, useBiometric, () {
                shouldLockStatic = false;
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                        builder: (context) => LockProvider()),
                        (route) => false);
              }),
              Spacer(
                flex: 1,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _button(state) {
    if (state is State) {
      return const CupertinoActivityIndicator();
    } else {
      return Text(
        "OK",
        style: TextStyle(
          fontSize: 14.sp,
          fontFamily: Assets.fontsNunitoSansRegular,
        ),
      );
    }
  }

  authenticate() async {
    print('Use biometric is: $useBiometric');
    try {
      final isAuthenticated = await LocalAuthApi.authenticate();
      print("Is authenticated: $isAuthenticated");
      if (isAuthenticated) {
        shouldLockStatic = false;
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => LockProvider()),
            (route) => false);
      }
    } catch (e) {
      print(e.toString());
    }
    ;
  }
}
