import 'package:flutter/material.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Widget number(TextEditingController _pinPutController, String number,BuildContext context) {
  return MaterialButton(
    focusColor: cSecondColor,
    hoverColor: cSecondColor,
    highlightColor: cSecondColor,
    height: cNumberLockH70.h,
    shape: CircleBorder(),
    onPressed: () {
      if (_pinPutController.text.length != 4 &&
          _pinPutController.text.length != 5) {
        _pinPutController.text = _pinPutController.text + number;
      }
      _pinPutController.selection =
          TextSelection.collapsed(offset: _pinPutController.text.length);
    },
    child: SizedBox(
      child: Center(
        child: Text(
          number,
          textAlign: TextAlign.center,
          style: TextStyle(
              fontSize: cNumberLockText42.sp,
              fontWeight: FontWeight.w400,
              color:themeIdentify(context)?cWhiteColor:cBlackColor),
        ),
      ),
    ),
  );
}
