import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/auth.dart';
import 'package:ijrochi/features/lock/presentation/widgets/number.dart';
import 'package:ijrochi/features/payments/lock_switcher.dart';
import 'package:ijrochi/generated/assets.dart';

Container numPad(
    TextEditingController _pinPutController,
    BuildContext context,
    bool useBiometric,
    VoidCallback onAuthenticated,  // Add the callback parameter
    ) {
  return Container(
    // height: 360.h,
    // width: 300.w,
    margin: EdgeInsets.symmetric(horizontal: 40.w),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            number(_pinPutController, '1', context),
            number(_pinPutController, '2', context),
            number(_pinPutController, '3', context),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            number(_pinPutController, '4', context),
            number(_pinPutController, '5', context),
            number(_pinPutController, '6', context),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            number(_pinPutController, '7', context),
            number(_pinPutController, '8', context),
            number(_pinPutController, '9', context),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              height: 40.h,
              width: cNumberLockW70.w,
              child: Visibility(
                visible: useBiometric,
                child: GestureDetector(
                    onTap: () async {
                      try {
                        final isAuthenticated =
                        await LocalAuthApi.authenticate();
                        if (isAuthenticated) {
                          onAuthenticated();  // Call the callback function
                        }
                      } catch (e) {
                        debugPrint(e.toString());
                      }
                    },
                    child: Platform.isAndroid
                        ? SvgPicture.asset(
                      Assets.iconsFingerScan,
                      color: themeIdentify(context)
                          ? cWhiteColor
                          : cBlackColor,
                      height: 25.h,
                    )
                        : Image.asset(
                      Assets.iconsBiometrics,
                      color: themeIdentify(context)
                          ? cWhiteColor
                          : cBlackColor,
                      height: 25.h,
                    )),
              ),
            ),
            number(_pinPutController, '0', context),
            GestureDetector(
              onTap: () {
                if (_pinPutController.text.isNotEmpty) {
                  _pinPutController.text = _pinPutController.text
                      .substring(0, _pinPutController.text.length - 1);
                  _pinPutController.selection = TextSelection.collapsed(
                      offset: _pinPutController.text.length);
                }
              },
              behavior: HitTestBehavior.translucent,
              child: SizedBox(
                height: cNumberLockH90.h,
                width: cNumberLockW70.w,
                child: Center(
                  child: Icon(
                    Icons.backspace,
                    size: 30.sm,
                    color: themeIdentify(context) ? cWhiteColor : cBlackColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    ),
  );
}