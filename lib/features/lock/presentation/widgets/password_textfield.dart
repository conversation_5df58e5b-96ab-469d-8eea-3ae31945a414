import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/settings/presentation/bloc/settings_bloc.dart';

class PasswordTextField extends StatefulWidget {
  final String? hintText;
  final TextEditingController? controller;
  bool? isError = false;
  final String? errorMessage;
  final SettingsBloc? bloc;

  PasswordTextField(
      {this.hintText,
      this.controller,
      this.isError,
      this.errorMessage,
      this.bloc});

  @override
  State<PasswordTextField> createState() => _PasswordTextFieldState();
}

class _PasswordTextFieldState extends State<PasswordTextField> {
  bool isObscureText = true;

  // @override
  // void dispose() {
  //   widget.bloc?.close();
  //   super.dispose();
  // }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 10.h,
          ),
          Container(
            height: 46.h,
            child: TextField(
              maxLength: 4,
              obscureText: isObscureText,
              controller: widget.controller,
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
              ],
              style: TextStyle(
                fontSize: 14.sp,
                fontFamily: regular,
              ),
              decoration: InputDecoration(
                isDense: true,
                  counterText: '',
                  contentPadding: EdgeInsets.only(left: 10,top: 0,bottom: 0,right: 0),
                  enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                          width: 1.4,
                          color: themeIdentify(context)
                              ? cBlueLight
                              : cFirstColor)),
                  border: OutlineInputBorder(
                      borderSide: BorderSide(
                          width: 2,
                          color: themeIdentify(context)
                              ? cBlueLight
                              : cFirstColor)),
                  focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                          width: 2,
                          color: themeIdentify(context)
                              ? cBlueLight
                              : cFirstColor)),
                  disabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                          width: 2,
                          color: themeIdentify(context)
                              ? cBlueLight
                              : cFirstColor)),
                  label: Text(widget.hintText.toString()),
                  labelStyle: TextStyle(
                      color: themeIdentify(context) ? cBlueLight : cFirstColor),
                  suffixIcon: InkWell(
                    onTap: () {
                      setState(() {
                        isObscureText = !isObscureText;
                      });
                    },
                    child: Icon(
                        widget.isError == true
                            ? Icons.error
                            : isObscureText == false
                                ? Icons.visibility_off
                                : Icons.remove_red_eye,
                        color: widget.isError == true
                            ? themeIdentify(context)
                                ? cBlueLight
                                : cFirstColor
                            : themeIdentify(context)
                                ? cBlueLight
                                : cFirstColor),
                  )),
              onChanged: (value) {
                setState(() {
                  widget.isError = false;
                });
                if (widget.bloc != null) {
                  widget.bloc?.add(CheckPinCodeInitialEvent());
                }
              },
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Visibility(
                  visible: widget.isError ?? false,
                  child: Text(widget.errorMessage.toString())),
              Padding(
                padding: EdgeInsets.only(right: 10.w, top: 4.h),
                child: Text(
                  '${widget.controller!.value.text.length}/4',
                  style: TextStyle(
                      color: themeIdentify(context) ? cBlueLight : cFirstColor),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
