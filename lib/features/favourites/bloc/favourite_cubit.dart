import 'package:bloc/bloc.dart';
import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/notifications/notification/model/moderator.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:intl/intl.dart';
import 'package:isar/isar.dart';
import 'package:meta/meta.dart';

part 'favourite_state.dart';

class FavouriteCubit extends Cubit<FavouriteState> {
  FavouriteCubit() : super(FavouriteInitial());
  final IsarService isarService = di();
  final DateFormat formatterDate = DateFormat('yyyy-MM-dd');

  void getFavouriteList(
      [String? date, Moderator? moderator, String? type]) async {
    print(date);
    print(moderator);
    print(type);
    List<dynamic> mixedList = [];

    List<NotificationDocs> notifications = await isarService
        .isar.notificationDocs
        .filter()
        .scheduledTimeIsNotNull()
        .findAll();

    List<TaskDocs> tasks = await isarService
        .isar.taskDocs
        .filter()
        .scheduledTimeIsNotNull()
        .findAll();

    mixedList.addAll(notifications);
    mixedList.addAll(tasks);
    mixedList.sort((b, a) {
      return DateTime.parse(a.date).compareTo(DateTime.parse(b.date));
    });

    if (type == "Xabarnomlar") {
      mixedList = notifications;
    } else if (type == "Vazifalar") {
      mixedList = tasks;
    }
    if (date != null && date.isNotEmpty) {
      List<dynamic> tempList = [];
      mixedList.forEach((element) {
        if (formatterDate.format(DateTime.parse(element.date)) == date) {
          tempList.add(element);
        }
      });
      mixedList = tempList;
    }
     if (moderator?.id != null && moderator?.id?.isNotEmpty==true) {
      List<dynamic> tempList = [];
      mixedList.forEach((element) {
        if(element is NotificationDocs){
          if (element.notification?.moderator?.id == moderator?.id) {
            tempList.add(element);
          }
        }
        else if(element is TaskDocs){
          if (element.task?.moderator?.id == moderator?.id) {
            tempList.add(element);
          }
        }
      });
      mixedList = tempList;
     }

    if (mixedList.isNotEmpty) {
      print("list:${mixedList}");
      emit(FavouriteSuccess(list: mixedList));
    } else {
      emit(FavouriteEmpty());
    }
  }
}
