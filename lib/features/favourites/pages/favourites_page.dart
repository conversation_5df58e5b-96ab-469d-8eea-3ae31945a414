import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/favourites/bloc/favourite_cubit.dart';
import 'package:ijrochi/features/favourites/pages/favourite_filter_page.dart';
import 'package:ijrochi/features/home/<USER>/pages/home.dart';
import 'package:ijrochi/features/notification_detail/presentation/page/notification_detailed_page.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/widgets/notification_item.dart';
import 'package:ijrochi/features/task_detail/page/task_detail_page.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/widget/task_item.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class FavouritesPage extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => FavouriteCubit(),
      child: FavouritesPage(),
    );
  }

  const FavouritesPage({Key? key}) : super(key: key);

  @override
  State<FavouritesPage> createState() => _FavouritesPageState();
}

class _FavouritesPageState extends State<FavouritesPage> {
  List<dynamic> mixedList = [];
  final IsarService isarService = di();
  var headerTimes = Map<String, int>();
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  late FavouriteCubit _favouriteCubit;

  @override
  void initState() {
    _favouriteCubit = BlocProvider.of<FavouriteCubit>(context);
    _favouriteCubit.getFavouriteList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        extendBodyBehindAppBar: true,
        backgroundColor: themeIdentify(context)
            ? cBackgroundColor
            : cGrayColor.withAlpha(30),
        body: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.h),
              width: MediaQuery.of(context).size.width,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    LocaleKeys.favourites.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.w600, fontSize: 16.sp),
                  ),
                  SizedBox(
                    width: 90.w,
                  ),
                  InkWell(
                    onTap: () async {
                      showModalBottomSheet(
                          context: context,
                          backgroundColor: Colors.transparent,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(20.r),
                                  topLeft: Radius.circular(20.r))),
                          builder: (BuildContext context) {
                            return FavouriteFilterPage(
                              onFilterTap: (value) {
                                print(value);
                                _favouriteCubit.getFavouriteList(
                                    value.date, value.moderator, value.type);
                              },
                            );
                          });
                    },
                    child: Container(
                        margin: EdgeInsets.symmetric(
                            vertical: 12.h, horizontal: 15.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.r)),
                        child: Padding(
                          padding: const EdgeInsets.all(5.0),
                          child: SvgPicture.asset(
                            Assets.iconsFilter,
                            color: themeIdentify(context)
                                ? cWhiteColor
                                : cFirstColor,
                          ),
                        )),
                  )
                ],
              ),
              color: themeIdentify(context) ? cFirstColorDark : cWhiteColor,
            ),
            BlocBuilder<FavouriteCubit, FavouriteState>(
                builder: (context, state) {
              if (state is FavouriteSuccess) {
                return Expanded(
                  child: ListView.builder(
                      padding: EdgeInsets.symmetric(vertical: 10.h),
                      itemCount: state.list.length,
                      itemBuilder: (BuildContext context, int index) {
                        String time = formatterDate.format(DateTime.parse(
                            state.list[index].createdAt.toString()));
                        int id = state.list[index].id ?? -1;
                        if (state.list[index] is NotificationDocs) {
                          if ((!headerTimes.containsKey(time)) ||
                              headerTimes.containsValue(id)) {
                            headerTimes[time] = id;
                            return NotificationItem(
                              visible: true,
                              onTapItem: (value) {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            NotificationDetailedPage.screen(
                                              notificationDocs:
                                                  state.list[index],
                                              archive: true,
                                            ))).then((value) => {
                                      if (value == true)
                                        {
                                          _favouriteCubit.getFavouriteList(),
                                          context
                                              .findAncestorStateOfType<
                                                  State<HomePage>>()
                                              ?.setState(() {})
                                        }
                                    });
                              },
                              notificationDoc: state.list[index],
                            );
                          } else {
                            return NotificationItem(
                              visible: false,
                              onTapItem: (value) {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            NotificationDetailedPage.screen(
                                                notificationDocs:
                                                    state.list[index],
                                                archive:
                                                    true))).then((value) => {
                                      if (value == true)
                                        {
                                          _favouriteCubit.getFavouriteList(),
                                          context
                                              .findAncestorStateOfType<
                                                  State<HomePage>>()
                                              ?.setState(() {})
                                        }
                                    });
                              },
                              notificationDoc: state.list[index],
                            );
                          }
                        } else {
                          if ((!headerTimes.containsKey(time)) ||
                              headerTimes.containsValue(id)) {
                            headerTimes[time] = id;
                            return TaskItem(
                              visible: true,
                              onTapItem: (value) {
                                Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                TaskDetailPage.screen(
                                                    taskDocs:
                                                        state.list[index])))
                                    .then((value) => {
                                          if (value)
                                            {
                                              _favouriteCubit
                                                  .getFavouriteList(),
                                              context
                                                  .findAncestorStateOfType<
                                                      State<HomePage>>()
                                                  ?.setState(() {})
                                            }
                                        });
                              },
                              taskDocs: state.list[index],
                            );
                          } else {
                            return TaskItem(
                              visible: false,
                              onTapItem: (value) {
                                Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                TaskDetailPage.screen(
                                                    taskDocs:
                                                        state.list[index])))
                                    .then((value) => {
                                          if (value)
                                            {
                                              _favouriteCubit
                                                  .getFavouriteList(),
                                              context
                                                  .findAncestorStateOfType<
                                                      State<HomePage>>()
                                                  ?.setState(() {})
                                            }
                                        });
                              },
                              taskDocs: state.list[index],
                            );
                          }
                        }
                      }),
                );
              } else if (state is FavouriteEmpty) {
                return Container(
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 150.h,
                      ),
                      Text(
                        LocaleKeys.no_data_fount.tr(),
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 25.sp,
                            color: themeIdentify(context)
                                ? cWhiteColor
                                : cBlackColor),
                      ),
                      SizedBox(
                        height: 15.h,
                      ),
                      Text(LocaleKeys.empty_list.tr(),
                          style: TextStyle(
                              fontStyle: FontStyle.italic,
                              fontSize: 20.sp,
                              color: themeIdentify(context)
                                  ? cWhiteColor
                                  : cBlackColor)),
                      SizedBox(
                        height: 15.h,
                      ),
                      CupertinoButton(
                          child: Text(LocaleKeys.action_update.tr(),style: TextStyle(color: cWhiteColor),),
                          color: cFirstColor,
                          onPressed: () {
                            _favouriteCubit.getFavouriteList();
                          }),
                    ],
                  ),
                );
              } else {
                return Expanded(
                  child: Center(
                    child: CupertinoActivityIndicator(
                      color: cFirstColor,
                      radius: 10.r,
                    ),
                  ),
                );
              }
            }),
          ],
        ));
  }
}
