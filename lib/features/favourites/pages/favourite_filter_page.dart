import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/notifications/notification/model/moderator.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:intl/intl.dart';
import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';

List<Moderator> moderator = [];
List<String> types = ["Tanlanmagan", "Xabarnomlar", "Vazifalar"];

class FavouriteFilterPage extends StatefulWidget {
  final Function(FavouriteFilterModel filterModel) onFilterTap;

  const FavouriteFilterPage({Key? key, required this.onFilterTap})
      : super(key: key);

  @override
  State<FavouriteFilterPage> createState() => _FavouriteFilterPageState();
}

class _FavouriteFilterPageState extends State<FavouriteFilterPage> {
  String? date;
  bool datePicked = false;
  final DateFormat formatterDate = DateFormat('yyyy-MM-dd');
  final DateTime now = DateTime.now();
  final SharedPreferences prefs = di();
  final IsarService isarService = di();
  late bool isDark;
  Moderator? selectedItem;
  String? selectedType;

  Future<List<Moderator>> getModerators() async {
    moderator.clear();
    moderator.add(Moderator(fullName: LocaleKeys.not_chosen.tr(), id: ""));
    await isarService.isar.moderators.where().findAll().then((value) {
      value.forEach((element) {
        moderator.add(element);
      });
      setState(() {
        selectedItem = moderator[0];
      });
    });
    return await isarService.isar.moderators.where().findAll();
  }

  @override
  void initState() {
    date = formatterDate.format(now);
    datePicked = date != "" ? true : false;
    getModerators();
    selectedType = types[0];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Navigator.pop(context);
      },
      child: Container(
        decoration: BoxDecoration(
            color: isDark ? cFirstColorDark : Colors.white,
            borderRadius: new BorderRadius.only(
                topLeft: const Radius.circular(20.0),
                topRight: const Radius.circular(20.0))),
        height: 420.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 8.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 80.w,
                  height: 6.h,
                  decoration: BoxDecoration(
                      color: cGrayColorLight,
                      borderRadius: BorderRadius.circular(8.r)),
                )
              ],
            ),
            SizedBox(
              height: 8.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  LocaleKeys.filter.tr(),
                  style: TextStyle(
                      color: isDark ? cWhiteColor : cBlackColor,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w500),
                )
              ],
            ),
            SizedBox(
              height: 20.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 20.w),
              child: Text(LocaleKeys.date.tr(),
                  style: TextStyle(
                      color: isDark ? cWhiteColor : cBlueLight,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500)),
            ),
            SizedBox(
              height: 5.h,
            ),
            InkWell(
              onTap: () {
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2023),
                  lastDate: DateTime(2099),
                ).then((value) {
                  if (date != formatterDate.format(value ?? DateTime.now())) {
                    setState(() {
                      date = formatterDate.format(value ?? DateTime.now());
                      datePicked = true;
                    });
                  }
                });
              },
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: cSecondColor),
                    borderRadius: BorderRadius.circular(8.r)),
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                width: MediaQuery.of(context).size.width,
                height: 60.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        date ?? "",
                        style: TextStyle(
                            color: isDark ? cWhiteColor : cBlackColor,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                    datePicked
                        ? IconButton(
                            onPressed: () {
                              setState(() {
                                date = "";
                                datePicked = false;
                              });
                            },
                            icon: Icon(Icons.clear,
                                color: isDark ? cWhiteColor : cSecondColor))
                        : IconButton(
                            onPressed: () {
                              setState(() {});
                            },
                            icon: InkWell(
                                onTap: () {
                                  showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime(2023),
                                    lastDate: DateTime(2099),
                                  ).then((value) {
                                      setState(() {
                                        date = formatterDate.format(value ?? DateTime.now());
                                        datePicked = true;
                                      });
                                  });
                                },
                              child: Icon(
                                Icons.calendar_month,
                                color: isDark ? cWhiteColor : cSecondColor,
                              ),
                            ))
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 20.w),
              child: Text(LocaleKeys.moderator_fio.tr(),
                  style: TextStyle(
                      color: isDark ? cWhiteColor : cBlueLight,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500)),
            ),
            SizedBox(
              height: 5.h,
            ),
            Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                    border: Border.all(color: cSecondColor),
                    borderRadius: BorderRadius.circular(8.r)),
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                width: MediaQuery.of(context).size.width,
                height: 60.h,
                child: DropdownButton<Moderator>(
                    icon: SizedBox.shrink(),
                    underline: SizedBox(),
                    isExpanded: true,
                    value: selectedItem,
                    items: moderator
                        .map<DropdownMenuItem<Moderator>>((Moderator value) {
                      return DropdownMenuItem<Moderator>(
                        value: value,
                        child: Text(value.fullName ?? "",
                            style: TextStyle(
                                color: isDark ? cWhiteColor : cBlackColor,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w400)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedItem = value;
                      });
                    })),
            SizedBox(
              height: 10.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 20.w),
              child: Text("Turi",
                  style: TextStyle(
                      color: isDark ? cWhiteColor : cBlueLight,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500)),
            ),
            SizedBox(
              height: 5.h,
            ),
            Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                    border: Border.all(color: cSecondColor),
                    borderRadius: BorderRadius.circular(8.r)),
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                width: MediaQuery.of(context).size.width,
                height: 60.h,
                child: DropdownButton<String>(
                    icon: SizedBox.shrink(),
                    underline: SizedBox(),
                    isExpanded: true,
                    value: selectedType,
                    items: types.map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value ?? "",
                            style: TextStyle(
                                color: isDark ? cWhiteColor : cBlackColor,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w400)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedType = value;
                      });
                    })),
            SizedBox(
              height: 20.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    LocaleKeys.cancel.tr(),
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                InkWell(
                  onTap: () {
                    FavouriteFilterModel filterModel = FavouriteFilterModel(
                        date: date,
                        moderator: selectedItem,
                        type: selectedType);
                    widget.onFilterTap(filterModel);
                    datePicked == true
                        ? prefs.setString("date", date ?? "")
                        : prefs.setString("date", "");
                    Navigator.pop(context);
                  },
                  child: Text(LocaleKeys.add_filter.tr(),
                      style: TextStyle(
                          color: isDark ? cWhiteColor : cSecondColor,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500)),
                )
              ],
            ),
            SizedBox(
              height: 20.h,
            )
          ],
        ),
      ),
    );
  }
}

class FavouriteFilterModel {
  String? date;
  Moderator? moderator;
  String? type;

  FavouriteFilterModel(
      {required this.date, required this.moderator, required this.type});

  @override
  String toString() {
    return 'FavouriteFilterModel{date: $date, moderator: $moderator, type: $type}';
  }
}
