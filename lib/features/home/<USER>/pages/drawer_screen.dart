import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/auth/presentation/name_bloc/input_name_bloc.dart';
import 'package:ijrochi/features/home/<USER>/pages/home.dart';
import 'package:ijrochi/features/payments/payment_settings.dart';

import 'package:ijrochi/features/settings/presentation/pages/settings_page.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:theme_mode_handler/theme_mode_handler.dart';
import 'package:ijrochi/di/dependency_injection.dart' as GetIT;
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/utils/app_constants.dart';

class DrawerScreen extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => di<InputNameBloc>(),
      child: DrawerScreen(),
    );
  }

  const DrawerScreen({Key? key}) : super(key: key);

  @override
  State<DrawerScreen> createState() => _DrawerScreenState();
}

class _DrawerScreenState extends State<DrawerScreen> {
  var maskFormatter = MaskTextInputFormatter(mask: '+### (##) ###-##-##');
  final SharedPreferences sharedPreferences = GetIT.di();
  bool isDark = false;
  late InputNameBloc inputNameBloc;
  TextEditingController nameController = TextEditingController();
  late String name;
  final GetStorage getStorage = di();
  final SharedPreferences prefs = di();

  late BuildContext dContext;

  @override
  void initState() {
    inputNameBloc = BlocProvider.of<InputNameBloc>(context);
    isDark = sharedPreferences.getString(theme_pref) == 'ThemeMode.dark'
        ? true
        : false;
    name = getStorage.read(FULL_NAME) ?? '---';
    nameController = TextEditingController(text: name);
    nameController.selection = TextSelection.fromPosition(
        TextPosition(offset: nameController.text.length));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: themeIdentify(context) ? cBackDarkColor2 : cWhiteColor,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          BlocConsumer<InputNameBloc, InputNameState>(
            listener: (context, state) {
              if (state.status == InputNameStatus.failure) {
                CustomToast.showToast(state.message ?? "");
              } else if (state.status == InputNameStatus.success) {
                getStorage.write(FULL_NAME, nameController.value.text);
                Navigator.pop(dContext);
              }
            },
            builder: (context, state) {
              return Column(
                children: [
                  Container(
                    padding: EdgeInsets.only(left: 20.w),
                    color:
                        themeIdentify(context) ? cBackDarkColor1 : cWhiteColor,
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 50.h),
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                getStorage.read(FULL_NAME) ?? '---',
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    color: themeIdentify(context)
                                        ? cWhiteColor
                                        : cBlackColor,
                                    fontWeight: FontWeight.w400),
                              ),
                            ),
                            IconButton(
                                onPressed: () {
                                  showDialog(
                                      context: context,
                                      builder: (BuildContext context) {
                                        dContext = context;
                                        return Dialog(
                                          child: Container(
                                            color: isDark
                                                ? cBackDarkColor2
                                                : cWhiteColor,
                                            padding: EdgeInsets.all(14.w),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      LocaleKeys.change_name
                                                          .tr(),
                                                      style: TextStyle(
                                                          fontSize: 14.sp),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 15.h,
                                                ),
                                                Container(
                                                  height: 50.h,
                                                  child: TextField(
                                                    controller: nameController,
                                                    decoration: InputDecoration(
                                                        label: Text(LocaleKeys
                                                            .fio
                                                            .tr()),
                                                        border:
                                                            OutlineInputBorder()),
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 10.h,
                                                ),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.end,
                                                  children: [
                                                    TextButton(
                                                        onPressed: () {
                                                          Navigator.pop(
                                                              context);
                                                        },
                                                        child: Text(
                                                          LocaleKeys.cancel
                                                              .tr(),
                                                          style: TextStyle(
                                                              fontSize: 14.sp,
                                                              color: isDark
                                                                  ? cWhiteColor
                                                                  : cBlackColor),
                                                        )),
                                                    TextButton(
                                                        onPressed: () {
                                                          inputNameBloc.add(AddNameEvent(
                                                              fullName:
                                                                  nameController
                                                                      .value
                                                                      .text,
                                                              phone_number:
                                                                  getStorage.read(
                                                                          PHONE_NUMBER) ??
                                                                      "+998(00)-000-00-00"));
                                                        },
                                                        child: state.status ==
                                                                InputNameStatus
                                                                    .loading
                                                            ? CupertinoActivityIndicator()
                                                            : Text(
                                                                LocaleKeys
                                                                    .change
                                                                    .tr(),
                                                                style:
                                                                    TextStyle(
                                                                  fontSize:
                                                                      14.sp,
                                                                  color:
                                                                      cFirstColor,
                                                                ),
                                                              ))
                                                  ],
                                                )
                                              ],
                                            ),
                                          ),
                                        );
                                      });
                                },
                                icon: Icon(
                                  Icons.edit,
                                  size: 20.w,
                                  color: cGrayTextColor,
                                ))
                          ],
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        Text(
                          LocaleKeys.phone.tr(),
                          style: TextStyle(color: cGrayTextColor),
                        ),
                        Text(
                          maskFormatter.maskText(
                              "+998" + getStorage.read(PHONE_NUMBER) ??
                                  "+998(00)-000-00-00"),
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: themeIdentify(context)
                                ? cWhiteColor
                                : cBlackColor,
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 8.h,
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => SettingsPage.screen()));
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 14.h),
                      alignment: Alignment.center,
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            Assets.iconsSettings,
                            color: themeIdentify(context)
                                ? cWhiteColor
                                : cBlackColor,
                            width: 18.w,
                          ),
                          SizedBox(
                            width: 14.w,
                          ),
                          Text(
                            LocaleKeys.settings.tr(),
                            style: TextStyle(fontSize: 14.sp),
                          )
                        ],
                      ),
                    ),
                  ),
                  Container(
                    height: 0.7.h,
                    color: isDark ? backgroundDark : backgroundLight,
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                  InkWell(
                    onTap: () {
                      Share.share(WEBSITE,
                          subject: LocaleKeys.share_address.tr());
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 14.h),
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            Assets.iconsShare,
                            color: themeIdentify(context)
                                ? cWhiteColor
                                : cBlackColor,
                            width: 20.w,
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            LocaleKeys.share.tr(),
                            style: TextStyle(fontSize: 14.sp),
                          )
                        ],
                      ),
                    ),
                  ),
                  Container(
                    height: 0.7.h,
                    color: isDark ? backgroundDark : backgroundLight,
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                  InkWell(
                    onTap: () async {
                      final Uri launchUri =
                          Uri(scheme: 'tel', path: SUPPORT_TEL);
                      if (await canLaunchUrl(launchUri)) {
                        await launchUrl(launchUri);
                      } else {
                        CustomToast.showToast('This action is not supported');
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 14.h),
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.phone,
                            color: themeIdentify(context)
                                ? cWhiteColor
                                : cBlackColor,
                            size: 20.w,
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            LocaleKeys.connect.tr(),
                            style: TextStyle(fontSize: 14.sp),
                          )
                        ],
                      ),
                    ),
                  ),
                  Container(
                    height: 0.7.h,
                    color: isDark ? backgroundDark : backgroundLight,
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                  Visibility(
                    visible: prefs.getBool(IS_PAYMENT_DONE) == false ? true : false,
                    child: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => PaymentSettings()));
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 14.h),
                            margin: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.payments_outlined,
                                  size: 20.h,
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Text(
                                  LocaleKeys.payment.tr(),
                                  style: TextStyle(fontSize: 14.sp),
                                )
                              ],
                            ),
                          ),
                        ),
                        Container(
                          height: 0.7.h,
                          color: isDark ? backgroundDark : backgroundLight,
                          margin: EdgeInsets.symmetric(horizontal: 16.w),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: 0.7.h,
                    color: isDark ? backgroundDark : backgroundLight,
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                  InkWell(
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () {
                      context
                          .findAncestorStateOfType<State<HomePage>>()
                          ?.setState(() {});
                      setState(() {
                        isDark = !isDark;
                        if (isDark) {
                          ThemeModeHandler.of(context)
                              ?.saveThemeMode(ThemeMode.dark);
                        } else {
                          ThemeModeHandler.of(context)
                              ?.saveThemeMode(ThemeMode.light);
                        }
                      });
                    },
                    child: Container(
                      //color: Colors.blue[100],
                      height: 50.h,
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                              width: 22.w,
                              child: SvgPicture.asset(
                                themeIdentify(context)
                                    ? Assets.iconsIcDarkMode
                                    : Assets.iconsIcLightMode,
                                color: themeIdentify(context)
                                    ? cWhiteColor
                                    : cBlackColor,
                              )),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                            LocaleKeys.dayRegime.tr(),
                            style: TextStyle(fontSize: 14.sp),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          InkWell(
            onTap: () => showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    backgroundColor:
                        isDark == true ? cBackDarkColor2 : cWhiteColor,
                    title: Text(
                      LocaleKeys.want_to_exit.tr(),
                      style: TextStyle(
                        color: isDark ? cGrayTextColor : cBlackColor,
                        fontSize: 16.sp,
                      ),
                    ),
                    content: Text(
                      LocaleKeys.logout_text.tr(),
                      style: TextStyle(
                        color: cGrayTextColor,
                        fontSize: 14.sp,
                      ),
                    ),
                    actions: [
                      TextButton(
                          onPressed: () {
                            setState(() {
                              // Navigator.pop(context);
                              Navigator.of(context).pop(false);
                            });
                          },
                          child: Text(
                            LocaleKeys.cancel.tr(),
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: isDark ? cWhiteColor : cBlackColor,
                            ),
                          )),
                      TextButton(
                          onPressed: () async {
                            WidgetsBinding.instance
                                .addPostFrameCallback((_) async {
                              await logoutServer();
                              await clearAndLogout(context);
                            });
                          },
                          child: Text(LocaleKeys.want_to_exit.tr(),
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: cFirstColor,
                              ))),
                    ],
                  );
                }).then((value) {
              //_bloc.add(CheckPinCodeInitialEvent());
            }),
            child: Container(
              color: isDark ? backgroundDark : backgroundLight,
              padding: EdgeInsets.symmetric(vertical: 18.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10.w),
                    child: Icon(
                      Icons.clear,
                      color: cPinkColor,
                      size: 20.w,
                    ),
                  ),
                  SizedBox(
                    width: 20,
                  ),
                  Text(
                    LocaleKeys.exit_system.tr(),
                    style: TextStyle(fontSize: 14.sp, color: cPinkColor),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
