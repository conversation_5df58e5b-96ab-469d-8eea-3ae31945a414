import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/back_service.dart';
import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/connecting/connecting_page.dart';
import 'package:ijrochi/features/favourites/pages/favourites_page.dart';
import 'package:ijrochi/features/home/<USER>/pages/drawer_screen.dart';
import 'package:ijrochi/features/main/presentation/pages/main_screen.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:badges/badges.dart' as badges;
import 'package:socket_io_client/socket_io_client.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final Dio dio = di.get();
  final SharedPreferences prefs = di();
  List<Widget> pages = [
    MainScreen.screen(),
    FavouritesPage.screen(),
    ConnectingPage()
  ];
  var selectedIndex = 0;
  late bool isDark;
  Future<int> favouriteCount = Future.value(0);
  Color hamburgerMenuColor = cWhiteColor;
  final GetStorage getStorage = di();

  changeHamburgerMenuColor(int index, BuildContext context) {
    if (!themeIdentify(context)) {
      if (index == 0) {
        hamburgerMenuColor = cWhiteColor;
      } else {
        hamburgerMenuColor = cBlackColor;
      }
    } else {
      hamburgerMenuColor = cWhiteColor;
    }
  }

  @override
  void initState() {
    super.initState();
    requestNotificationPermissions(context, channelKey: null, permissionList: [
      NotificationPermission.Alert,
      NotificationPermission.Badge,
      NotificationPermission.Sound,
      NotificationPermission.Vibration,
    ]);

    if (Platform.isWindows | Platform.isMacOS | Platform.isLinux) {
      ///Socket init
      listenSocketDesktop();
    } else {
      ///For online-offline status
      Socket socket = io(
          socketUrl,
          OptionBuilder()
              .setAuth({"token": getStorage.read(BEARER_TOKEN)})
              .setTransports(['websocket'])
              .enableAutoConnect()
              .build());
    }
  }

  @override
  Widget build(BuildContext context) {
    changeHamburgerMenuColor(selectedIndex, context);
    favouriteCount = getFavouriteList();
    return Scaffold(
        key: scaffoldKey,
        drawer: DrawerScreen.screen(),
        body: AnnotatedRegion(
          value: themeIdentify(context)
              ? SystemUiOverlayStyle(
                  statusBarColor: cFirstColorDark,
                  statusBarIconBrightness: Brightness.light,
                  statusBarBrightness: Brightness.dark,
                )
              : SystemUiOverlayStyle(
                  statusBarColor: cWhiteColor,
                  statusBarIconBrightness: Brightness.dark,
                  statusBarBrightness: Brightness.light,
                ),
          child: SafeArea(
            child: Stack(
              alignment: Alignment.topCenter,
              children: [
                pages[selectedIndex],
                Positioned(
                    top: 5.h,
                    left: 0.w,
                    child: InkWell(
                      onTap: () {
                        scaffoldKey.currentState!.openDrawer();
                      },
                      child: Container(
                          width: 50.w,
                          child: Padding(
                            padding: EdgeInsets.all(16.w),
                            child: SvgPicture.asset(
                              Assets.iconsHumburgerMenu,
                              color: hamburgerMenuColor,
                            ),
                          )),
                    )),
              ],
            ),
          ),
        ),
        // body: IndexedStack(
        //   children: pages,
        //   index: selectedIndex,
        // ),
        bottomNavigationBar: FutureBuilder(
            future: favouriteCount,
            builder: (BuildContext context, AsyncSnapshot<int> favouriteCount) {
              print(favouriteCount.data);
              if (favouriteCount.hasData) {
                return BottomNavigationBar(
                  currentIndex: selectedIndex,
                  selectedItemColor: cFirstColor,
                  selectedFontSize: 12.sp,
                  unselectedFontSize: 12.sp,
                  items: [
                    BottomNavigationBarItem(
                        icon: SvgPicture.asset(
                          Assets.iconsIcHome,
                          color: themeIdentify(context) == false
                              ? selectedIndex == 0
                                  ? cFirstColor
                                  : cBottomMenuIconColor
                              : selectedIndex == 0
                                  ? cFirstColor
                                  : cWhiteColor,
                          width: 20.w,
                          height: 20.w,
                        ),
                        label: LocaleKeys.main.tr()),
                    BottomNavigationBarItem(
                      icon: badges.Badge(
                        badgeContent: Text(
                          favouriteCount.data! > 9
                              ? "9+"
                              : favouriteCount.data.toString(),
                          style: TextStyle(color: cWhiteColor, fontSize: 10.sp),
                        ),
                        child: SvgPicture.asset(
                          Assets.iconsBell,
                          color: themeIdentify(context) == false
                              ? selectedIndex == 1
                                  ? cFirstColor
                                  : cBottomMenuIconColor
                              : selectedIndex == 1
                                  ? cFirstColor
                                  : cWhiteColor,
                          width: 18.w,
                        ),
                      ),
                      label: LocaleKeys.favourites.tr(),
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(
                        Icons.phone,
                        color: themeIdentify(context) == false
                            ? selectedIndex == 2
                                ? cFirstColor
                                : cBottomMenuIconColor
                            : selectedIndex == 2
                                ? cFirstColor
                                : cWhiteColor,
                        size: 18.w,
                      ),
                      label: LocaleKeys.connect.tr(),
                    ),
                  ],
                  onTap: (index) {
                    setState(() {
                      selectedIndex = index;
                      changeHamburgerMenuColor(index, context);
                    });
                  },
                );
              } else {
                return BottomNavigationBar(
                  currentIndex: selectedIndex,
                  selectedItemColor: cFirstColor,
                  items: [
                    BottomNavigationBarItem(
                        icon: SvgPicture.asset(
                          Assets.iconsHome,
                          color: themeIdentify(context) == false
                              ? selectedIndex == 0
                                  ? cFirstColor
                                  : cBottomMenuIconColor
                              : selectedIndex == 0
                                  ? cFirstColor
                                  : cWhiteColor,
                          width: 24.w,
                          height: 24.w,
                        ),
                        label: LocaleKeys.main.tr()),
                    BottomNavigationBarItem(
                      icon: SvgPicture.asset(
                        Assets.iconsBell,
                        color: themeIdentify(context) == false
                            ? selectedIndex == 1
                                ? cFirstColor
                                : cBottomMenuIconColor
                            : selectedIndex == 1
                                ? cFirstColor
                                : cWhiteColor,
                        width: 24.w,
                        height: 24.w,
                      ),
                      label: LocaleKeys.favourites.tr(),
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(
                        Icons.phone,
                        color: themeIdentify(context) == false
                            ? selectedIndex == 2
                                ? cFirstColor
                                : cBottomMenuIconColor
                            : selectedIndex == 2
                                ? cFirstColor
                                : cWhiteColor,
                        size: 24.w,
                      ),
                      label: LocaleKeys.connect.tr(),
                    ),
                  ],
                  onTap: (index) {
                    setState(() {
                      selectedIndex = index;
                      changeHamburgerMenuColor(index, context);
                    });
                  },
                );
              }
            }));
  }

  Future<int> getFavouriteList() async {
    final IsarService isarService = di();
    int notificationsScheduledCount = await isarService.isar.notificationDocs
        .filter()
        .scheduledTimeIsNotNull()
        .count();

    int taskScheduledCount = await isarService.isar.taskDocs
        .filter()
        .scheduledTimeIsNotNull()
        .count();
    print(notificationsScheduledCount);
    print(taskScheduledCount);
    return notificationsScheduledCount + taskScheduledCount;
  }
}
