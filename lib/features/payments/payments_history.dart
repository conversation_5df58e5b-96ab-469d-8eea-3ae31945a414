import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/payments/widgets/history_item.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class PaymentsHistoryPage extends StatefulWidget {
  const PaymentsHistoryPage({Key? key}) : super(key: key);

  @override
  _PaymentsHistoryPageState createState() => _PaymentsHistoryPageState();
}

class _PaymentsHistoryPageState extends State<PaymentsHistoryPage> {
  late Future<List<Payments>> userPayments;
  late List<Payments> userPaymentsStatic;
  GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  checkNetwork() async {
    NetworkInfo networkInfo = di();

    if (!await networkInfo.isConnected) {
      CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
    }
  }

  @override
  void initState() {
    userPayments = getUserPayments();
    checkNetwork();
    userPaymentsStatic = [
      Payments(
          amount: "12 000",
          startDate: "2024-11-11",
          endDate: "2024-11-11",
          month: 3),
      Payments(
          amount: "18 000",
          startDate: "2023-11-11",
          endDate: "2023-11-11",
          month: 3)
    ];
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Scaffold(
        appBar: AppBar(
          title: Text(LocaleKeys.payment_history.tr(), style: TextStyle(color: cWhiteColor),),
          backgroundColor: cSecondColor,
          iconTheme: IconThemeData(color: cWhiteColor),
          elevation: 0,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 9,
              child: Center(
                child: FutureBuilder<List<Payments>>(
                    future: userPayments,
                    builder: (BuildContext context,
                        AsyncSnapshot<List<Payments>> snapshot) {
                      switch (snapshot.connectionState) {
                        case ConnectionState.none:
                        case ConnectionState.waiting:
                          return const CircularProgressIndicator();
                        case ConnectionState.active:
                        case ConnectionState.done:
                          if (snapshot.hasError) {
                            return Text('Error: ${snapshot.error}');
                          } else {
                            return RefreshIndicator(
                              key: _refreshIndicatorKey,
                              color: Colors.blue,
                              onRefresh: () {
                                setState(() {});
                                return userPayments = getUserPayments();
                              },
                              child: snapshot.data!.length > 0
                                  ? GroupedListView<Payments, String?>(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 18.w, vertical: 30.h),
                                      groupBy: (element) => DateTime.parse(
                                              element.startDate ?? '0000-00-00')
                                          .year
                                          .toString(),
                                      order: GroupedListOrder.DESC,
                                      elements: snapshot.data ?? [],
                                      groupSeparatorBuilder: (String? value) =>
                                          Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Align(
                                          alignment: Alignment.center,
                                          child: Container(
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(25.r),
                                                color: isDark()
                                                    ? cCardDarkColor
                                                    : cBlackColor
                                                        .withAlpha(20)),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 10.w,
                                                vertical: 5.h),
                                            child: Text(
                                              '$value-${LocaleKeys.year.tr()}',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.copyWith(fontSize: 16.sp),
                                            ),
                                          ),
                                        ),
                                      ),
                                      itemBuilder: (ctx, element) {
                                        return HistoryItem(
                                            paymentModel: element);
                                      },
                                      physics: AlwaysScrollableScrollPhysics(
                                          parent: BouncingScrollPhysics()),
                                    )
                                  : Column(
                                      children: [
                                        Spacer(flex: 2,),
                                        Image.asset(
                                          Assets.iconsEmpty,
                                          height: 250.h,
                                        ),
                                        Spacer(),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20.w),
                                          child: MaterialButton(
                                            onPressed: () {
                                              setState(() {
                                                userPayments =
                                                    getUserPayments();
                                              });
                                            },
                                            child:
                                                Text(LocaleKeys.refresh.tr()),
                                            color: cFirstColor,
                                            elevation: 0,
                                            minWidth: 200.w,
                                            height: 50.h,
                                            textColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        cRadius16.r)),
                                          ),
                                        ),
                                        Spacer(flex: 4,)
                                      ],
                                    ),
                            );
                          }
                      }
                    }),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Future<List<Payments>> getUserPayments() async {
  final Dio dio = di();
  final GetStorage gs = di();
  List<Payments> payments = [];
  var userId = gs.read(ID) ?? '';

  try {
    final response = await dio.get(paymentHistoryPath,
        queryParameters: {'_id':userId, 'page': 1, 'limit': 20},
        options: Options(
            headers: <String, String>{'Content-Type': 'application/json'}));
    final data = response.data['user']?['payments'] ?? '';
    print(data.toString());
    if (response.statusCode == 200) {
      for (int i = 0; i < (data.length); i++) {
        payments.add(Payments.fromJson(data[i]));
      }
      return payments;
    } else {
      CustomToast.showToast(data.toString());
      print(data.toString());
      return [];
    }
  } catch (e) {
    print(e);
    return [];
  }

  // return Future.value([
  //   Payments(
  //       amount: "12 000",
  //       startDate: "2024-11-11",
  //       endDate: "2024-11-11",
  //       month: 3),
  //   Payments(
  //       amount: "18 000",
  //       startDate: "2023-11-11",
  //       endDate: "2023-11-11",
  //       month: 3)
  // ]);
}
