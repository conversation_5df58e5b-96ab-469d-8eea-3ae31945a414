import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:get/get.dart' hide Trans;
import 'package:ijrochi/core/functions/functions.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

import 'lock_switcher.dart';
import 'payment_settings.dart';

///TODO: Use cached image for background
class FunctionalLockPage extends StatefulWidget {
  const FunctionalLockPage({super.key});

  @override
  State<FunctionalLockPage> createState() => _FunctionalLockPageState();
}

class _FunctionalLockPageState extends State<FunctionalLockPage> {
  SharedPreferences prefs = di();
  final NetworkInfo networkInfo = di();
  var sm = SessionManager();
  Future<bool> _initFuture = Future.value(false);

  @override
  void initState() {
    reInitializerButton();
    super.initState();
  }

  reInitializerButton() async {
    await prefs.reload();
    setState(() {
      _initFuture =Future.value(true); //checkPayment();
    });
  }

  @override
  Widget build(BuildContext context) {
    var isPaymentDone = prefs.getBool(IS_PAYMENT_DONE) ?? true;

    print('Payment approved: $isPaymentDone\n');

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: 50.h),
        child: SizedBox(
          height: 60.h,
          width: 250.h,
          child: FloatingActionButton.extended(
            backgroundColor: isPaymentDone ? cGreenColor : cRedColor,
            label: Row(
              children: [
                SizedBox(
                  width: 10.w,
                ),
                SizedBox(
                  width: 160.w,
                  child: Text(
                    LocaleKeys.open_app.tr(),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: cWhiteColor, fontSize: 15.sp),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
              ],
            ),
            onPressed: () {
              if (isPaymentDone) {
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => LockProvider()));
              } else {
                CustomToast.showToast(
                    "Iltimos, ilovani ishlatish uchun to'lovni amalga oshiring");
              }
            },
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
            image: DecorationImage(
          opacity: 0.1,
          image: AssetImage(Assets.imagesLockPattern),
          fit: BoxFit.cover,
        )),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.only(
                      right: context.isTablet ? 30.w : 8.w, top: 40.h),
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      showDialog(
                          context: context,
                          builder: (_) {
                            return BackdropFilter(
                                filter:
                                    ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                                child: Dialog(
                                  backgroundColor:
                                      Theme.of(context).cardTheme.color,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15.r),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(20.h),
                                    child: Text(
                                        "Ijrochi support: \n\n$SUPPORT_TEL\n$TELEGRAM"),
                                  ),
                                ));
                          });
                    },
                    icon: Icon(
                      Icons.info,
                      size: context.isTablet ? 40.h : 35.h,
                      color: Theme.of(context).primaryColor.withOpacity(0.7) ??
                          cBlackColor,
                    ),
                  ),
                )),
            Align(
                alignment: Alignment.topLeft,
                child: Padding(
                  padding: EdgeInsets.only(
                      left: context.isTablet ? 30.w : 8.w, top: 40.h),
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AlertDialog(
                              backgroundColor:
                                  isDark() ? cBackDarkColor2 : cWhiteColor,
                              title: Text(
                                LocaleKeys.want_to_exit.tr(),
                                style: TextStyle(
                                  color:
                                      isDark() ? cGrayTextColor : cBlackColor,
                                  fontSize: 16.sp,
                                ),
                              ),
                              content: Text(
                                LocaleKeys.logout_text.tr(),
                                style: TextStyle(
                                  color: cGrayTextColor,
                                  fontSize: 14.sp,
                                ),
                              ),
                              actions: [
                                TextButton(
                                    onPressed: () {
                                      setState(() {
                                        // Navigator.pop(context);
                                        Navigator.of(context).pop(false);
                                      });
                                    },
                                    child: Text(
                                      LocaleKeys.cancel.tr(),
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        color: isDark()
                                            ? cWhiteColor
                                            : cBlackColor,
                                      ),
                                    )),
                                TextButton(
                                    onPressed: () async {
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) async {
                                        await clearAndLogout(context);
                                      });
                                    },
                                    child: Text(LocaleKeys.want_to_exit.tr(),
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          color: cFirstColor,
                                        ))),
                              ],
                            );
                          });
                    },
                    icon: Icon(
                      Icons.exit_to_app,
                      size: context.isTablet ? 40.h : 35.h,
                      color: Theme.of(context).primaryColor.withOpacity(0.7) ??
                          cBlackColor,
                    ),
                  ),
                )),
            Column(
              children: [
                SizedBox(height: 100.h),
                Expanded(
                    flex: 3,
                    child: Icon(
                      Icons.lock,
                      size: 250,
                      color: cFirstColor,
                    )),
                Expanded(
                  flex: 4,
                  child: ListView(
                    physics: BouncingScrollPhysics(
                        parent: AlwaysScrollableScrollPhysics()),
                    children: [
                      FutureBuilder<bool>(
                          future: _initFuture,
                          builder: (context, snapshot) {
                            ///Set payment value
                            prefs.setBool(
                                IS_PAYMENT_DONE, snapshot.data ?? false);

                            var isPayed = (snapshot.data ?? false);

                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                {
                                  // Otherwise, display a loading indicator.
                                  return functionButton(
                                      isPayed: isPayed, isLoading: true);
                                }
                              default:
                                return functionButton(
                                    isPayed: isPayed, isLoading: false);
                            }
                          })
                    ],
                  ),
                ),
                Spacer(
                  flex: 2,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget functionButton({required bool isPayed, required bool isLoading}) {
    return Column(
      children: [
        InkWell(
          onTap: () {
            reInitializerButton();

            if (!isPayed && !isLoading) {
              WidgetsBinding.instance.addPostFrameCallback((d) {
                CustomToast.showToast("To'lov qilinmagan!");
              });
            }
          },
          child: Container(
            width: double.infinity,
            height: 75.h,
            margin: EdgeInsets.only(left: 25.w, right: 25.w, bottom: 10.h),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
            decoration: ShapeDecoration(
              color: cFirstColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(36.r),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: Icon(
                    Icons.sync,
                    color: cWhiteColor,
                    size: 20.h,
                  ),
                ),
                Expanded(
                  flex: 6,
                  child: Padding(
                    padding: EdgeInsets.all(8.h),
                    child: Text(
                      "To'lovni tekshirish",
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: cWhiteColor, fontSize: 14.sp),
                    ),
                  ),
                ),
                Expanded(
                  child: CircleAvatar(
                    radius: 30.r,
                    backgroundColor: isLoading
                        ? cGreenColor
                        : isPayed
                            ? cGreenColor
                            : cRedColor,
                    child: isLoading
                        ? Center(
                            child: CupertinoActivityIndicator(
                            color: cWhiteColor,
                            radius: 8.r,
                          ))
                        : Icon(
                            isPayed ? Icons.gpp_good : Icons.gpp_bad,
                            color: cWhiteColor,
                            size: 22.h,
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
        InkWell(
          onTap: () {
            Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PaymentSettings(),
                ));
          },
          child: Container(
            width: double.infinity,
            height: 75.h,
            margin: EdgeInsets.only(left: 25.w, right: 25.w, bottom: 10.h),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
            decoration: ShapeDecoration(
              color: cFirstColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(36.r),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: Icon(
                    Icons.payments_outlined,
                    color: cWhiteColor,
                    size: 25.h,
                  ),
                ),
                Expanded(
                  flex: 6,
                  child: Padding(
                    padding: EdgeInsets.all(8.h),
                    child: Text(
                      "To'lov qilish",
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: cWhiteColor, fontSize: 14.sp),
                    ),
                  ),
                ),
                Expanded(
                  child: CircleAvatar(
                    radius: 30.r,
                    backgroundColor: cGreenColor,
                    child: Icon(
                      Icons.arrow_circle_right,
                      color: cWhiteColor,
                      size: 22.h,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
