import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class HistoryItem extends StatelessWidget {
  final Payments paymentModel;

  HistoryItem({super.key, required this.paymentModel});

  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.h, vertical: 7.5.w),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 10,
              blurRadius: 20,
              blurStyle: BlurStyle.normal),
        ],
        borderRadius: BorderRadius.circular(25.r),
        color: Theme.of(context).cardTheme.color,
      ),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(vertical: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    LocaleKeys.tariff.tr(),
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
                SizedBox(width: 18.h),
                Flexible(
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25.r),
                        color: cSecondColor.withAlpha(50)),
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                    child: Text(
                      ' ${paymentModel.month ?? "0"} ${LocaleKeys.monthly.tr()}',
                      style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontSize: 16.sp,
                          fontFamily: 'Medium'),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    LocaleKeys.sum.tr(),
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
                SizedBox(width: 18.h),
                Flexible(
                  child: Text(
                    paymentModel.amount ?? '0',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 16.sp)
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    LocaleKeys.start_time.tr(),
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
                SizedBox(width: 18.h),
                Flexible(
                  child: Text(
                    formatterDate.format(
                        DateTime.parse(paymentModel.startDate ?? '0000-00-00')),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 16.sp)

                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    LocaleKeys.finished_time.tr(),
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
                SizedBox(width: 18.h),
                Flexible(
                  child: Text(
                    formatterDate.format(
                        DateTime.parse(paymentModel.endDate ?? '0000-00-00')),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 16.sp)

                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
