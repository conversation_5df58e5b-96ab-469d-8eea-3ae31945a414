import 'package:flutter/material.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/home/<USER>/pages/home.dart';
import 'package:ijrochi/features/payments/functional_lock_page.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ijrochi/di/dependency_injection.dart';

// ignore: must_be_immutable
class LockProvider extends StatelessWidget {
  final SharedPreferences prefs = di();

  var emulator = false;

  @override
  Widget build(BuildContext context) {

    bool isPaid = prefs.getBool(IS_PAYMENT_DONE) ?? true;

    print('Is payment done: $isPaid\n');

    // var isDemo = prefs.getBool(is_demo) ?? false;
    // if (isDemo) {
    //   emulator = true;
    // }

    return emulator
        ? HomePage()
        : isPaid
            ? HomePage()
            : FunctionalLockPage();
  }
}
