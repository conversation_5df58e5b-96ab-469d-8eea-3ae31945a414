import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/login/data/models/user_model.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/features/payments/payments.dart';
import 'package:ijrochi/features/payments/payments_history.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class PaymentSettings extends StatefulWidget {
  const PaymentSettings({Key? key}) : super(key: key);

  @override
  _PaymentSettingsState createState() => _PaymentSettingsState();
}

class _PaymentSettingsState extends State<PaymentSettings> {


  GetStorage gs = di();
  String? nextPaymentDate;
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');


  nextPaymentInitFunc() {
    getUserPaymentDate().then((value) {
      setState(() {
        nextPaymentDate = value;
        print(nextPaymentDate);
      });
    });
  }

  @override
  void initState() {
    nextPaymentInitFunc();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            LocaleKeys.payments.tr(),
            style: TextStyle(color: cWhiteColor),
          ),
          backgroundColor: cSecondColor,
          iconTheme: IconThemeData(color: cWhiteColor),
          elevation: 0,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 20.h,
            ),
            GestureDetector(
              onTap: () {
                ///
              },
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 7.5.h),
                decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                          color: cFirstColor.withOpacity(0.2),
                          spreadRadius: 10.r,
                          blurRadius: 20.r,
                          blurStyle: BlurStyle.normal),
                    ],
                    borderRadius: BorderRadius.circular(18.r),
                    color: Theme.of(context).cardTheme.color),
                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.add_alert,
                            color: cRedColor,
                            size: 22.h,
                          ),
                          SizedBox(width: 18.w),
                          Flexible(
                            child: Text(
                              LocaleKeys.next_payment_sum.tr(),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: 18.w),
                        ],
                      ),
                    ),
                    Expanded(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          formatterDate.format(DateTime.parse(nextPaymentDate ??
                              gs.read('paymentDate') ??
                              '1970-00-00')),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  CupertinoPageRoute(
                      builder: (context) => const PaymentsHistoryPage()),
                );
              },
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 7.5.h),
                decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                          color: cFirstColor.withOpacity(0.2),
                          spreadRadius: 10.r,
                          blurRadius: 20.r,
                          offset: Offset(0,15),
                          blurStyle: BlurStyle.normal),
                    ],
                    borderRadius: BorderRadius.circular(18.r),
                    color: Theme.of(context).cardTheme.color),
                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
                child: Row(
                  children: [
                    ///TODO: Check theming
                    Icon(Icons.history, size: 22.h),
                    SizedBox(width: 18.w),
                    Text(
                      LocaleKeys.payment_history.tr(),
                    ),
                  ],
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  CupertinoPageRoute(
                      builder: (context) => const PaymentsPage()),
                ).then((value) => nextPaymentInitFunc());
              },
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 7.5.h),
                decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                          color: cFirstColor.withOpacity(0.1),
                          spreadRadius: 10.r,
                          blurRadius: 20.r,
                          blurStyle: BlurStyle.normal,
                          offset: Offset(0, 10.w)),
                    ],
                    borderRadius: BorderRadius.circular(18.r),
                    gradient: cFirstGradient),
                padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
                child: Row(
                  children: [
                    Spacer(),
                    ///TODO: Check theming
                    Icon(
                      Icons.wallet,
                      size: 22.h,
                      color: cWhiteColor,
                    ),
                    SizedBox(width: 18.w),
                    Text(
                      LocaleKeys.make_payment.tr(),
                      style: TextStyle(
                          color: cWhiteColor, fontWeight: FontWeight.w500),
                    ),
                    Spacer(),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 20.h,
            ),
          ],
        ),
      ),
    );
  }
}

Future<String?> getUserPaymentDate() async {
  final Dio dio = di();
  GetStorage gs = di();
  var userId = gs.read(ID) ?? '';

  try {
    final response = await dio.get(userPath,
        queryParameters: {'_id': userId},
        options: Options(headers: <String, String>{
          'Content-Type': 'application/json',
        }));
    final data = response.data;
    print(data.toString());
    if (response.statusCode == 200) {
      var userInfo = UserModel.fromJson(data);
      gs.write('paymentDate', userInfo.endDate ?? '0000-00-00');
      print('End date ${userInfo.endDate}');
      return userInfo.endDate;
    } else {
      CustomToast.showToast(data.toString());
      print(data.toString());
      return null;
    }
  } catch (e) {
    print(e);
    return null;
  }
}
