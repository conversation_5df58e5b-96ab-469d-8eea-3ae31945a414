import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/core/widgets/dialog_frame.dart';
import 'package:ijrochi/core/widgets/dotted_border.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/login/data/models/user_model.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:uzpay/enums.dart';
import 'package:uzpay/objects.dart' hide CustomToast;
import 'package:uzpay/uzpay.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import 'models/tariff_model.dart';
import 'widgets/expansion_item.dart';

class PaymentsPage extends StatefulWidget {
  const PaymentsPage({Key? key}) : super(key: key);

  @override
  _PaymentsPageState createState() => _PaymentsPageState();
}

class _PaymentsPageState extends State<PaymentsPage> {
  SharedPreferences prefs = di();
  TariffModel? _groupValueTariff;
  late Future<List<TariffModel>> tariffs;

  checkNetwork() async {
    NetworkInfo networkInfo = di();

    if (!await networkInfo.isConnected) {
      CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
    }
  }

  @override
  void initState() {
    checkNetwork();
    tariffs = getTariffs();
    tariffs.then((value) {
      setState(() {
        _groupValueTariff = value.asMap().containsKey(0)
            ? value.first
            : TariffModel(id: 'x', price: 0, term: 0);
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            LocaleKeys.make_payment.tr(),
            style: TextStyle(color: cWhiteColor),
          ),
          backgroundColor: cSecondColor,
          iconTheme: IconThemeData(color: cWhiteColor),
          elevation: 0,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 30.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(LocaleKeys.tariff.tr(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: context.isTablet ? 16.sp : 20.sp)),
                  SizedBox(
                    height: 10.h,
                  ),
                  ExpansionItem(
                      title:
                          "${_groupValueTariff?.term ?? '0'} ${LocaleKeys.to_month.tr()} — ${_groupValueTariff?.price ?? '0'} ${LocaleKeys.som.tr()}",
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 10.h),
                        child: FutureBuilder<List<TariffModel>>(
                          future: tariffs, // async work
                          builder: (BuildContext context,
                              AsyncSnapshot<List<TariffModel>> snapshot) {
                            switch (snapshot.connectionState) {
                              // case ConnectionState.waiting:
                              //   return Text('Loading....');
                              default:
                                if (snapshot.hasError)
                                  return Text('Error: ${snapshot.error}');
                                else
                                  return Column(
                                    children: snapshot.data
                                            ?.map((TariffModel value) {
                                          return RadioListTile(
                                            title: Text(
                                              "${value.term} ${LocaleKeys.to_month.tr()} — ${value.price} ${LocaleKeys.som.tr()}",
                                              style: TextStyle(
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodySmall
                                                      ?.color,
                                                  fontSize: context.isTablet
                                                      ? 12.sp
                                                      : 16.sp),
                                            ),
                                            value: value,
                                            groupValue: _groupValueTariff,
                                            onChanged: (newValue) => setState(
                                                () => _groupValueTariff =
                                                    newValue as TariffModel),
                                          );
                                        }).toList() ??
                                        [],
                                  );
                            }
                          },
                        ),
                      )),
                  SizedBox(
                    height: 25.h,
                  ),
                  Text(LocaleKeys.payment_system.tr(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: context.isTablet ? 16.sp : 20.sp)),
                  SizedBox(
                    height: 10.h,
                  ),
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: InkWell(
                          onTap: () => doPayment(PaymentSystem.Click),
                          child: Card(
                            elevation: 1,
                            color: cWhiteColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.r)),
                            child: Center(
                              child: Container(
                                  height: 90.h,
                                  // width: 200.w,
                                  child: Image.asset(
                                    Assets.imagesClickLogo,
                                    width: 120.w,
                                    fit: BoxFit.contain,
                                  )),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        flex: 1,
                        child: InkWell(
                          onTap: () => doPayment(PaymentSystem.Payme),
                          child: Card(
                            elevation: 1,
                            color: cWhiteColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.r)),
                            child: Center(
                              child: Container(
                                  height: 90.h,
                                  // width: 200.w,
                                  child: Image.asset(
                                    Assets.imagesPaymeLogoActive,
                                    width: 100.w,
                                    fit: BoxFit.contain,
                                  )),
                            ),
                          ),
                        ),
                      )
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  doPayment(PaymentSystem paymentSystem) async {
    var price = _groupValueTariff?.price ?? 0;
    int TRANS_ID = await getPaymentUserId() ?? -1;

    ///Avvaliga parametrlarni belgilab olamiz
    var paymentParams = Params(
      paymeParams: PaymeParams(
          transactionParam: TRANS_ID.toString(),
          merchantId: PAYME_MERCHANT_ID,
          // Quyidagilar ixtiyoriy parametrlar
          accountObject: 'userId', // Agar o'zgargan bo'lsa
          headerTitle: LocaleKeys.payment_with_payme.tr()), // Header yozuvi

      clickParams: ClickParams(
          transactionParam: TRANS_ID.toString(),
          merchantId: CLICK_MERCHANT_ID,
          serviceId: CLICK_SERVICE_ID,
          merchantUserId: CLICK_MERCHANT_USER_ID,
          // Quyidagilar ixtiyoriy parametrlar
          headerTitle: LocaleKeys.payment_with_click.tr()), // Header yozuvi
    );

    if (price > 500 && TRANS_ID != -1) {
      showDialog(
          context: context,
          builder: (context) => AllDialogSkeleton(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 20.h, horizontal: 10.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 10.h,
                                ),
                                ZoomTapAnimation(
                                  child: Theme(
                                    data: ThemeData(
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      scaffoldBackgroundColor:
                                          Theme.of(context).cardTheme.color,
                                    ),
                                    child: InkWell(
                                      onTap: () async {
                                        Navigator.pop(context);

                                        /// Tashqi brauzer orqali to'lov chekini ochish
                                        UzPay.doPayment(context,
                                            amount: price.toDouble(),
                                            // To'lov summasi
                                            paymentSystem: paymentSystem ==
                                                    PaymentSystem.Payme
                                                ? PaymentSystem.Payme
                                                : PaymentSystem.Click,
                                            paymentParams: paymentParams,
                                            browserType: BrowserType.External,

                                            // Quyida ixtiyoriy parametr
                                            externalBrowserMenuItem:
                                                ChromeSafariBrowserMenuItem(
                                                    id: 1,
                                                    label: 'Ijrochi Support',
                                                    action: (url, title) {
                                                      launchCustomUrl(
                                                          SUPPORT_TG);
                                                    }));
                                      },
                                      child: DottedBorderWidget(
                                        child: Center(
                                          child: Padding(
                                            padding: EdgeInsets.all(8.h),
                                            child: Column(
                                              children: [
                                                Image.asset(
                                                  Assets.imagesBrowsers,
                                                  height: 40.h,
                                                  // color: cFirstColor,
                                                ),
                                                SizedBox(
                                                  height: 10.h,
                                                ),
                                                Container(
                                                  width: 100.w,
                                                  child: Text(
                                                    LocaleKeys.outer_browser
                                                        .tr(),
                                                    textAlign: TextAlign.center,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 3,
                                                    style: TextStyle(
                                                        fontSize: 18.sp,
                                                        fontFamily: 'Medium',
                                                        color: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium
                                                            ?.color),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ]),
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        flex: 1,
                        child: Column(children: [
                          SizedBox(
                            height: 10.h,
                          ),
                          ZoomTapAnimation(
                            child: Theme(
                              data: ThemeData(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                scaffoldBackgroundColor:
                                    Theme.of(context).cardTheme.color,
                              ),
                              child: InkWell(
                                onTap: () {
                                  Navigator.pop(context);

                                  /// Ichki brauzer orqali to'lov chekini ochish
                                  UzPay.doPayment(context,
                                      amount: price.toDouble(),
                                      // To'lov summasi
                                      paymentSystem:
                                          paymentSystem == PaymentSystem.Payme
                                              ? PaymentSystem.Payme
                                              : PaymentSystem.Click,
                                      paymentParams: paymentParams,
                                      browserType: BrowserType.Internal,

                                      // Quyida ixtiyoriy parametr
                                      externalBrowserMenuItem:
                                          ChromeSafariBrowserMenuItem(
                                              id: 1,
                                              label: 'Ijrochi Support',
                                              action: (url, title) {
                                                launchCustomUrl(SUPPORT_TG);
                                              }));
                                },
                                child: DottedBorderWidget(
                                  child: Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(8.w),
                                      child: Column(
                                        children: [
                                          Image.asset(
                                            Assets.imagesBrowser,
                                            height: 40.h,
                                            // color: cFirstColor,
                                          ),
                                          SizedBox(
                                            height: 10.h,
                                          ),
                                          Container(
                                            width: 100.w,
                                            child: Text(
                                              LocaleKeys.inner_browser.tr(),
                                              textAlign: TextAlign.center,
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 3,
                                              style: TextStyle(
                                                  fontSize: 18.sp,
                                                  fontFamily: 'Medium',
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.color),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ]),
                      )
                    ],
                  ),
                ),
                title: LocaleKeys.choose_browser.tr(),
                icon: Assets.iconsTickCircle,
                color: Theme.of(context).cardTheme.color,
                textColor: Theme.of(context).textTheme.displayMedium?.color,
              ));
    } else {
      CustomToast.showToast(LocaleKeys.payment_impossible.tr());
    }
  }

  Future<int?> getPaymentUserId() async {
    final Dio dio = di();
    GetStorage gs = di();
    var userId = gs.read(ID) ?? '';

    try {
      final response = await dio.get(userPath,
          queryParameters: {'_id': userId},
          options: Options(
              headers: <String, String>{'Content-Type': 'application/json'}));
      final data = response.data;
      print(data.toString());
      if (response.statusCode == 200) {
        var userInfo = UserModel.fromJson(data);
        return userInfo.userId;
      } else {
        CustomToast.showToast(data.toString());
        print(data.toString());
        return null;
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          CustomToast.showToast('Server error: ' + "${e.response!.data}");
          print('Server error: ' + "${e.response!.data}");
        }
      }
      return null;
    }
  }

  Future<List<TariffModel>> getTariffs() async {
    final Dio dio = di();

    List<TariffModel> tariffs = [];

    try {
      final response = await dio.get(paymentPlansPath,
          options: Options(
              headers: <String, String>{'Content-Type': 'application/json'}));
      final data = response.data;
      print(data.toString());
      if (response.statusCode == 200) {
        for (int i = 0; i < (data['docs'].length); i++) {
          tariffs.add(TariffModel.fromJson(data['docs'][i]));
        }
        return tariffs;
      } else {
        CustomToast.showToast(data.toString());
        print(data.toString());
        return [];
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          CustomToast.showToast('Server error: ' + "${e.response!.data}");
          print('Server error: ' + "${e.response!.data}");
        }
      }
      return [];
    }
  }
}
