import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/core/widgets/empty_list_widget.dart';
import 'package:ijrochi/core/widgets/failure_widget.dart';
import 'package:ijrochi/core/widgets/no_more_widget.dart';
import 'package:ijrochi/features/task_detail/page/task_detail_page.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/features/tasks/task/presentation/bloc/task_bloc.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/task_bloc/task_count_bloc.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/widget/task_item.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

class TaskPage extends StatefulWidget {
  final TaskCountEnumStatus status;
  final TaskTypeEnum taskTypeEnum;
  final TaskBloc bloc;
  final TaskCountBloc taskCountBloc;

  const TaskPage(
      {super.key,
      required this.status,
      required this.taskTypeEnum,
      required this.bloc,
      required this.taskCountBloc});

  @override
  State<TaskPage> createState() => _TaskPageState();
}

class _TaskPageState extends State<TaskPage>
    with AutomaticKeepAliveClientMixin {
  final PagingController<int, TaskDocs> _pagingController =
      PagingController(firstPageKey: 1);
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');

  late bool isDark;
  bool reset = false;
  bool refresh = true;
  String? moderator;
  String? date;
  List<TaskDocs> list = [];
  var headerTimes = Map<String, String>();
  var prevPageKey = 0;

  pagingComponent() {
    _pagingController.addPageRequestListener((pageKey) {
      if (pageKey == 1) {
        widget.bloc.add(GetTaskEvent(
            status: widget.status,
            page: 1,
            refresh: refresh,
            reset: true,
            date: date,
            moderator: moderator,
            taskTypeEnum: widget.taskTypeEnum));
      } else {
        widget.bloc.add(GetTaskEvent(
            status: widget.status,
            page: pageKey,
            refresh: refresh,
            reset: false,
            date: date,
            moderator: moderator,
            taskTypeEnum: widget.taskTypeEnum));
      }
    });
    if (refresh) {
      ///First event when online
      _pagingController.notifyPageRequestListeners(1);
    } else {
      ///First event when offline
      widget.bloc.add(GetTaskEvent(
          status: widget.status,
          page: 1,
          refresh: refresh,
          reset: reset,
          date: date,
          moderator: moderator,
          taskTypeEnum: widget.taskTypeEnum));
    }
  }

  handleRefresh(bool refresh) async {
    reset = true;
    this.refresh = refresh;
    this.moderator = null;
    this.date = null;

    ///Prevent adding duplicate appends at once with one page key
    prevPageKey = 0;
    headerTimes.clear();
    _pagingController.refresh();
  }

  filter({required String? moderatorId, required String? date}) async {
    ///Prevent adding duplicate appends at once with one page key
    print(date);
    prevPageKey = 0;
    this.refresh = true;
    this.moderator = moderatorId;
    this.date = date;
    headerTimes.clear();
    _pagingController.refresh();
  }

  @override
  void initState() {
    super.initState();
    pagingComponent();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      backgroundColor: isDark ? cBackgroundColor : cGrayColor.withAlpha(30),
      body: BlocConsumer<TaskBloc, TaskState>(
        buildWhen: (previous, current) {
          if (current.status == TaskStatus.filtered) {
            filter(
              moderatorId: current.moderator,
              date: current.date,
            );
            return false;
          }
          return true;
        },
        listener: (context, state) {
          print(state.status);
          if (state.status == TaskStatus.success) {
            if (state.message != null) {
              CustomToast.showToast(state.message.toString());
            }
            list = state.taskModel?.docs ?? [];
            bool isLastPage =
                state.taskModel?.totalPages == state.taskModel?.page;
            int? currentPage = state.taskModel?.page ?? 0;
            final _next = currentPage + 1;

            if (prevPageKey != currentPage) {
              if (isLastPage) {
                _pagingController.appendLastPage(list);
              } else {
                _pagingController.appendPage(list, _next);
              }
              prevPageKey = currentPage;
            }
          }
          else if(state.status==TaskStatus.failure){
            _pagingController.error=state.message;
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () async {
              handleRefresh(true);
            },
            child: PagedListView(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                pagingController: _pagingController,
                physics: BouncingScrollPhysics(),
                builderDelegate: PagedChildBuilderDelegate<TaskDocs>(
                    noItemsFoundIndicatorBuilder: (_) => EmptyListWidget(
                      onTap: () {
                        handleRefresh(true);
                      },
                      title: LocaleKeys.empty_list.tr(),
                      isDark: isDark,
                    ),
                    noMoreItemsIndicatorBuilder: (_) => NoMoreWidget(
                      onTap: () {
                        handleRefresh(true);
                      },
                    ),
                    newPageProgressIndicatorBuilder: (_)=> SizedBox(height: 200.h,child: CupertinoActivityIndicator(radius: 20.r,),),
                    firstPageProgressIndicatorBuilder: (_)=>Center(child: CupertinoActivityIndicator(radius: 20.r,),),
                    firstPageErrorIndicatorBuilder: (_)=>FailureWidget(
                      onTap: () {
                        handleRefresh(true);
                      },
                      title: state.message ?? LocaleKeys.unknown_error.tr(),
                      isDark: isDark,
                    ),
                    newPageErrorIndicatorBuilder: (_)=>FailureWidget(
                      onTap: () {
                        handleRefresh(true);
                      },
                      title: state.message ?? LocaleKeys.unknown_error.tr(),
                      isDark: isDark,
                    ),
                    itemBuilder: (context, item, index) {
                      String time = formatterDate
                          .format(DateTime.parse(item.createdAt.toString()));
                      String id = item.taskId ?? "";
                      if ((!headerTimes.containsKey(time)) ||
                          headerTimes.containsValue(id)) {
                        headerTimes[time] = id;
                        return TaskItem(
                          taskDocs: item,
                          visible: true,
                          onTapItem: (TaskDocs taskDocs) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        TaskDetailPage.screen(
                                            taskDocs: item,
                                            status: widget.status)))
                                .then((value) {
                              if (value == true) {
                                widget.taskCountBloc.add(
                                    GetTaskCountEvent(status: widget.status));
                                handleRefresh(true);
                              }
                            });
                          },
                        );
                      } else {
                        return TaskItem(
                          taskDocs: item,
                          visible: false,
                          onTapItem: (TaskDocs taskDocs) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        TaskDetailPage.screen(
                                            taskDocs: item,
                                            status: widget.status)))
                                .then((value) {
                              if (value == true) {
                                widget.taskCountBloc.add(
                                    GetTaskCountEvent(status: widget.status));
                                handleRefresh(true);
                              }
                            });
                          },
                        );
                      }
                    })),
          );

        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
