import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/core/widgets/empty_list_widget.dart';
import 'package:ijrochi/core/widgets/failure_widget.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/pages/filter_page.dart';
import 'package:ijrochi/features/task_detail/page/task_detail_page.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/features/tasks/task/presentation/archive_parent_page.dart';
import 'package:ijrochi/features/tasks/task/presentation/bloc/task_bloc.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/pages/task_parent_page.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/task_bloc/task_count_bloc.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/widget/task_item.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

import '../../../../core/widgets/no_more_widget.dart';

class LateTaskPage extends StatefulWidget {
  const LateTaskPage({
    super.key,
  });

  static Widget screen() {
    return MultiBlocProvider(providers: [
      BlocProvider(create: (context) => di<TaskBloc>()),
    ], child: LateTaskPage());
  }

  @override
  State<LateTaskPage> createState() => _LateTaskPageState();
}

class _LateTaskPageState extends State<LateTaskPage>
    with AutomaticKeepAliveClientMixin {
  final PagingController<int, TaskDocs> _pagingController =
      PagingController(firstPageKey: 1);
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');

  late bool isDark;
  bool reset = false;
  bool refresh = true;
  String? moderator;
  String? date;
  List<TaskDocs> list = [];
  var headerTimes = Map<String, String>();
  var prevPageKey = 0;
  int selectedTabIndex = 0;

  pagingComponent() {
    _pagingController.addPageRequestListener((pageKey) {
      if (pageKey == 1) {
        BlocProvider.of<TaskBloc>(context).add(GetTaskEvent(
            status: TaskCountEnumStatus.late,
            page: 1,
            refresh: refresh,
            reset: true,
            date: date,
            moderator: moderator,
            taskTypeEnum: TaskTypeEnum.late));
      } else {
        BlocProvider.of<TaskBloc>(context).add(GetTaskEvent(
            status: TaskCountEnumStatus.late,
            page: pageKey,
            refresh: refresh,
            reset: false,
            date: date,
            moderator: moderator,
            taskTypeEnum: TaskTypeEnum.late));
      }
    });
    if (refresh) {
      ///First event when online
      _pagingController.notifyPageRequestListeners(1);
    } else {
      ///First event when offline
      BlocProvider.of<TaskBloc>(context).add(GetTaskEvent(
        status: TaskCountEnumStatus.late,
        page: 1,
        refresh: refresh,
        reset: reset,
        date: date,
        moderator: moderator,
        taskTypeEnum: TaskTypeEnum.late,
      ));
    }
  }

  handleRefresh(bool refresh) async {
    reset = true;
    this.refresh = refresh;
    this.moderator = null;
    this.date = null;

    ///Prevent adding duplicate appends at once with one page key
    prevPageKey = 0;
    headerTimes.clear();
    _pagingController.refresh();
  }

  filter({required String? moderatorId, required String? date}) async {
    ///Prevent adding duplicate appends at once with one page key
    print(date);
    prevPageKey = 0;
    this.refresh = true;
    this.moderator = moderatorId;
    this.date = date;
    headerTimes.clear();
    _pagingController.refresh();
  }

  @override
  void initState() {
    super.initState();
    pagingComponent();
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    super.build(context);
    return Container(
      color: cWhiteColor,
      child: Scaffold(
        backgroundColor: isDark ? cBackgroundColor : cGrayColor.withAlpha(30),
        appBar: AppBar(
          elevation: 2,
          title: Center(
            child: Column(
              children: [
                Text(
                  LocaleKeys.late.tr(),
                  style:
                      TextStyle(fontWeight: FontWeight.w500, fontSize: 16.sp),
                ),
              ],
            ),
          ),
          actions: [
            InkWell(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () async {
                showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                            topRight: Radius.circular(20.r),
                            topLeft: Radius.circular(20.r))),
                    builder: (BuildContext dialogContext) {
                      return FilterPage(
                        onFilterTap: (value) {
                          BlocProvider.of<TaskBloc>(context).add(FilterEvent(
                              date: value.date,
                              moderator: value.moderator?.id,
                              filteredStatus: TaskStatus.filtered));
                        },
                      );
                    });
              },
              child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: SvgPicture.asset(
                      Assets.iconsFilter,
                      color: isDark ? cWhiteColor : cFirstColor,
                    ),
                  )),
            )
          ],
        ),
        body: Stack(
          children: [
            BlocConsumer<TaskBloc, TaskState>(
              buildWhen: (previous, current) {
                if (current.status == TaskStatus.filtered) {
                  filter(
                    moderatorId: current.moderator,
                    date: current.date,
                  );
                  return false;
                }
                return true;
              },
              listener: (context, state) {
                if (state.status == TaskStatus.success) {
                  if (state.message != null) {
                    CustomToast.showToast(state.message.toString());
                  }
                  list = state.taskModel?.docs ?? [];
                  bool isLastPage =
                      state.taskModel?.totalPages == state.taskModel?.page;
                  int? currentPage = state.taskModel?.page ?? 0;
                  final _next = currentPage + 1;

                  if (prevPageKey != currentPage) {
                    if (isLastPage) {
                      _pagingController.appendLastPage(list);
                    } else {
                      _pagingController.appendPage(list, _next);
                    }
                    prevPageKey = currentPage;
                  }
                }
                else if(state.status==TaskStatus.failure){
                  _pagingController.error=state.message;
                }
              },
              builder: (context, state) {
                return RefreshIndicator(
                  onRefresh: () async {
                    handleRefresh(true);
                  },
                  child: PagedListView(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      pagingController: _pagingController,
                      physics: BouncingScrollPhysics(),
                      builderDelegate: PagedChildBuilderDelegate<TaskDocs>(
                          noItemsFoundIndicatorBuilder: (_) =>
                              EmptyListWidget(
                                onTap: () {
                                  handleRefresh(true);
                                },
                                title: LocaleKeys.empty_list.tr(),
                                isDark: isDark,
                              ),
                          noMoreItemsIndicatorBuilder: (_) => NoMoreWidget(
                            onTap: () {
                              handleRefresh(true);
                            },
                          ),
                          newPageProgressIndicatorBuilder: (_)=> SizedBox(height: 200.h,child: CupertinoActivityIndicator(radius: 20.r,),),
                          firstPageErrorIndicatorBuilder: (_)=>FailureWidget(
                            onTap: () {
                              handleRefresh(true);
                            },
                            title: state.message ?? LocaleKeys.unknown_error.tr(),
                            isDark: isDark,
                          ),
                          newPageErrorIndicatorBuilder: (_)=>FailureWidget(
                            onTap: () {
                              handleRefresh(true);
                            },
                            title: state.message ?? LocaleKeys.unknown_error.tr(),
                            isDark: isDark,
                          ),
                          firstPageProgressIndicatorBuilder: (_)=>Center(child: CupertinoActivityIndicator(radius: 20.r,),),
                          itemBuilder: (context, item, index) {
                            String time = formatterDate.format(
                                DateTime.parse(item.createdAt.toString()));
                            String id = item.taskId ?? "";
                            if ((!headerTimes.containsKey(time)) ||
                                headerTimes.containsValue(id)) {
                              headerTimes[time] = id;
                              return TaskItem(
                                taskDocs: item,
                                visible: true,
                                onTapItem: (TaskDocs taskDocs) {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              TaskDetailPage.screen(
                                                  taskDocs: item,
                                                  status: TaskCountEnumStatus
                                                      .progress))).then(
                                          (value) {
                                        if (value == true) {
                                          handleRefresh(true);
                                        }
                                      });
                                },
                              );
                            } else {
                              return TaskItem(
                                taskDocs: item,
                                visible: false,
                                onTapItem: (TaskDocs taskDocs) {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              TaskDetailPage.screen(
                                                  taskDocs: item,
                                                  status: TaskCountEnumStatus
                                                      .progress))).then(
                                          (value) {
                                        if (value == true) {
                                          handleRefresh(true);
                                        }
                                      });
                                },
                              );
                            }
                          })),
                );
              },
            ),
            Positioned(
              bottom: 24.h,
              right: 50.h,
              left: 50.h,
              child: Material(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24.r)),
                elevation: 20.w,
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: 52.h,
                  decoration: BoxDecoration(
                    color: isDark ? cFirstColorDark : cWhiteColor,
                    borderRadius: BorderRadius.circular(24.r),
                    border: Border.all(
                        width: 1, color: isDark ? cWhiteColor : cBlackColor),
                  ),
                  child: Row(children: [
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        TaskParentPage.screen(
                                            status: TaskCountEnumStatus.news,
                                            title: LocaleKeys.task_new.tr()),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.add_chart,
                              color: isDark ? cWhiteColor : cBlackColor,
                              size: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        TaskParentPage.screen(
                                            status:
                                                TaskCountEnumStatus.progress,
                                            title:
                                                LocaleKeys.task_process.tr()),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: SvgPicture.asset(
                              Assets.iconsIcTaskProcess,
                              color: isDark ? cWhiteColor : cBlackColor,
                              width: 24.w,
                              height: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        LateTaskPage.screen(),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.error_outline,
                              color: isDark ? cBlueLight : cFirstColor,
                              size: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        ArchiveParentTaskPage.screen(),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.archive_outlined,
                              color: isDark ? cWhiteColor : cBlackColor,
                              size: 24.w,
                            ))),
                  ]),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
