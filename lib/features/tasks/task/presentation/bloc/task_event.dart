part of 'task_bloc.dart';

abstract class TaskEvent extends Equatable {
  const TaskEvent();
}

class GetTaskEvent extends TaskEvent {
  final TaskCountEnumStatus status;
  final TaskTypeEnum taskTypeEnum;
  final int page;
  final bool refresh;
  final bool reset;
  final String? date;
  final String? moderator;

  GetTaskEvent(
      {required this.status,
      required this.taskTypeEnum,
      required this.page,
      required this.refresh,
      required this.reset,
      required this.date,
      required this.moderator});

  @override
  List<Object?> get props => [
        status,
        taskTypeEnum,
        page,
        refresh,
        reset,
        date,
        moderator,
      ];
}

class FilterEvent extends TaskEvent {
  final TaskCountEnumStatus? status;
  final TaskTypeEnum? taskTypeEnum;
  final String? date;
  final String? moderator;
  final TaskStatus? filteredStatus;

  FilterEvent(
      {this.status,
      this.taskTypeEnum,
      required this.date,
      required this.moderator,
      this.filteredStatus});

  @override
  List<Object?> get props => [status, taskTypeEnum, date, moderator];
}
