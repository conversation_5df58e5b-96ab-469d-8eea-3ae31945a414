part of 'task_bloc.dart';

enum TaskStatus {
  initial,
  loading,
  failure,
  success,
  noInternet,
  filteredDone,
  filteredNotDone,
  filtered
}

class TaskState extends Equatable {
  final TaskStatus status;
  final String? message;
  final TaskModel? taskModel;
  final String? moderator;
  final String? date;
  final TaskCountEnumStatus? taskCountEnumStatus;
  final TaskTypeEnum? taskTypeEnum;

  TaskState(
      {required this.status,
      this.message,
      this.taskModel,
      this.moderator,
      this.date,
      this.taskCountEnumStatus,
      this.taskTypeEnum});

  static TaskState initial() => TaskState(status: TaskStatus.initial);

  TaskState copyWith(
          {TaskStatus? status,
          String? message,
          TaskModel? taskModel,
          String? moderator,
          String? date,
          TaskCountEnumStatus? taskCountEnumStatus,
          TaskTypeEnum? taskTypeEnum}) =>
      TaskState(
          status: status ?? this.status,
          message: message ?? this.message,
          taskModel: taskModel ?? this.taskModel,
          moderator: moderator ?? this.moderator,
          date: date ?? this.date,
          taskCountEnumStatus: taskCountEnumStatus ?? this.taskCountEnumStatus,
          taskTypeEnum: taskTypeEnum ?? this.taskTypeEnum);

  @override
  List<Object?> get props => [
        status,
        message,
        taskModel,
        moderator,
        date,
        taskCountEnumStatus,
        taskTypeEnum,
      ];
}
