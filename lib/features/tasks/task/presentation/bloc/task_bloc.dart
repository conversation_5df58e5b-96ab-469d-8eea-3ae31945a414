import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/features/tasks/task/datasources/task_local_datasource.dart';
import 'package:ijrochi/features/tasks/task/datasources/task_remote_datasource.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

part 'task_event.dart';

part 'task_state.dart';

class TaskBloc extends Bloc<TaskEvent, TaskState> {
  final TaskRemoteDatasource taskRemoteDatasource;
  final TaskLocalDataSource taskLocalDataSource;
  final NetworkInfo networkInfo;

  TaskBloc(
      {required this.taskRemoteDatasource,
      required this.taskLocalDataSource,
      required this.networkInfo})
      : super(TaskState.initial()) {
    on<GetTaskEvent>(getTask, transformer: droppable());
    on<FilterEvent>(filter, transformer: droppable());
  }

  FutureOr<void> getTask(GetTaskEvent event, Emitter<TaskState> emit) async {
    if (await networkInfo.isConnected) {
      if (event.reset) {
        emit(state.copyWith(status: TaskStatus.loading));
      }
      try {
        TaskModel taskModel = await taskRemoteDatasource.getNotifications(
            status: event.status,
            taskTypeEnum: event.taskTypeEnum,
            page: event.page,
            moderator: event.moderator,
            date: event.date);

        taskLocalDataSource.setNotifications(
            task: taskModel.docs,
            status: event.status,
            taskTypeEnum: event.taskTypeEnum,
            reset: event.reset);
        emit(state.copyWith(status: TaskStatus.success, taskModel: taskModel));
      } on DioException catch (e) {
        print(e);
        emit(state.copyWith(
            status: TaskStatus.failure, message: LocaleKeys.error.tr()));
      } catch (e) {
        print(e);
        emit(state.copyWith(
            status: TaskStatus.failure, message: LocaleKeys.error.tr()));
      }
    } else {
      List<TaskDocs> list = await taskLocalDataSource.getTasks(
          status: event.status, taskTypeEnum: event.taskTypeEnum, page: 1);
      emit(state.copyWith(
          status: TaskStatus.success,
          taskModel: TaskModel(docs: list, totalPages: 1, page: 1)));
    }
  }

  filter(FilterEvent event, Emitter<TaskState> emit) {
    print("FILTER:${event.taskTypeEnum}");
    emit(state.copyWith(
        taskCountEnumStatus: event.status,
        taskTypeEnum: event.taskTypeEnum,
        date: event.date,
        moderator: event.moderator,
        status: event.filteredStatus));
  }
}
