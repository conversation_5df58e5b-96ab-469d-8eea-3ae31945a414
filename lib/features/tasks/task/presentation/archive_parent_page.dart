import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/core/widgets/empty_list_widget.dart';
import 'package:ijrochi/core/widgets/failure_widget.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/pages/filter_page.dart';
import 'package:ijrochi/features/task_detail/page/task_detail_page.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/features/tasks/task/presentation/archive_done_page.dart';
import 'package:ijrochi/features/tasks/task/presentation/archive_not_done_page.dart';
import 'package:ijrochi/features/tasks/task/presentation/bloc/task_bloc.dart';
import 'package:ijrochi/features/tasks/task/presentation/late_task_page.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/pages/task_parent_page.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/task_bloc/task_count_bloc.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/widget/task_item.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

class ArchiveParentTaskPage extends StatefulWidget {
  const ArchiveParentTaskPage({
    super.key,
  });

  static Widget screen() {
    return MultiBlocProvider(providers: [
      BlocProvider(create: (context) => di<TaskBloc>()),
    ], child: ArchiveParentTaskPage());
  }

  @override
  State<ArchiveParentTaskPage> createState() => _ArchiveParentTaskPageState();
}

class _ArchiveParentTaskPageState extends State<ArchiveParentTaskPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late bool isDark;
  int selectedTabIndex = 0;

  late TaskBloc taskBloc;

  @override
  void initState() {
    super.initState();
    taskBloc = BlocProvider.of<TaskBloc>(context);
    _tabController = TabController(vsync: this, initialIndex: 0, length: 2);
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
        appBar: AppBar(
          elevation: 2,
          title: Center(
            child: Column(
              children: [
                Text(
                  LocaleKeys.task_archive.tr(),
                  style:
                      TextStyle(fontWeight: FontWeight.w500, fontSize: 16.sp),
                ),
              ],
            ),
          ),
          actions: [
            InkWell(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () async {
                showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                            topRight: Radius.circular(20.r),
                            topLeft: Radius.circular(20.r))),
                    builder: (BuildContext context) {
                      return FilterPage(
                        onFilterTap: (value) {
                          taskBloc.add(FilterEvent(
                              date: value.date,
                              moderator: value.moderator?.id,
                              taskTypeEnum: _tabController.index == 0
                                  ? TaskTypeEnum.done
                                  : TaskTypeEnum.not_done,
                              status: _tabController.index == 0
                                  ? TaskCountEnumStatus.done
                                  : TaskCountEnumStatus.not_done,
                              filteredStatus: _tabController.index == 0
                                  ? TaskStatus.filteredDone
                                  : TaskStatus.filteredNotDone));
                        },
                      );
                    });
              },
              child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: SvgPicture.asset(
                      Assets.iconsFilter,
                      color: isDark ? cWhiteColor : cFirstColor,
                    ),
                  )),
            )
          ],
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(40.h),
            child: TabBar(controller: _tabController, tabs: [
              Container(
                alignment: Alignment.center,
                height: 40.h,
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: Text(
                  LocaleKeys.done.tr(),
                  style: TextStyle(
                      color: isDark ? cWhiteColor : cBlackColor,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400),
                ),
              ),
              Container(
                alignment: Alignment.center,
                height: 40.h,
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: Text(
                  LocaleKeys.not_done.tr(),
                  style: TextStyle(
                      color: fastTabColor,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400),
                ),
              ),
            ]),
          ),
        ),
        body: Stack(
          children: [
            TabBarView(
              children: [
                ArchiveDoneTaskPage(
                  taskBloc: taskBloc,
                ),
                ArchiveNotDoneTaskPage(
                  taskBloc: taskBloc,
                )
              ],
              controller: _tabController,
            ),
            Positioned(
              bottom: 24.h,
              right: 50.h,
              left: 50.h,
              child: Material(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24.r)),
                elevation: 20.w,
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: 52.h,
                  decoration: BoxDecoration(
                    color: isDark ? cFirstColorDark : cWhiteColor,
                    borderRadius: BorderRadius.circular(24.r),
                    border: Border.all(
                        width: 1, color: isDark ? cWhiteColor : cBlackColor),
                  ),
                  child: Row(children: [
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        TaskParentPage.screen(
                                            status: TaskCountEnumStatus.news,
                                            title: LocaleKeys.task_new.tr()),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.add_chart,
                              color: isDark ? cWhiteColor : cBlackColor,
                              size: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        TaskParentPage.screen(
                                            status:
                                                TaskCountEnumStatus.progress,
                                            title:
                                                LocaleKeys.task_process.tr()),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: SvgPicture.asset(
                              Assets.iconsIcTaskProcess,
                              color: isDark ? cWhiteColor : cBlackColor,
                              width: 24.w,
                              height: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        LateTaskPage.screen(),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.error_outline,
                              color: isDark ? cWhiteColor : cBlackColor,
                              size: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        ArchiveParentTaskPage.screen(),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.archive_outlined,
                              color: isDark ? cBlueLight : cFirstColor,
                              size: 24.w,
                            ))),
                  ]),
                ),
              ),
            ),
          ],
        ));
  }
}
