import 'package:dio/dio.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';

abstract class TaskRemoteDatasource {
  Future<TaskModel> getNotifications(
      {required TaskCountEnumStatus status,
      required TaskTypeEnum taskTypeEnum,
      required int page,
      String? date,
      String? moderator});
}

class TaskRemoteDatasourceImpl extends TaskRemoteDatasource {
  final Dio dio;

  TaskRemoteDatasourceImpl({required this.dio});

  @override
  Future<TaskModel> getNotifications(
      {required TaskCountEnumStatus status,
      required TaskTypeEnum taskTypeEnum,
      required int page,
      String? date,
      String? moderator}) async {
    print("type:${taskTypeEnum}");
    var query = {
      "type": taskTypeEnum.name,
      "page": page
    };
    if (date != null) {
      query['date'] = date;
    }
    if (moderator != null&&moderator.isNotEmpty) {
      query['moderator'] = moderator;
    }
      print("type coming--->${taskTypeEnum.name}");
    var response = await dio.get(urls[status] ?? "", queryParameters: query);
    if (response.statusCode == 200) {
      TaskModel taskModel = TaskModel.fromJson(response.data,taskTypeEnum);
      return taskModel;
    } else {
      throw DioException(
          requestOptions: response.requestOptions, response: response);
    }
  }
}
