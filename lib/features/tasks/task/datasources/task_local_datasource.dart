import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:isar/isar.dart';

abstract class TaskLocalDataSource {
  Future<List<TaskDocs>> getTasks(
      {required TaskCountEnumStatus status,
      required TaskTypeEnum taskTypeEnum,
      required int page,
      String? date,
      String? moderator});

  Future setNotifications(
      {required List<TaskDocs>? task,
      required TaskCountEnumStatus status,
      required TaskTypeEnum taskTypeEnum,
      required bool reset});
}

class TaskLocalDataSourceImpl extends TaskLocalDataSource {
  final IsarService isarService;

  TaskLocalDataSourceImpl({required this.isarService});

  @override
  Future<List<TaskDocs>> getTasks(
      {required TaskCountEnumStatus status,
      required TaskTypeEnum taskTypeEnum,
      required int page,
      String? date,
      String? moderator}) async {
    List<TaskDocs> list = await isarService.isar.taskDocs
        .filter()
        .typeEqualTo(taskTypeEnum)
        .statusEqualTo(statusToInt(status))
        .scheduledTimeIsNull()
        .sortByDateDesc()
        .findAll();
    return list;
  }

  @override
  Future setNotifications(
      {required List<TaskDocs>? task,
      required TaskCountEnumStatus status,
      required TaskTypeEnum taskTypeEnum,
      required bool reset}) async {
    if (reset) {
      await isarService.isar.writeTxn(() async {
        await isarService.isar.taskDocs
            .filter()
            .typeEqualTo(taskTypeEnum)
            .statusEqualTo(statusToInt(status))
            .scheduledTimeIsNull()
            .deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.taskDocs.putAll(task ?? []);
      });
    } else {
      await isarService.isar.writeTxn(() async {
        isarService.isar.taskDocs.putAll(task ?? []);
      });
    }
  }
}
