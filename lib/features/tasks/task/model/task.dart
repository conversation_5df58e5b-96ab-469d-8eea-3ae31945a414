import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:isar/isar.dart';

part 'task.g.dart';

class TaskModel {
  TaskModel({
    this.docs,
    this.totalDocs,
    this.limit,
    this.page,
    this.totalPages,
    this.pagingCounter,
    this.hasPrevPage,
    this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  TaskModel.fromJson(dynamic json, [TaskTypeEnum? taskTypeEnum]) {
    if (json['docs'] != null) {
      docs = [];
      json['docs'].forEach((v) {
        docs?.add(TaskDocs.fromJson(v, taskTypeEnum));
      });
    }
    totalDocs = json['totalDocs'];
    limit = json['limit'];
    page = json['page'];
    totalPages = json['totalPages'];
    pagingCounter = json['pagingCounter'];
    hasPrevPage = json['hasPrevPage'];
    hasNextPage = json['hasNextPage'];
    prevPage = json['prevPage'];
    nextPage = json['nextPage'];
  }

  List<TaskDocs>? docs;
  int? totalDocs;
  int? limit;
  int? page;
  int? totalPages;
  int? pagingCounter;
  bool? hasPrevPage;
  bool? hasNextPage;
  dynamic prevPage;
  int? nextPage;
}

@collection
@Name("task")
class TaskDocs {
  TaskDocs(
      {this.taskId,
      this.task,
      this.moderator,
      this.isRead,
      this.isDone,
      this.chats,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.date,
      this.startDate,
      this.endDate,
      this.doneDate,
      this.unRead,
      required this.type,
      this.scheduledTime,
      this.confirmed});

  TaskDocs.fromJson(dynamic json, [TaskTypeEnum? type]) {
    print("----------------------->${type}");
    taskId = json['_id'];
    moderator = json['moderator'] != null
        ? EmbeddedModerator.fromJson(json['moderator'])
        : null;
    task = json['task'] != null
        ? Task.fromJson(json['task'], moderator, type, taskId)
        : null;
    isRead = json['isRead'];
    isDone = json['isDone'];
    if (json['chats'] != null) {
      chats = [];
      json['chats'].forEach((v) {
        chats?.add(Chat.fromJson(v));
      });
    }
    status = json['status'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    date = json['date'];
    startDate = json['startDate'];
    endDate = json['endDate'];
    doneDate = json['doneDate'];
    unRead = json['unRead'];
    confirmed = json['confirmed'];
    this.type = type ?? TaskTypeEnum.late;
  }

  Id id = Isar.autoIncrement; // you can also use id = null to auto increment
  String? taskId;
  Task? task;
  EmbeddedModerator? moderator;
  bool? isRead;
  bool? isDone;
  List<Chat>? chats;
  int? status;
  String? createdAt;
  String? updatedAt;
  String? date;
  String? startDate;
  String? endDate;
  String? doneDate;
  int? unRead;
  @enumerated
  late TaskTypeEnum type;
  DateTime? scheduledTime;
  bool? confirmed;

  TaskDocs copyWith({
    String? taskId,
    Task? task,
    EmbeddedModerator? moderator,
    bool? isRead,
    bool? isDone,
    List<Chat>? chats,
    int? status,
    String? createdAt,
    String? updatedAt,
    String? date,
    String? startDate,
    String? endDate,
    String? doneDate,
    int? unRead,
    TaskTypeEnum? type,
    DateTime? scheduledTime,
    bool? confirmed
  }) {
    return TaskDocs(
      taskId: taskId ?? this.taskId,
      task: task ?? this.task,
      moderator: moderator ?? this.moderator,
      isRead: isRead ?? this.isRead,
      isDone: isDone ?? this.isDone,
      chats: chats ?? this.chats,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      date: date ?? this.date,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      doneDate: doneDate ?? this.doneDate,
      unRead: unRead ?? this.unRead,
      type: type ?? this.type,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      confirmed: confirmed??this.confirmed
    );
  }

  @override
  String toString() {
    return 'TaskDocs{task: $task, status: $status, type: $type}';
  }
}
