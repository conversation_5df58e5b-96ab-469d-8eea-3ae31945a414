import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:isar/isar.dart';

part 'task_count.g.dart';

@collection
@Name("task_count")
class TaskCount {
  TaskCount(
      {this.all,
      this.today,
      this.withinThreeDays,
      this.withinOneWeek,
      required this.status});

  TaskCount.fromJson({dynamic json, required TaskCountEnumStatus status}) {
    all = json['all'];
    today = json['today'];
    withinThreeDays = json['withinThreeDays'];
    withinOneWeek = json['withinOneWeek'];
    this.status = status;
  }

  Id id = Isar.autoIncrement; // you can also use id = null to auto increment
  int? all;
  int? today;
  int? withinThreeDays;
  int? withinOneWeek;
  @enumerated
  late TaskCountEnumStatus status;
}
