// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_count.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetTaskCountCollection on Isar {
  IsarCollection<TaskCount> get taskCounts => this.collection();
}

const TaskCountSchema = CollectionSchema(
  name: r'task_count',
  id: 6461875428467713876,
  properties: {
    r'all': PropertySchema(
      id: 0,
      name: r'all',
      type: IsarType.long,
    ),
    r'status': PropertySchema(
      id: 1,
      name: r'status',
      type: IsarType.byte,
      enumMap: _TaskCountstatusEnumValueMap,
    ),
    r'today': PropertySchema(
      id: 2,
      name: r'today',
      type: IsarType.long,
    ),
    r'withinOneWeek': PropertySchema(
      id: 3,
      name: r'withinOneWeek',
      type: IsarType.long,
    ),
    r'withinThreeDays': PropertySchema(
      id: 4,
      name: r'withinThreeDays',
      type: IsarType.long,
    )
  },
  estimateSize: _taskCountEstimateSize,
  serialize: _taskCountSerialize,
  deserialize: _taskCountDeserialize,
  deserializeProp: _taskCountDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _taskCountGetId,
  getLinks: _taskCountGetLinks,
  attach: _taskCountAttach,
  version: '3.1.0+1',
);

int _taskCountEstimateSize(
  TaskCount object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _taskCountSerialize(
  TaskCount object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.all);
  writer.writeByte(offsets[1], object.status.index);
  writer.writeLong(offsets[2], object.today);
  writer.writeLong(offsets[3], object.withinOneWeek);
  writer.writeLong(offsets[4], object.withinThreeDays);
}

TaskCount _taskCountDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = TaskCount(
    all: reader.readLongOrNull(offsets[0]),
    status: _TaskCountstatusValueEnumMap[reader.readByteOrNull(offsets[1])] ??
        TaskCountEnumStatus.news,
    today: reader.readLongOrNull(offsets[2]),
    withinOneWeek: reader.readLongOrNull(offsets[3]),
    withinThreeDays: reader.readLongOrNull(offsets[4]),
  );
  object.id = id;
  return object;
}

P _taskCountDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (_TaskCountstatusValueEnumMap[reader.readByteOrNull(offset)] ??
          TaskCountEnumStatus.news) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    case 3:
      return (reader.readLongOrNull(offset)) as P;
    case 4:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _TaskCountstatusEnumValueMap = {
  'news': 0,
  'progress': 1,
  'late': 2,
  'done': 3,
  'not_done': 4,
};
const _TaskCountstatusValueEnumMap = {
  0: TaskCountEnumStatus.news,
  1: TaskCountEnumStatus.progress,
  2: TaskCountEnumStatus.late,
  3: TaskCountEnumStatus.done,
  4: TaskCountEnumStatus.not_done,
};

Id _taskCountGetId(TaskCount object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _taskCountGetLinks(TaskCount object) {
  return [];
}

void _taskCountAttach(IsarCollection<dynamic> col, Id id, TaskCount object) {
  object.id = id;
}

extension TaskCountQueryWhereSort
    on QueryBuilder<TaskCount, TaskCount, QWhere> {
  QueryBuilder<TaskCount, TaskCount, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension TaskCountQueryWhere
    on QueryBuilder<TaskCount, TaskCount, QWhereClause> {
  QueryBuilder<TaskCount, TaskCount, QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterWhereClause> idGreaterThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterWhereClause> idLessThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension TaskCountQueryFilter
    on QueryBuilder<TaskCount, TaskCount, QFilterCondition> {
  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> allIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'all',
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> allIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'all',
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> allEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'all',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> allGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'all',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> allLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'all',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> allBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'all',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> statusEqualTo(
      TaskCountEnumStatus value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> statusGreaterThan(
    TaskCountEnumStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> statusLessThan(
    TaskCountEnumStatus value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> statusBetween(
    TaskCountEnumStatus lower,
    TaskCountEnumStatus upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> todayIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'today',
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> todayIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'today',
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> todayEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'today',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> todayGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'today',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> todayLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'today',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition> todayBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'today',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinOneWeekIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withinOneWeek',
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinOneWeekIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withinOneWeek',
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinOneWeekEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withinOneWeek',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinOneWeekGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'withinOneWeek',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinOneWeekLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'withinOneWeek',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinOneWeekBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'withinOneWeek',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinThreeDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withinThreeDays',
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinThreeDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withinThreeDays',
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinThreeDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withinThreeDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinThreeDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'withinThreeDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinThreeDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'withinThreeDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterFilterCondition>
      withinThreeDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'withinThreeDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension TaskCountQueryObject
    on QueryBuilder<TaskCount, TaskCount, QFilterCondition> {}

extension TaskCountQueryLinks
    on QueryBuilder<TaskCount, TaskCount, QFilterCondition> {}

extension TaskCountQuerySortBy on QueryBuilder<TaskCount, TaskCount, QSortBy> {
  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByAll() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'all', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByAllDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'all', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByToday() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'today', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByTodayDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'today', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByWithinOneWeek() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withinOneWeek', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByWithinOneWeekDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withinOneWeek', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByWithinThreeDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withinThreeDays', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> sortByWithinThreeDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withinThreeDays', Sort.desc);
    });
  }
}

extension TaskCountQuerySortThenBy
    on QueryBuilder<TaskCount, TaskCount, QSortThenBy> {
  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByAll() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'all', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByAllDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'all', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByToday() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'today', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByTodayDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'today', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByWithinOneWeek() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withinOneWeek', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByWithinOneWeekDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withinOneWeek', Sort.desc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByWithinThreeDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withinThreeDays', Sort.asc);
    });
  }

  QueryBuilder<TaskCount, TaskCount, QAfterSortBy> thenByWithinThreeDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withinThreeDays', Sort.desc);
    });
  }
}

extension TaskCountQueryWhereDistinct
    on QueryBuilder<TaskCount, TaskCount, QDistinct> {
  QueryBuilder<TaskCount, TaskCount, QDistinct> distinctByAll() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'all');
    });
  }

  QueryBuilder<TaskCount, TaskCount, QDistinct> distinctByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status');
    });
  }

  QueryBuilder<TaskCount, TaskCount, QDistinct> distinctByToday() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'today');
    });
  }

  QueryBuilder<TaskCount, TaskCount, QDistinct> distinctByWithinOneWeek() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withinOneWeek');
    });
  }

  QueryBuilder<TaskCount, TaskCount, QDistinct> distinctByWithinThreeDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withinThreeDays');
    });
  }
}

extension TaskCountQueryProperty
    on QueryBuilder<TaskCount, TaskCount, QQueryProperty> {
  QueryBuilder<TaskCount, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<TaskCount, int?, QQueryOperations> allProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'all');
    });
  }

  QueryBuilder<TaskCount, TaskCountEnumStatus, QQueryOperations>
      statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }

  QueryBuilder<TaskCount, int?, QQueryOperations> todayProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'today');
    });
  }

  QueryBuilder<TaskCount, int?, QQueryOperations> withinOneWeekProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withinOneWeek');
    });
  }

  QueryBuilder<TaskCount, int?, QQueryOperations> withinThreeDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withinThreeDays');
    });
  }
}
