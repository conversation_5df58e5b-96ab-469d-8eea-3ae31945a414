import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:isar/isar.dart';

abstract class TaskCountLocalDataSource {
  Future<TaskCount?> getTask(TaskCountEnumStatus status);

  Future<void> setTask(TaskCount taskCount);
}

class TaskCountLocalDataSourceImpl extends TaskCountLocalDataSource {
  final IsarService isarService;

  TaskCountLocalDataSourceImpl({required this.isarService});

  @override
  Future<TaskCount?> getTask(TaskCountEnumStatus status) async {
    final notificationCount =
        isarService.isar.taskCounts.filter().statusEqualTo(status).findFirst();
    return notificationCount;
  }

  @override
  Future<void> setTask(TaskCount taskCount) async {
    try {
      await isarService.isar.writeTxn(() async {
        await isarService.isar.taskCounts
            .filter()
            .statusEqualTo(taskCount.status)
            .deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        await isarService.isar.taskCounts.put(taskCount);
      });
    } catch (e) {
      throw Exception();
    }
  }
}
