import 'package:dio/dio.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';

abstract class TaskCountRemoteDataSource {
  Future<TaskCount> getTaskCount({required TaskCountEnumStatus status});
}

class TaskCountRemoteDataSourceImpl extends TaskCountRemoteDataSource {
  final Dio dio;

  TaskCountRemoteDataSourceImpl({required this.dio});

  @override
  Future<TaskCount> getTaskCount({required TaskCountEnumStatus status}) async {
    String path = status == TaskCountEnumStatus.news
        ? getNewTasksStatisticsPath
        : getProgressTasksStatisticsPath;

    var response = await dio.get(path);
    if (response.statusCode == 200) {
      return TaskCount.fromJson(status: status, json: response.data);
    } else {
      throw DioException(
          requestOptions: response.requestOptions, response: response);
    }
  }
}
