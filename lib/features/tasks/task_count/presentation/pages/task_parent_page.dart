import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/notifications/notification_count/presentation/pages/filter_page.dart';
import 'package:ijrochi/features/tasks/task/presentation/archive_parent_page.dart';
import 'package:ijrochi/features/tasks/task/presentation/bloc/task_bloc.dart';
import 'package:ijrochi/features/tasks/task/presentation/late_task_page.dart';
import 'package:ijrochi/features/tasks/task/presentation/task_page.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:ijrochi/features/tasks/task_count/presentation/task_bloc/task_count_bloc.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

class TaskParentPage extends StatefulWidget {
  final TaskCountEnumStatus status;
  final String title;

  static Widget screen(
      {required TaskCountEnumStatus status, required String title}) {
    return MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => di<TaskCountBloc>()),
          BlocProvider(create: (context) => di<TaskBloc>()),
        ],
        child: TaskParentPage(
          status: status,
          title: title,
        ));
  }

  const TaskParentPage({super.key, required this.status, required this.title});

  @override
  State<TaskParentPage> createState() => _TaskParentPageState();
}

class _TaskParentPageState extends State<TaskParentPage>
    with SingleTickerProviderStateMixin {
  late bool isDark;
  late TabController _tabController;
  List<Widget> pages = [];
  late TaskBloc taskBloc;
  late TaskCountBloc taskCountBloc;
  int selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(vsync: this, initialIndex: 0, length: 4);
    taskCountBloc = BlocProvider.of<TaskCountBloc>(context);
    taskBloc = BlocProvider.of<TaskBloc>(context);
    taskCountBloc.add(GetTaskCountEvent(status: widget.status));
    pages.add(TaskPage(
      status: widget.status,
      taskTypeEnum: TaskTypeEnum.all,
      bloc: taskBloc,
      taskCountBloc: taskCountBloc,
    ));
    pages.add(TaskPage(
      status: widget.status,
      taskTypeEnum: TaskTypeEnum.today,
      bloc: taskBloc,
      taskCountBloc: taskCountBloc,
    ));
    pages.add(TaskPage(
      status: widget.status,
      taskTypeEnum: TaskTypeEnum.three,
      bloc: taskBloc,
      taskCountBloc: taskCountBloc,
    ));
    pages.add(TaskPage(
      status: widget.status,
      taskTypeEnum: TaskTypeEnum.weeks,
      bloc: taskBloc,
      taskCountBloc: taskCountBloc,
    ));
  }

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
        appBar: AppBar(
            elevation: 2,
            title: Center(
              child: Column(
                children: [
                  Text(
                    widget.title,
                    style:
                        TextStyle(fontWeight: FontWeight.w500, fontSize: 16.sp),
                  ),
                ],
              ),
            ),
            actions: [
              InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () async {
                  showModalBottomSheet(
                      context: context,
                      backgroundColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                              topRight: Radius.circular(20.r),
                              topLeft: Radius.circular(20.r))),
                      builder: (BuildContext context) {
                        return FilterPage(
                          onFilterTap: (value) {
                            taskBloc.add(FilterEvent(
                                date: value.date,
                                moderator: value.moderator?.id,
                                taskTypeEnum: indexToType(_tabController.index),
                                status: widget.status,
                            filteredStatus: TaskStatus.filtered));
                          },
                        );
                      });
                },
                child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 15.w),
                    child: Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: SvgPicture.asset(
                        Assets.iconsFilter,
                        color: isDark ? cWhiteColor : cFirstColor,
                      ),
                    )),
              )
            ],
            bottom:(widget.status==TaskCountEnumStatus.news||widget.status==TaskCountEnumStatus.progress)==true?
            PreferredSize(
              preferredSize: Size.fromHeight(40.h),
              child: BlocConsumer<TaskCountBloc, TaskCountState>(
                listener: (context, state) {
                  // TODO: implement listener
                },
                builder: (context, state) {
                  if (state.status == TaskCountStatus.success) {
                    return TabBar(
                        isScrollable: true,
                        controller: _tabController,
                        tabs: [
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.all.tr()}(${state.taskCount?.all ?? 0})',
                              style: TextStyle(
                                  color: isDark ? cWhiteColor : cBlackColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.today.tr()}(${state.taskCount?.today ?? 0})',
                              style: TextStyle(
                                  color: fastTabColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.three_days.tr()}(${state.taskCount?.withinThreeDays ?? 0})',
                              style: TextStyle(
                                  color: simpleTabColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            height: 40.h,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            child: Text(
                              '${LocaleKeys.week.tr()}(${state.taskCount?.withinOneWeek ?? 0})',
                              style: TextStyle(
                                  color: importantTabColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                        ]);
                  } else if (state.status == TaskCountStatus.loading) {
                    return Shimmer(
                        color: cBackDarkColor2,
                        child: Container(
                          height: 40.h,
                          color: isDark
                              ? cBackDarkColor2
                              : cGrayColor.withOpacity(.2),
                        ),
                        interval: Duration(seconds: 5));
                  } else if (state.status == TaskCountStatus.failure) {
                    return Text(state.message.toString());
                  } else {
                    return SizedBox(
                      height: 40.h,
                    );
                  }
                },
              ),
            ):null),
        body: Stack(
          children: [
            TabBarView(
              children: pages,
              controller: _tabController,
            ),
            Positioned(
              bottom: 24.h,
              right: 50.h,
              left: 50.h,
              child: Material(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24.r)),
                elevation: 20.w,
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: 52.h,
                  decoration: BoxDecoration(
                    color: isDark ? cFirstColorDark : cWhiteColor,
                    borderRadius: BorderRadius.circular(24.r),
                    border: Border.all(
                        width: 1, color: isDark ? cWhiteColor : cBlackColor),
                  ),
                  child: Row(children: [
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              menuSelect(0);
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        TaskParentPage.screen(
                                            status: TaskCountEnumStatus.news,
                                            title: LocaleKeys.task_new.tr()),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.add_chart,
                              color: iconColorChange(
                                  widget.status, TaskCountEnumStatus.news),
                              size: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              menuSelect(1);
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        TaskParentPage.screen(
                                            status:
                                                TaskCountEnumStatus.progress,
                                            title:
                                                LocaleKeys.task_process.tr()),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: SvgPicture.asset(
                              Assets.iconsIcTaskProcess,
                              color: iconColorChange(
                                  widget.status, TaskCountEnumStatus.progress),
                              width: 24.w,
                              height: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              menuSelect(2);
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        LateTaskPage.screen(),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.error_outline,
                              color: iconColorChange(
                                  widget.status, TaskCountEnumStatus.late),
                              size: 24.w,
                            ))),
                    Expanded(
                        child: IconButton(
                            onPressed: () {
                              menuSelect(3);
                              Navigator.pushReplacement(
                                  context,
                                  PageRouteBuilder(
                                    pageBuilder: (BuildContext context,
                                            Animation<double> animation,
                                            Animation<double>
                                                secondaryAnimation) =>
                                        ArchiveParentTaskPage.screen(),
                                    transitionDuration:
                                        Duration(milliseconds: 500),
                                    reverseTransitionDuration:
                                        Duration(milliseconds: 500),
                                  ));
                            },
                            icon: Icon(
                              Icons.archive_outlined,
                              color: iconColorChange(
                                  widget.status, TaskCountEnumStatus.done),
                              size: 24.w,
                            ))),
                  ]),
                ),
              ),
            ),
          ],
        )
    );
  }

  Color iconColorChange(
      TaskCountEnumStatus status, TaskCountEnumStatus selectedStatus) {
    if (status == selectedStatus) {
      return isDark ? cBlueLight : cFirstColor;
    } else {
      return isDark ? cWhiteColor : cBlackColor;
    }
  }

  menuSelect(int tabIndex) {
    setState(() {
      selectedTabIndex = tabIndex;
    });
  }
}
