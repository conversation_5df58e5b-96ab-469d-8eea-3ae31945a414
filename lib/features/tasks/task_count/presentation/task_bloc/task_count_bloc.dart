import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/features/tasks/task_count/datasource/task_count_local_datasource.dart';
import 'package:ijrochi/features/tasks/task_count/datasource/task_count_remote_datasource.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

part 'task_count_event.dart';

part 'task_count_state.dart';

class TaskCountBloc extends Bloc<TaskCountEvent, TaskCountState> {
  final TaskCountLocalDataSource taskCountLocalDataSource;
  final TaskCountRemoteDataSource taskCountRemoteDataSource;
  final NetworkInfo networkInfo;

  TaskCountBloc(
      {required this.taskCountLocalDataSource,
      required this.taskCountRemoteDataSource,
      required this.networkInfo})
      : super(TaskCountState.initial()) {
    on<GetTaskCountEvent>(getTaskCounts);
  }

  FutureOr<void> getTaskCounts(
      GetTaskCountEvent event, Emitter<TaskCountState> emit) async {
    if (await networkInfo.isConnected) {
      emit(state.copyWith(status: TaskCountStatus.loading));
      try {
        TaskCount taskCount =
            await taskCountRemoteDataSource.getTaskCount(status: event.status);
        taskCountLocalDataSource.setTask(taskCount);
        emit(state.copyWith(
            status: TaskCountStatus.success, TaskCount: taskCount));
      } on DioException catch (e) {
        emit(state.copyWith(
            status: TaskCountStatus.failure, message: LocaleKeys.error.tr()));
      } catch (e) {
        emit(state.copyWith(
            status: TaskCountStatus.failure, message: LocaleKeys.error.tr()));
      }
    } else {
      try {
        TaskCount? taskCount =
            await taskCountLocalDataSource.getTask(event.status);
        emit(state.copyWith(
            status: TaskCountStatus.success, TaskCount: taskCount));
      } catch (e) {
        emit(state.copyWith(
            status: TaskCountStatus.noInternet,
            message: LocaleKeys.no_internet.tr()));
      }
    }
  }
}
