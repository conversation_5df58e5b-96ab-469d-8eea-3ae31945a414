part of 'task_count_bloc.dart';

enum TaskCountStatus { initial, loading, failure, success,noInternet}

class TaskCountState extends Equatable {
  final TaskCountStatus status;
  final String? message;
  final TaskCount? taskCount;

  TaskCountState(
      {required this.status, this.message, this.taskCount});

  static TaskCountState initial() =>
      TaskCountState(status: TaskCountStatus.initial);

  TaskCountState copyWith({TaskCountStatus? status, String? message,
    TaskCount? TaskCount}) =>
      TaskCountState(
          status: status ?? this.status,
          message: message ?? this.message,
          taskCount: TaskCount ?? this.taskCount);

  @override
  List<Object?> get props => [status, message, TaskCount];
}
