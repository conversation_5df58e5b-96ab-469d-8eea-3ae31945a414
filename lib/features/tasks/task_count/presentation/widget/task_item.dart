import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:html/parser.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:intl/intl.dart';

class TaskItem extends StatefulWidget {
  final TaskDocs taskDocs;
  final bool visible;
  final Function(TaskDocs taskDocs) onTapItem;

  const TaskItem(
      {Key? key,
      required this.taskDocs,
      required this.visible,
      required this.onTapItem})
      : super(key: key);

  @override
  State<TaskItem> createState() => _TaskItemState();
}

class _TaskItemState extends State<TaskItem> {
  final DateFormat formatterHour = DateFormat('HH:mm');
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  late bool isDark;

  @override
  Widget build(BuildContext context) {
    isDark = Theme.of(context).brightness == Brightness.dark;
    return InkWell(
      onTap: () {
        widget.onTapItem(widget.taskDocs);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Visibility(
            visible: widget.visible,
            child: Container(
              margin: EdgeInsets.only(top: 10.h),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: isDark ? cDateBackgroundDark : cDateBackgroundLight,
                  borderRadius: BorderRadius.circular(30.r)),
              width: 100.w,
              height: 26.h,
              child: Text(
                formatterDate
                    .format(DateTime.parse(widget.taskDocs.date.toString())),
                style: TextStyle(
                    color: themeIdentify(context) ? cWhiteColor : cBlackColor,
                    fontSize: 14.sp),
              ),
            ),
          ),
          SizedBox(
            height: 2.h,
          ),
          Card(
            margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
            elevation: 2,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r)),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: themeIdentify(context) ? cBackDarkColor2 : cWhiteColor,
              ),
              padding: EdgeInsets.symmetric(vertical: 12.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 15.w, top: 6.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 5.h, horizontal: 10.w),
                          decoration: BoxDecoration(
                              color: cFirstColor,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(15.r))),
                          child: Text(
                            '${LocaleKeys.task_push_title.tr()} № ${widget.taskDocs.task?.serialNumber}',
                            style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(right: 15.w),
                          child: Text(
                            formatterHour.format(
                                DateTime.parse(widget.taskDocs.date ?? "")),
                            style: TextStyle(
                              color: cGrayTextColor,
                              fontSize: 13.sp,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 15.w, top: 12.sp, right: 15.w),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${LocaleKeys.term.tr()}:' +
                                ' ${formatterDate.format(DateTime.parse(widget.taskDocs.endDate ?? "1970-00-00"))} (${duration1(DateTime.parse(widget.taskDocs.date ?? "1970-00-00"), DateTime.parse(widget.taskDocs.endDate ?? "1970-00-00"))} ' +
                                '${LocaleKeys.day.tr()}' +
                                ')',
                            style: TextStyle(
                                fontSize: 14.sp, color: cGrayTextColor),
                          ),
                          Row(
                            children: [
                              SvgPicture.asset(
                                Assets.iconsCounterRefresh,
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              Container(
                                padding: EdgeInsets.all(5.h),
                                decoration: BoxDecoration(
                                    border: Border.all(),
                                    borderRadius: BorderRadius.circular(8.r)),
                                child: Text(
                                  "${duration2(DateTime.parse(widget.taskDocs.endDate ?? "1970-00-00"))}" +
                                      ' ' +
                                      '${LocaleKeys.day.tr()}',
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      fontSize: 12.sp,
                                      color: duration2(DateTime.parse(
                                                  widget.taskDocs.endDate ??
                                                      "1970-00-00")) >=
                                              0
                                          ? themeIdentify(context)
                                              ? cWhiteColor
                                              : cBlackColor
                                          : cRedColor,
                                      overflow: TextOverflow.ellipsis),
                                ),
                              ),
                            ],
                          ),
                        ]),
                  ),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${LocaleKeys.send_organisation.tr()}:',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: cGrayTextColor,
                          ),
                        ),
                        Flexible(
                          child: Text(
                            "${widget.taskDocs.moderator?.fullName}",
                            style: TextStyle(
                              fontSize: 13.sp,
                            ),
                            textAlign: TextAlign.right,
                          ),
                        )
                      ],
                    ),
                  ),
                  Visibility(
                    visible: widget.taskDocs.task?.documentType?.title != null,
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Hujjat turi:',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: cGrayTextColor,
                            ),
                          ),
                          Flexible(
                            child: Text(
                              "${widget.taskDocs.task?.documentType?.title ?? '-'}",
                              style: TextStyle(
                                fontSize: 13.sp,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Visibility(
                    visible: widget.taskDocs.task?.documentDate != null,
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Hujjat sanasi:',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: cGrayTextColor,
                            ),
                          ),
                          Flexible(
                            child: Text(
                              "${widget.taskDocs.task?.documentDate ?? '-'}",
                              style: TextStyle(
                                fontSize: 13.sp,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Visibility(
                    visible:
                        widget.taskDocs.task?.documentNumber?.title != null,
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Hujjat raqami:',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: cGrayTextColor,
                            ),
                          ),
                          Flexible(
                            child: Text(
                              "${widget.taskDocs.task?.documentNumber?.title ?? '-'}",
                              style: TextStyle(
                                fontSize: 13.sp,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Visibility(
                    visible: widget.taskDocs.task?.headNumber != null,
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Band raqami:',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: cGrayTextColor,
                            ),
                          ),
                          Flexible(
                            child: Text(
                              "${widget.taskDocs.task?.headNumber ?? '-'}",
                              style: TextStyle(
                                fontSize: 13.sp,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: cGrayColor,
                    indent: 14.w,
                    endIndent: 14.w,
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 16.w, top: 10.h),
                    child: Text(
                      widget.taskDocs.task?.title ?? LocaleKeys.unknown.tr(),
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: isDark ? cWhiteColor : cBlackColor,
                          fontSize: 16.sp),
                    ),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Padding(
                      padding: EdgeInsets.only(left: 16.w, right: 16.w),
                      child: Text(
                        parse(widget.taskDocs.task?.content ??
                                    "<html lang='en'>...</html>")
                                .documentElement
                                ?.text ??
                            "",
                        style: TextStyle(
                            fontSize: 14.sp,
                            overflow: TextOverflow.ellipsis,
                            color: cGrayTextColor),
                        maxLines: 3,
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  int duration1(DateTime startDate, DateTime endDate) {
    Duration difference = endDate.difference(startDate);
    return difference.inDays;
  }

  int duration2(DateTime endDate) {
    Duration difference2 = endDate.difference(DateTime.now());
    int lastCount = difference2.inDays;
    return lastCount;
  }

  Color colorChanger(String? color, bool isDark) {
    if (color == null) {
      return themeIdentify(context) ? cWhiteColor : cBlackColor;
    } else {
      return HexColor.fromHex(color);
    }
  }
}
