import 'dart:async';
import 'dart:io';

import 'package:auto_start_flutter/auto_start_flutter.dart';
import 'package:context_holder/context_holder.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/utils/theme_manager.dart';
import 'package:ijrochi/core/utils/theme.dart';
import 'package:ijrochi/core/widgets/dialog_frame.dart';
import 'package:ijrochi/push_notifications/app_killed_services.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:socket_io_client/socket_io_client.dart';
import 'package:upgrader/upgrader.dart';

import '../di/dependency_injection.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:http/http.dart' as http;
import 'package:theme_mode_handler/theme_mode_handler.dart';
import '../generated/assets.dart';
import 'desktop_app.dart';

bool shouldLockStatic = true;

class AppProvider extends StatefulWidget {
  AppProvider({Key? key}) : super(key: key);

  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  @override
  State<AppProvider> createState() => _AppProviderState();
}

class _AppProviderState extends State<AppProvider> with WidgetsBindingObserver {
  final SharedPreferences prefs = di();
  final GetStorage getStorage = di();
  final Dio dio = di();
  final http.Client client = di();
  Upgrader? upgrader = di();
  dynamic homeFunc;

  Timer? _timer;

  @override
  void initState() {
    super.initState();

    ///For Desktop
    if (!Platform.isAndroid || !Platform.isIOS) {
      initSystemTray(_timer);
    }

    print("ID: " + prefs.getString(ID).toString());
    print("ServerId:" + prefs.getString("server_id").toString());

    WidgetsBinding.instance.addObserver(this);
    homeFunc = getInitialPage();
    createFolderInAppDocDir();
    print("BEARER TOKEN: ${getStorage.read(BEARER_TOKEN)}");
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
  }
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
    } else if (state == AppLifecycleState.inactive) {
    }
  }
  identifyPhoneThemeAndApply(BuildContext context) {
    var brightness = MediaQuery.of(context).platformBrightness;
    bool isDarkMode = brightness == Brightness.dark;
    if (isDarkMode) {
      ThemeModeHandler.of(context)?.saveThemeMode(ThemeMode.dark);
    } else {
      ThemeModeHandler.of(context)?.saveThemeMode(ThemeMode.light);
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return ScreenUtilInit(
      splitScreenMode: true,
      builder: (BuildContext context, child) => ThemeModeHandler(
        manager: MyManager(),
        builder: (ThemeMode themeMode) {
          SystemChrome.setSystemUIOverlayStyle(themeMode == ThemeMode.light
              ? SystemUiOverlayStyle.dark
              : SystemUiOverlayStyle.light);

          return MaterialApp(
              builder: (context, widget) {
                ScreenUtil.init(
                  context,
                  designSize: const Size(375, 812),
                  minTextAdapt: true,
                );
                return widget ?? Center(child: Text('Error!'));
              },
              debugShowCheckedModeBanner: false,
              // localizationsDelegates: context.localizationDelegates,
              // supportedLocales: context.supportedLocales,
              // locale: context.locale,
              // initialRoute: '/',
              home: Column(
                children: [
                  Platform.isWindows ? TitleBar() : Container(),
                  Expanded(
                    child: MaterialApp(
                      theme: IjrochiTheme.lightTheme,
                      darkTheme: IjrochiTheme.darkTheme,
                      color: Colors.transparent,
                      themeMode: themeMode,
                      navigatorKey: ContextHolder.key,
                      debugShowCheckedModeBanner: false,
                      localizationsDelegates: context.localizationDelegates,
                      supportedLocales: context.supportedLocales,
                      locale: context.locale,
                      home: UpgradeAlert(
                        child: FutureBuilder<Widget>(
                          future: homeFunc, // async work
                          builder: (BuildContext context,
                              AsyncSnapshot<Widget> snapshot) {
                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                {
                                  if (Platform.isAndroid | Platform.isIOS) {
                                    ///Autostart handler
                                    autoStartWall(context);
                                  }
                        
                                  return Loading();
                                }
                              default:
                                if (snapshot.hasError)
                                  return setInitialPageToHomePage();
                                else
                                  return snapshot.data ??
                                      setInitialPageToHomePage();
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ));

          // RingingPageTask(audioPlayer: AudioPlayer(), receivedNotification: ReceivedNotification();
        },
      ),
    );
  }

  void autoStartWall(BuildContext context) async {
    //check auto-start availability.
    var isAvailable = await (isAutoStartAvailable) ?? false;
    var isAutostartShown = prefs.getBool('isAutostartShown') ?? false;
    print('=============== Is autostart available: $isAvailable');
    //if available then navigate to auto-start setting page.

    if (isAvailable && !isAutostartShown) {
      WidgetsBinding.instance.addPostFrameCallback((time) {
        showDialog(
            context: context,
            builder: (context) {
              return AllDialogSkeleton(
                  child: Center(
                    child: Column(children: [
                      SizedBox(
                        height: 20.h,
                      ),
                      Text(
                        LocaleKeys.autoWallDesc.tr(),
                        style: TextStyle(fontSize: 20.sp),
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      MaterialButton(
                          child: Text(
                            LocaleKeys.openSettings.tr(),
                            style: TextStyle(color: cWhiteColor),
                          ),
                          color: cFirstColor,
                          onPressed: () async {
                            Navigator.of(context).pop();
                            await getAutoStartPermission();
                          })
                    ]),
                  ),
                  title: LocaleKeys.warning1.tr(),
                  icon: Assets.iconsRound,
                  iconColor: cFirstColor);
            });
        prefs.setBool('isAutostartShown', true);
      });
    }
  }
}
