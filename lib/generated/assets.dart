///This file is automatically generated. DO NOT EDIT, all your changes would be lost.
class Assets {
  Assets._();

  static const String assetsAppIcon = 'assets/app_icon.ico';
  static const String assetsIconsIcDarkMode = 'assets/icons/ic_dark_mode.xml';
  static const String assetsRingtone = 'assets/ringtone.m4a';
  static const String fontsAliceRegular = 'assets/fonts/Alice-Regular.ttf';
  static const String fontsInterBold = 'assets/fonts/Inter-Bold.ttf';
  static const String fontsInterMedium = 'assets/fonts/Inter-Medium.ttf';
  static const String fontsInterRegular = 'assets/fonts/Inter-Regular.ttf';
  static const String fontsInterSemiBold = 'assets/fonts/Inter-SemiBold.ttf';
  static const String fontsNunitoSansRegular = 'assets/fonts/NunitoSans-Regular.ttf';
  static const String fontsOpenSansRegular = 'assets/fonts/OpenSans-Regular.ttf';
  static const String fontsSansSerifMedium = 'assets/fonts/Sans-Serif-Medium.ttf';
  static const String fontsTimesNewRoman = 'assets/fonts/times_new_roman.ttf';
  static const String iconsActive = 'assets/icons/active.svg';
  static const String iconsAddAlert = 'assets/icons/add_alert.svg';
  static const String iconsArchives = 'assets/icons/archives.svg';
  static const String iconsAvatar = 'assets/icons/avatar.svg';
  static const String iconsBell = 'assets/icons/bell.svg';
  static const String iconsBiometrics = 'assets/icons/biometrics.png';
  static const String iconsCamera = 'assets/icons/camera.svg';
  static const String iconsConnects = 'assets/icons/connects.svg';
  static const String iconsContacts = 'assets/icons/contacts.svg';
  static const String iconsCounterRefresh = 'assets/icons/counter_refresh.svg';
  static const String iconsDocument = 'assets/icons/document.svg';
  static const String iconsDotOutlined = 'assets/icons/dot_outlined.svg';
  static const String iconsDownload = 'assets/icons/download.svg';
  static const String iconsEmpty = 'assets/icons/empty.png';
  static const String iconsExit = 'assets/icons/exit.svg';
  static const String iconsFile = 'assets/icons/file.svg';
  static const String iconsFilter = 'assets/icons/filter.svg';
  static const String iconsFingerScan = 'assets/icons/finger-scan.svg';
  static const String iconsGallery = 'assets/icons/gallery.svg';
  static const String iconsGlobus = 'assets/icons/globus.svg';
  static const String iconsHome = 'assets/icons/home.svg';
  static const String iconsHumburgerMenu = 'assets/icons/humburger_menu.svg';
  static const String iconsIcContacts = 'assets/icons/ic_contacts.svg';
  static const String iconsIcDarkMode = 'assets/icons/ic_dark_mode.svg';
  static const String iconsIcHome = 'assets/icons/ic_home.svg';
  static const String iconsIcLanguage = 'assets/icons/ic_language.svg';
  static const String iconsIcLightMode = 'assets/icons/ic_light_mode.svg';
  static const String iconsIcPhone = 'assets/icons/ic_phone.svg';
  static const String iconsIcTaskNewIllustriation = 'assets/icons/ic_task_new_illustriation.svg';
  static const String iconsIcTaskProcess = 'assets/icons/ic_task_process.svg';
  static const String iconsMicrophone = 'assets/icons/microphone.svg';
  static const String iconsNewIcon = 'assets/icons/new_icon.svg';
  static const String iconsNight = 'assets/icons/night.svg';
  static const String iconsPerson = 'assets/icons/person.svg';
  static const String iconsRound = 'assets/icons/round.svg';
  static const String iconsRoute = 'assets/icons/route.svg';
  static const String iconsSend = 'assets/icons/send.svg';
  static const String iconsSettings = 'assets/icons/settings.svg';
  static const String iconsShare = 'assets/icons/share.svg';
  static const String iconsTickCircle = 'assets/icons/tick-circle.svg';
  static const String iconsTime = 'assets/icons/time.svg';
  static const String iconsX = 'assets/icons/x.svg';
  static const String ijrochiMobileAssetsAppIcon = 'assets/app_icon.png';
  static const String imagesArchives = 'assets/images/archives.png';
  static const String imagesBackgroundPic = 'assets/images/background_pic.png';
  static const String imagesBrowser = 'assets/images/browser.png';
  static const String imagesBrowsers = 'assets/images/browsers.png';
  static const String imagesClickLogo = 'assets/images/click_logo.png';
  static const String imagesEmptyBox = 'assets/images/empty_box.png';
  static const String imagesHomeTopBackground = 'assets/images/home_top_background.png';
  static const String imagesIntroImage = 'assets/images/intro_image.png';
  static const String imagesLockPattern = 'assets/images/lock-pattern.png';
  static const String imagesLogo = 'assets/images/logo.png';
  static const String imagesLogoAPI31 = 'assets/images/logoAPI31.png';
  static const String imagesNoConnection = 'assets/images/no_connection.png';
  static const String imagesPaymeLogo = 'assets/images/payme_logo.png';
  static const String imagesPaymeLogoActive = 'assets/images/payme_logo_active.png';
  static const String imagesPhoneImage = 'assets/images/phone_image.png';
  static const String imagesSammyMessageSent = 'assets/images/sammy_message_sent.png';
  static const String imagesTwitterLogo = 'assets/images/twitter-logo.png';
  static const String translationsRu = 'assets/translations/ru.json';
  static const String translationsUzCyrillic = 'assets/translations/uz-cyrillic.json';
  static const String translationsUzLatin = 'assets/translations/uz-latin.json';

}
