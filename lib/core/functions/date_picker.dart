import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

import 'functions.dart';

void showDateTimePicker(BuildContext ctx,
    {required onPressedOK(DateTime val)}) {
  DateTime selected = DateTime.now();
  // bool isDark = Theme.of(ctx).brightness == Brightness.dark;

  // showCupertinoModalPopup is a built-in function of the cupertino library
  showModalBottomSheet(
      backgroundColor: isDark() ? cFirstColorDark : cWhiteColor,
      showDragHandle: true,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(20.r), topLeft: Radius.circular(20.r))),
      context: ctx,
      builder: (_) => Container(
            height: 350.h,
            decoration: BoxDecoration(
              color: isDark() ? cFirstColorDark : cWhiteColor,
              // borderRadius: BorderRadius.circular(20.0),
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                      padding: EdgeInsets.symmetric(vertical: 20.h),
                      child: Material(
                          color: isDark() ? cFirstColorDark : cWhiteColor,
                          child: Text(
                            LocaleKeys.add_reminder.tr(),
                            style: TextStyle(fontSize: 20.sp),
                          ))),
                  SizedBox(
                    height: 200.h,
                    child: CupertinoTheme(
                      data: CupertinoThemeData(
                        textTheme: CupertinoTextThemeData(
                          dateTimePickerTextStyle: TextStyle(
                            color: isDark()
                                ? cWhiteColor
                                : cBlackColor, // Change this to your desired color
                          ),
                        ),
                      ),
                      child: CupertinoDatePicker(
                          use24hFormat: true,
                          minimumDate:
                              DateTime.now().add(const Duration(minutes: 1)),
                          initialDateTime:
                              DateTime.now().add(const Duration(minutes: 1)),
                          onDateTimeChanged: (val) {
                            selected = val;
                          }),
                    ),
                  ),

                  // Close the modal
                  CupertinoButton(
                      child: Text(
                        'OK',
                        style: TextStyle(color:isDark() ? cWhiteColor : cBlackColor,fontWeight: FontWeight.bold),
                      ),
                      onPressed: () {
                        if (selected.isAfter(DateTime.now()))
                          return onPressedOK(selected);
                        else {
                          CustomToast.showToast(
                              LocaleKeys.selectLongPeriod.tr());
                        }
                      })
                ],
              ),
            ),
          ));
}
