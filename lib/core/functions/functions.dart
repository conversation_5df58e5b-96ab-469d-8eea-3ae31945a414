import 'dart:convert';
import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http_parser/http_parser.dart';
import 'package:ijrochi/core/database/isar_service.dart';
import 'package:ijrochi/core/network/network_info.dart';
import 'package:ijrochi/core/utils/api_path.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/core/widgets/custom_toast.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/login/presentation/pages/login_page.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/download_chat_file_cubit/download_chat_file_bloc.dart';
import 'package:ijrochi/features/notification_detail/presentation/bloc/upload_dialog_bloc/upload_dialog_cubit.dart';
import 'package:ijrochi/features/notification_detail/widgets/progress_dialog.dart';
import 'package:ijrochi/features/notifications/notification/model/moderator.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/notifications/notification/model/notification_enum.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count_enum_status.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:ijrochi/generated/assets.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

import 'package:isar/isar.dart';
import 'package:mime/mime.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:platform_device_id_plus/platform_device_id.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:pdf/widgets.dart' as pw;
import 'package:url_launcher/url_launcher.dart';
import 'package:path/path.dart' as path;

import '../../back_service.dart';

String showCommentName(String? type, String original_url, String text) {
  if (type == "FILE") {
    try {
      return original_url.split("/").last;
    } catch (e) {
      return text;
    }
  } else {
    return text;
  }
}

getPath() async {
  if (Platform.isIOS || Platform.isWindows) {
    final Directory? downloadsDir = await getApplicationDocumentsDirectory();
    return downloadsDir?.path;
  } else {
    return androidDownloadPath;
  }
}

void setLanguage(String lang) async {
  SharedPreferences prefs = di();
  await prefs.setString(language_pref, lang);
}

bool lazyTasks(BuildContext context) {
  ///Set user settings and reset languages
  context.setLocale(Locale('uz', 'latin'));
  setLanguage('uz');
  if (Platform.isAndroid | Platform.isIOS) {
    // FlutterBackgroundService().invoke("stopService");
  }
  Phoenix.rebirth(context);
  return false;
}

clearAndLogout(BuildContext context) async {
  SharedPreferences prefs = di();
  GetStorage getStorage = di();

  try {

    prefs.clear().then((value) async {
      ///Clear and get token again
    });
    getStorage.erase();

    ///Clear all dbs
    var isar = await Isar.getInstance();
    if (isar != null) {
      isar.writeTxnSync(() => isar.clearSync());
    }

    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (BuildContext context) => LoginPage.screen(),
      ),
      (Route route) => lazyTasks(context),
    );
  } catch (e) {
    print(e);
  }
}

Future<void> updateFirebaseToken() async {
  final Dio dio = di();
  final SharedPreferences prefs = di();
  String firebaseToken = await prefs.getString(firebaseTokenKEY) ?? "";
  try {
    var response =
        await dio.post(updateFirebaseTokenPath, data: {"token": firebaseToken});
    if (response.statusCode == 201 || response.statusCode == 200) {
      print("Updates");
    } else {
      print("Not updates");
    }
  } catch (e) {
    print("Not updates:$e");
  }
}

///Copy after auth success
Future<void> sendEnteredStatus() async {
  SharedPreferences prefs = di();
  final http.Client client = di();
  var body = {
    'token': await prefs.getString('mobile_token'),
  };
  try {
    var response = await client.post(Uri.parse(baseUrl + enteredStatusPath),
        headers: <String, String>{
          'Content-Type': 'application/json',
          'lang': '${prefs.getString(language_pref)}',
          'Authorization': 'Bearer ' + '${prefs.getString('mobile_token')}'
        },
        body: jsonEncode(body));
    if (response.statusCode == 505 &&
        json.decode(response.body)['code'] == 401) {
      prefs.setString('id', '');
    }
    print("Send entered status ===>" + response.body);
  } catch (e) {
    print(e);
  }
}

Future<void> sendOnlineStatus() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  Dio dio = Dio();
  var body = {
    'token': await prefs.getString(BEARER_TOKEN),
  };

  print( await prefs.getString(BEARER_TOKEN));
  try {
    var response = await dio.post(baseUrl + onlineStatusPath,
        data: body,
        options: Options(headers: {
          'Content-Type': 'application/json',
          'lang': '${prefs.getString(language_pref)}',
          'Authorization': 'Bearer ' + '${prefs.getString(BEARER_TOKEN)}'
        }));
  } catch (e) {
    print(e);
  }
}

Future<void> sendOfflineStatus() async {
  SharedPreferences prefs = di();
  final http.Client client = di();
  var body = {
    'token': await prefs.getString(BEARER_TOKEN),
  };
  try {
    var response = await client.post(Uri.parse(baseUrl + offlineStatusPath),
        headers: <String, String>{
          'Content-Type': 'application/json',
          'lang': '${prefs.getString(language_pref)}',
          'Authorization': 'Bearer ' + '${prefs.getString(BEARER_TOKEN)}'
        },
        body: jsonEncode(body));

    print("Send offline status ===>" + response.body);
  } catch (e) {
    print(e);
  }
}

Future<void> testNotification() async {
  SharedPreferences prefs = di();
  final http.Client client = di();
  var token = prefs.getString(firebaseTokenKEY).toString();

  var body = {
    "to": token,
    "content_available": true,
    "apns-priority": 5,
    "data": {
      "title": "Never gonna give you up",
      "body": "Never gonna let you down"
    }
  };

  try {
    var response =
        await client.post(Uri.parse("https://fcm.googleapis.com/fcm/send"),
            headers: <String, String>{
              'Content-Type': 'application/json',
              'Accept': '*/*',
              'Authorization':
                  "key=AAAAdAjQsGM:APA91bFJJdNj8CKJpiAf1JBM9CO2FQ6c7jkIBmSEnV_xA6O0_3rgvj55mKIS9kEpghL23gfn94Z3QhC7LeBUtNrvZvrfHNiYzXAJ3uuKcBdTuC40YaUvV_6lx87gEXjwf5qArdQowqRt"
            },
            body: jsonEncode(body));

    print("Notification sent! Response ===> " + response.body);
  } catch (e) {
    print(e);
  }
}

themeIdentify(BuildContext context) {
  return Theme.of(context).brightness == Brightness.dark;
}

Future<String> getSelectedLanguage() async {
  final SharedPreferences sharedPreferences = di();
  String lang = await sharedPreferences.getString(language_pref) ?? 'uz';
  switch (lang) {
    case 'uz':
      {
        return 'O\'zbekcha';
      }
    case 'ru':
      {
        return 'Русский';
      }
    case 'cr':
      {
        return 'Ўзбекча';
      }
    default:
      {
        return 'O\'zbekcha';
      }
  }
}

Future<Map<Permission, PermissionStatus>> requestPermissionsForFile() async {
  AndroidDeviceInfo androidInfo = di();
  if (Platform.isAndroid && androidInfo.version.sdkInt <= 33) {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
    ].request();
    return statuses;
  } else {
    return {};
  }
}

Future<Map<Permission, PermissionStatus>> requestLocation() async {
  Map<Permission, PermissionStatus> statuses = await [
    Permission.location,
  ].request();
  return statuses;
}

Future<Map<Permission, PermissionStatus>> requestPermissionsForCamera() async {
  Map<Permission, PermissionStatus> statuses = await [
    Permission.camera,
  ].request();
  return statuses;
}

Future<Map<Permission, PermissionStatus>>
    requestPermissionsForMicrophone() async {
  Map<Permission, PermissionStatus> statuses =
      await [Permission.microphone].request();
  return statuses;
}

Future<String> titleMakerNotification() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String lang = prefs.getString(language_pref) ?? 'uz';
  if (lang == 'uz') {
    return "Xabarnoma";
  } else if (lang == "ru") {
    return "Уведомления";
  } else {
    return "Хабарномалар";
  }
}

Future<String> titleMakerTask() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String lang = prefs.getString(language_pref) ?? 'uz';
  if (lang == 'uz') {
    return "Vazifa";
  } else if (lang == "ru") {
    return "Задача";
  } else {
    return "Вазифа";
  }
}

Future<String> titleMakerResponse() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String lang = prefs.getString(language_pref) ?? 'uz';
  if (lang == 'uz') {
    return "Eslatma";
  } else if (lang == "ru") {
    return "Напоминание";
  } else {
    return "Эслатма";
  }
}

Future<String> getDeviceName() async {
  String? deviceName;
  try {
    final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo buildAndroid = await deviceInfoPlugin.androidInfo;
      return deviceName = buildAndroid.model ?? 'unknown';
    } else if (Platform.isIOS) {
      IosDeviceInfo buildIos = await deviceInfoPlugin.iosInfo;
      return deviceName = buildIos.name ?? 'unknown';
    } else if (Platform.isWindows) {
      WindowsDeviceInfo buildWindows = await deviceInfoPlugin.windowsInfo;
      return deviceName = buildWindows.productName ?? 'unknown';
    } else if (Platform.isMacOS) {
      MacOsDeviceInfo buildMacOs = await deviceInfoPlugin.macOsInfo;
      return deviceName = buildMacOs.model ?? 'unknown';
    } else if (Platform.isLinux) {
      LinuxDeviceInfo buildLinux = await deviceInfoPlugin.linuxInfo;
      return deviceName = buildLinux.name ?? 'unknown';
    } else {
      return deviceName = 'unknown device';
    }
  } on PlatformException {
    deviceName = 'Failed to get deviceName';
    return deviceName;
  }
}

int getPlatformAsInteger() {
  if (Platform.isAndroid) {
    return 1;
  } else if (Platform.isIOS) {
    return 2;
  } else if (Platform.isWindows) {
    return 3;
  } else {
    return 1;
  }
}

String getPlatformName() {
  String? platformName;
  if (Platform.isAndroid) {
    return platformName = 'Android';
  } else if (Platform.isIOS) {
    return platformName = 'iOS';
  } else if (Platform.isWindows) {
    return platformName = 'Windows';
  } else if (Platform.isMacOS) {
    return platformName = 'MacOS';
  } else if (Platform.isLinux) {
    return platformName = 'Linux';
  } else {
    return platformName = 'unknown device';
  }
}

Future<String> getDeviceVersion() async {
  String? deviceName;
  try {
    final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo buildAndroid = await deviceInfoPlugin.androidInfo;
      return deviceName = buildAndroid.version.release ?? 'unknown';
    } else if (Platform.isIOS) {
      IosDeviceInfo buildIos = await deviceInfoPlugin.iosInfo;
      return deviceName = buildIos.systemVersion ?? 'unknown';
    } else if (Platform.isWindows) {
      WindowsDeviceInfo buildWindows = await deviceInfoPlugin.windowsInfo;
      return deviceName = buildWindows.displayVersion ?? 'unknown';
    } else if (Platform.isMacOS) {
      MacOsDeviceInfo buildMacOs = await deviceInfoPlugin.macOsInfo;
      return deviceName = buildMacOs.kernelVersion ?? 'unknown';
    } else if (Platform.isLinux) {
      LinuxDeviceInfo buildLinux = await deviceInfoPlugin.linuxInfo;
      return deviceName = buildLinux.version ?? 'unknown linux device';
    } else {
      return deviceName = 'unknown device';
    }
  } on PlatformException {
    deviceName = 'Failed to get deviceName';
    return deviceName;
  }
}

String getTrayImagePath(String imageName) {
  return Platform.isWindows ? 'assets/$imageName.ico' : 'assets/$imageName.png';
}

String getImagePath(String imageName) {
  return Platform.isWindows ? 'assets/$imageName.png' : 'assets/$imageName.png';
}

Future<String> getDeviceId() async {
  String? deviceId;
  try {
    deviceId = await PlatformDeviceId.getDeviceId ?? 'unknown';
    return deviceId;
  } on PlatformException {
    deviceId = 'Failed to get deviceId.';
    CustomToast.showToast(deviceId);
    return deviceId;
  }
}

getTokenFromLocal() async {
  String? savedToken;
  SharedPreferences prefs = di();
  savedToken = await prefs.getString("bearer_token") ?? "";
  return savedToken.replaceAll("\"", "");
}

getFirebaseTokenLocal() async {
  String? firebaseTokenLocal;
  SharedPreferences prefs = di();
  firebaseTokenLocal = await prefs.getString(firebaseTokenKEY) ?? "";
  return firebaseTokenLocal;
}

Future<String> createFolderInAppDocDir() async {
  final Directory _appDocDirFolder = Directory('$androidDownloadPath');

  if (await _appDocDirFolder.exists()) {
    return _appDocDirFolder.path;
  } else {
    final Directory _appDocDirNewFolder =
        await _appDocDirFolder.create(recursive: true);
    return _appDocDirNewFolder.path;
  }
}

generateNotificationPdf(
    {required String name,
    required String id,
    required String createdAt,
    required String statusName,
    required String organisation,
    required String title,
    required String content}) async {
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
  final DateFormat formatterCommentDate = DateFormat('dd.MM.yyyy HH:mm');

  var status = await Permission.storage.status;
  if (!status.isGranted) {
    await Permission.storage.request();
  }
  final pdf = pw.Document();

  pw.Font? tamilFont =
      pw.Font.ttf(await rootBundle.load(Assets.fontsOpenSansRegular));

  pdf.addPage(pw.Page(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return pw.Container(
            child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text("Ijrochi",
                    style: pw.TextStyle(
                        color: PdfColor.fromHex("#06161A"),
                        fontSize: 40.sp,
                        font: tamilFont))
              ]),
              pw.SizedBox(
                height: 10.h,
              ),
              pw.Text(
                  "Generated by: ${name}, ${formatterCommentDate.format(DateTime.now())}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 30.h),
              pw.Text("Номер уведомления: ${id}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 10.h),
              pw.Text(
                  "Время отправки: ${formatterCommentDate.format(DateTime.parse(createdAt))}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 10.h),
              pw.Text("Статус: ${statusName}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 10.h),
              pw.Text("Отправил: ${organisation}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 40.h),
              pw.Text(title,
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 30.h),
              pw.Text(Bidi.stripHtmlIfNeeded(content),
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont))
            ]));
      }));

  String? path = "";
  if (Platform.isIOS || Platform.isWindows) {
    final Directory? downloadsDir = await getApplicationDocumentsDirectory();
    path = downloadsDir?.path;
  } else {
    path = "/storage/emulated/0/Download";
  }
  try {
    final file = File('${path}/notification${id}.pdf');
    await file.writeAsBytes(await pdf.save());
    Share.shareXFiles([XFile('${path}/notification${id}.pdf')]);
  } catch (e) {
    print("SHARE: ${e}");
  }
}

generateTaskPdf(
    {required String name,
    required String id,
    required String time,
    required String organisation,
    required String title,
    required String content}) async {
  var status = await Permission.storage.status;
  final DateFormat formatterCommentDate = DateFormat('dd.MM.yyyy HH:mm');

  if (!status.isGranted) {
    await Permission.storage.request();
  }
  final pdf = pw.Document();
  pw.Font? tamilFont =
      pw.Font.ttf(await rootBundle.load(Assets.fontsNunitoSansRegular));
  pdf.addPage(pw.Page(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return pw.Container(
            child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text("Ijrochi",
                    style: pw.TextStyle(
                        color: PdfColor.fromHex("#06161A"), fontSize: 40.sp))
              ]),
              pw.SizedBox(
                height: 10.h,
              ),
              pw.Text(
                  "Generated by: ${name}, ${formatterCommentDate.format(DateTime.now())}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 30.h),
              pw.Text("Номер уведомления: ${id}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 10.h),
              pw.Text("Время отправки: ${time}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 10.h),
              pw.Text("Отправил: ${organisation}",
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 40.h),
              pw.Text(title,
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont)),
              pw.SizedBox(height: 30.h),
              pw.Text(Bidi.stripHtmlIfNeeded(content),
                  style: pw.TextStyle(
                      color: PdfColor.fromHex("#06161A"),
                      fontSize: 24.sp,
                      font: tamilFont))
            ]));
      }));

  String? path = "";
  if (Platform.isIOS || Platform.isWindows) {
    final Directory? downloadsDir = await getApplicationDocumentsDirectory();
    path = downloadsDir?.path;
  } else {
    path = "/storage/emulated/0/Download";
  }
  try {
    final file = File('${path}/notification${id}.pdf');
    await file.writeAsBytes(await pdf.save());
    Share.shareXFiles([XFile('${path}/notification${id}.pdf')]);
  } catch (e) {
    print("SHARE: ${e}");
  }
}

dismissDialog(BuildContext? context) {
  if (context != null) {
    Navigator.pop(context);
  }
}

logoutServer() async {
  final Dio dio = di();
  try {
    await dio.get(logoutPath);
  } catch (e) {}
}

String archiveToStatus(bool? archive) {
  if (archive == true) {
    return ARCHIVE_STATUS;
  } else if (archive == false) {
    return NEW_STATUS;
  } else {
    return PROCESS_STATUS;
  }
}

Color statusToColor(int? status) {
  switch (status) {
    case 1:
      {
        return fastTabColor;
      }
    case 2:
      {
        return simpleTabColor;
      }
    case 3:
      {
        return importantTabColor;
      }
    case 4:
      {
        return veryImportantTabColor;
      }
    default:
      {
        return cBlackColor;
      }
  }
}

Color statusNameToColor(String? status) {
  switch (status) {
    case "today":
      {
        return fastTabColor;
      }
    case "withinThreeDays":
      {
        return simpleTabColor;
      }
    case "withinOneWeek":
      {
        return importantTabColor;
      }
    case "all":
      {
        return cBlackColor;
      }
    default:
      {
        return cBlackColor;
      }
  }
}

String statusToColorString(int? status) {
  switch (status) {
    case 1:
      {
        return fastTabColor.toHex();
      }
    case 2:
      {
        return simpleTabColor.toHex();
      }
    case 3:
      {
        return importantTabColor.toHex();
      }
    case 4:
      {
        return veryImportantTabColor.toHex();
      }
    default:
      {
        return cBlackColor.toHex();
      }
  }
}

String statusToColorStringTask(TaskTypeEnum type) {
  switch (type) {
    case TaskTypeEnum.all:
      {
        return fastTabColor.toHex();
      }
    case TaskTypeEnum.today:
      {
        return simpleTabColor.toHex();
      }
    case TaskTypeEnum.three:
      {
        return importantTabColor.toHex();
      }
    case TaskTypeEnum.weeks:
      {
        return veryImportantTabColor.toHex();
      }
    default:
      {
        return cBlackColor.toHex();
      }
  }
}

String statusToString(int? status, String? language) {
  // Define the translations for each status in different languages
  Map<String, Map<int, String>> translations = {
    'ru': {
      1: 'Срочно',
      2: 'Обычный',
      3: 'Важный',
      4: 'Очень важный',
    },
    'cr': {
      1: 'Тезкор',
      2: 'Оддий',
      3: 'Мухим',
      4: 'Ўта мухим',
    },
    'uz': {
      1: 'Tezkor',
      2: 'Oddiy',
      3: 'Muhim',
      4: 'O\'ta muhim',
    }
  };

  // Determine the default language if none is provided
  String selectedLanguage = language ?? 'uz';

  // Get the status string based on the provided status and language
  return translations[selectedLanguage]?[status] ??
      translations['uz']?[1] ??
      '';
}

NotificationEnumStatus indexToStatus(int index) {
  switch (index) {
    case 0:
      {
        return NotificationEnumStatus.all;
      }
    case 1:
      {
        return NotificationEnumStatus.fast;
      }
    case 2:
      {
        return NotificationEnumStatus.simple;
      }
    case 3:
      {
        return NotificationEnumStatus.important;
      }
    case 4:
      {
        return NotificationEnumStatus.veryImportant;
      }
    default:
      {
        return NotificationEnumStatus.all;
      }
  }
}

TaskTypeEnum indexToType(int index) {
  switch (index) {
    case 0:
      {
        return TaskTypeEnum.all;
      }
    case 1:
      {
        return TaskTypeEnum.today;
      }
    case 2:
      {
        return TaskTypeEnum.three;
      }
    case 3:
      {
        return TaskTypeEnum.weeks;
      }
    default:
      {
        return TaskTypeEnum.all;
      }
  }
}

int statusToInt(TaskCountEnumStatus status) {
  switch (status) {
    case TaskCountEnumStatus.news:
      {
        return 1;
      }
    case TaskCountEnumStatus.progress:
      {
        return 2;
      }
    case TaskCountEnumStatus.done:
      {
        return 3;
      }
    case TaskCountEnumStatus.late:
      {
        return 4;
      }
    case TaskCountEnumStatus.not_done:
      {
        return 5;
      }

    default:
      {
        return 1;
      }
  }
}

/// get moderator from api
Future getModerator() async {
  final Dio dio = di();
  final NetworkInfo networkInfo = di();
  final IsarService isarService = di();
  if (await networkInfo.isConnected) {
    try {
      var response = await dio.get(getModeratorPath);
      List<Moderator> list = listFromModerator(response.data);
      await isarService.isar.writeTxn(() async {
        await isarService.isar.moderators.clear();
      });
      await isarService.isar.writeTxn(() async {
        await isarService.isar.moderators.putAll(list);
      });
    } catch (e) {
      print("Error->${e}");
    }
  }
}

Future<List<NotificationPermission>> requestNotificationPermissions(
    BuildContext context,
    {
    // if you only intends to request the permissions until app level, set the channelKey value to null
    required String? channelKey,
    required List<NotificationPermission> permissionList}) async {
  // Check which of the permissions you need are allowed at this time
  List<NotificationPermission> permissionsAllowed = await AwesomeNotifications()
      .checkPermissionList(channelKey: channelKey, permissions: permissionList);

  // If all permissions are allowed, there is nothing to do
  if (permissionsAllowed.length == permissionList.length)
    return permissionsAllowed;

  // Refresh the permission list with only the disallowed permissions
  List<NotificationPermission> permissionsNeeded =
      permissionList.toSet().difference(permissionsAllowed.toSet()).toList();

  // Check if some of the permissions needed request user's intervention to be enabled
  List<NotificationPermission> lockedPermissions = await AwesomeNotifications()
      .shouldShowRationaleToRequest(
          channelKey: channelKey, permissions: permissionsNeeded);

  // If there is no permissions depending on user's intervention, so request it directly
  if (lockedPermissions.isEmpty) {
    // Request the permission through native resources.
    await AwesomeNotifications().requestPermissionToSendNotifications(
        channelKey: channelKey, permissions: permissionsNeeded);

    // After the user come back, check if the permissions has successfully enabled
    permissionsAllowed = await AwesomeNotifications().checkPermissionList(
        channelKey: channelKey, permissions: permissionsNeeded);
  } else {
    // If you need to show a rationale to educate the user to conceived the permission, show it
    await showDialog(
        context: context,
        builder: (context) => AlertDialog(
              backgroundColor: cWhiteColor,
              title: Text(
                LocaleKeys.notification_permissions.tr(),
                textAlign: TextAlign.center,
                maxLines: 2,
                style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w600),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 10.h,
                  ),
                  Icon(
                    Icons.notifications_active,
                    size: 100.w,
                    color: cFirstColor,
                  ),
                  SizedBox(
                    height: 30.h,
                  ),
                  Text(
                    LocaleKeys.give_notification_permissions.tr(),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 5.h),
                  Text(
                    lockedPermissions
                        .join(', ')
                        .replaceAll('NotificationPermission.', ''),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                    style:
                        TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w600),
                  ),
                ],
              ),
              actions: [
                TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      LocaleKeys.deny.tr(),
                      style: TextStyle(color: cRedColor, fontSize: 20.sp),
                    )),
                TextButton(
                  onPressed: () async {
                    Navigator.pop(context);

                    // Request the permission through native resources. Only one page redirection is done at this point.
                    await AwesomeNotifications()
                        .requestPermissionToSendNotifications(
                            channelKey: channelKey,
                            permissions: lockedPermissions);

                    // After the user come back, check if the permissions has successfully enabled
                    permissionsAllowed = await AwesomeNotifications()
                        .checkPermissionList(
                            channelKey: channelKey,
                            permissions: lockedPermissions);
                  },
                  child: Text(
                    LocaleKeys.allow.tr(),
                    style: TextStyle(
                        color: cFirstColor,
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ));
  }

  // Return the updated list of allowed permissions
  return permissionsAllowed;
}

getScheduledNotificationCount({bool? all = false, int? status}) async {
  final IsarService isarService = di();
  if (all == true) {
    int count = await isarService.isar.notificationDocs
        .filter()
        .scheduledTimeIsNotNull()
        .count();
    return count;
  } else {
    int count = await isarService.isar.notificationDocs
        .filter()
        .scheduledTimeIsNotNull()
        .statusEqualTo(status)
        .count();
    print("$status: $count");
    return count;
  }
}

///In most places these are used but it against DRY
bool isDark() {
  final SharedPreferences prefs = di();
  return prefs.getString(theme_pref) == "ThemeMode.dark";
}

launchCustomUrl(String url) async {
  var uri = Uri.parse(url);

  if (await canLaunchUrl(uri)) {
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  } else {
    throw 'Could not launch $url';
  }
}

BigInt hexStringToInt(String hexString) {
  return BigInt.parse(hexString, radix: 16);
}

String intToHexString(BigInt integer) {
  return integer.toRadixString(16);
}

bool canConvertBigIntToInt(BigInt bigInt) {
  return bigInt >= BigInt.from(-9223372036854775808) &&
      bigInt <= BigInt.from(9223372036854775807);
}

int bigIntToInt(BigInt bigInt) {
  if (canConvertBigIntToInt(bigInt)) {
    return bigInt.toInt();
  } else {
    throw RangeError("BigInt value is out of range for int");
  }
}

Future<bool> checkPayment() async {
  final Dio dio = di();
  GetStorage gs = di();
  var userId = gs.read(ID);

  try {
    final response = await dio.get(paymentCheckPath,
        queryParameters: {'_id': userId},
        options: Options(headers: <String, String>{
          'Content-Type': 'application/json',
        }));
    final data = response.data;
    print(data.toString());
    if (response.statusCode == 200) {
      return data['success'] ?? false;
    } else {
      CustomToast.showToast(data.toString());
      print(data.toString());
      return false;
    }
  } catch (e) {
    print(e);
    return false;
  }
}

Widget ChatItemMe(Map<String, dynamic> chat) {
  if (chat.containsKey("file")) {
    return BlocConsumer<DownloadChatFileBloc, DownloadChatFileState>(
      listener: (context, state) {
        if (state.status == DownloadChatFileStatus.success &&
            chat["_id"] == state.messageId) {
          try {
            OpenFilex.open(File(state.localPath ?? '').path);
          } catch (e) {
            CustomToast.showToast(LocaleKeys.error_open_file.tr());
          }
        } else if (state.status == DownloadChatFileStatus.failure &&
            chat["_id"] == state.messageId) {
          CustomToast.showToast(state.message ?? "");
        }
      },
      builder: (context, state) {
        return InkWell(
          onTap: () async {
            BlocProvider.of<DownloadChatFileBloc>(context).add(
                DownloadAndOpenChatFileEvent(
                    fileUrl: chat["file"]["path"], messageId: chat["_id"]));
          },
          child: Container(
            padding: EdgeInsets.all(8),
            height: 80.h,
            alignment: Alignment.center,
            child: Row(
              children: [
                Expanded(
                    flex: 2,
                    child: state.status == DownloadChatFileStatus.loading
                        ? chat["_id"] == state.messageId
                            ? CircularProgressIndicator()
                            : CircleAvatar(
                                backgroundColor: cWhiteColor,
                                child: SvgPicture.asset(Assets.iconsDocument))
                        : CircleAvatar(
                            backgroundColor: cWhiteColor,
                            child: SvgPicture.asset(Assets.iconsDocument))),
                SizedBox(
                  width: 8.w,
                ),
                Expanded(
                  flex: 8,
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                          alignment: Alignment.centerLeft,
                          height: 60.h,
                          child: Text(
                            chat["file"]['originalname'].toString(),
                            style: TextStyle(
                              fontSize: 10.sp,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(kbToMb(chat['file']['size']),
                              style: TextStyle(fontSize: 8.sp)),
                          Text(
                            chat['date'],
                            style: TextStyle(fontSize: 8.sp),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
            margin: EdgeInsets.symmetric(vertical: 2.h),
            decoration: BoxDecoration(
                color: cFirstColor.withAlpha(50),
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(cRadius16.r),
                    topLeft: Radius.circular(cRadius16.r),
                    topRight: Radius.circular(cRadius16.r))),
          ),
        );
      },
    );
  } else if (chat.containsKey("message")) {
    return Container(
      padding: EdgeInsets.all(12.w),
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            chat['message'].toString(),
            textAlign: TextAlign.left,
          ),
          SizedBox(
            height: 8.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                chat['date'],
                style: TextStyle(
                  fontSize: 8.sp,
                ),
              ),
            ],
          )
        ],
      ),
      margin: EdgeInsets.symmetric(vertical: 2.h),
      decoration: BoxDecoration(
          color: cFirstColor.withAlpha(50),
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(cRadius16.r),
              topLeft: Radius.circular(cRadius16.r),
              topRight: Radius.circular(cRadius16.r))),
    );
  } else {
    return SizedBox();
  }
}

Widget ChatItemOthers(Map<String, dynamic> chat) {
  if (chat.containsKey("file")) {
    return BlocConsumer<DownloadChatFileBloc, DownloadChatFileState>(
      listener: (context, state) {
        if (state.status == DownloadChatFileStatus.success &&
            chat["_id"] == state.messageId) {
          try {
            OpenFilex.open(File(state.localPath ?? '').path);
          } catch (e) {
            CustomToast.showToast(LocaleKeys.error_open_file.tr());
          }
        } else if (state.status == DownloadChatFileStatus.failure &&
            chat["_id"] == state.messageId) {
          CustomToast.showToast(state.message ?? "");
        }
      },
      builder: (context, state) {
        return InkWell(
          onTap: () {
            BlocProvider.of<DownloadChatFileBloc>(context).add(
                DownloadAndOpenChatFileEvent(
                    fileUrl: chat["file"]["path"], messageId: chat["_id"]));
          },
          child: Container(
            padding: EdgeInsets.all(8),
            height: 80.h,
            alignment: Alignment.center,
            child: Row(
              children: [
                Expanded(
                    flex: 2,
                    child: state.status == DownloadChatFileStatus.loading
                        ? chat["_id"] == state.messageId
                            ? CircularProgressIndicator()
                            : CircleAvatar(
                                backgroundColor: cWhiteColor,
                                child: SvgPicture.asset(Assets.iconsDocument))
                        : CircleAvatar(
                            backgroundColor: cWhiteColor,
                            child: SvgPicture.asset(Assets.iconsDocument))),
                SizedBox(
                  width: 8.w,
                ),
                Expanded(
                  flex: 8,
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                          alignment: Alignment.centerLeft,
                          height: 60.h,
                          child: Text(
                            chat["file"]['originalname'].toString(),
                            style:
                                TextStyle(fontSize: 10.sp, color: cWhiteColor),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 4.w,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(kbToMb(chat['file']['size']),
                              style: TextStyle(fontSize: 8.sp)),
                          Text(
                            chat['date'],
                            style:
                                TextStyle(fontSize: 8.sp, color: cWhiteColor),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
            margin: EdgeInsets.symmetric(vertical: 2.h),
            decoration: BoxDecoration(
                color: cFirstColor,
                borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(cRadius16.r),
                    topLeft: Radius.circular(cRadius16.r),
                    topRight: Radius.circular(cRadius16.r))),
          ),
        );
      },
    );
  } else if (chat.containsKey("message")) {
    return Container(
      padding: EdgeInsets.all(12.w),
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            chat['message'].toString(),
            style: TextStyle(color: cWhiteColor),
            textAlign: TextAlign.left,
          ),
          SizedBox(
            height: 8.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                chat['date'],
                style: TextStyle(fontSize: 8.sp, color: cWhiteColor),
              ),
            ],
          )
        ],
      ),
      margin: EdgeInsets.symmetric(vertical: 2.h),
      decoration: BoxDecoration(
          color: cFirstColor,
          borderRadius: BorderRadius.only(
              bottomRight: Radius.circular(cRadius16.r),
              topLeft: Radius.circular(cRadius16.r),
              topRight: Radius.circular(cRadius16.r))),
    );
  } else {
    return SizedBox();
  }
}

saveTokenAndData(String token, dynamic decodedToken, String phone_number) {
  GetStorage getStorage = di();
  String fullName = decodedToken['title']?['fullName'] ?? "";
  String id = decodedToken['_id'];
  getStorage.write(BEARER_TOKEN, token);
  getStorage.write(ID, id);
  getStorage.write(FULL_NAME, fullName);
  getStorage.write(PHONE_NUMBER, phone_number);
}

String kbToMb(int kb) {
  // Check if kilobytes exceed 1024
  if (kb > 1024) {
    double mb = kb / 1024;
    return '$mb MB';
  } else {
    return '$kb KB';
  }
}

Map<TaskCountEnumStatus, String> urls = {
  TaskCountEnumStatus.news: getNewTasksPath,
  TaskCountEnumStatus.progress: getProgressTasksPath,
  TaskCountEnumStatus.late: getLateTasksPath,
  TaskCountEnumStatus.done: getDoneTasksPath,
  TaskCountEnumStatus.not_done: getDoneTasksPath
};
