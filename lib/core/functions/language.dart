
import 'package:ijrochi/core/database/embeded_models.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:shared_preferences/shared_preferences.dart';

String? languageText(Title? title) {
  SharedPreferences sharedPreferences = di();
  String lang = sharedPreferences.getString(language_pref) ?? 'uz';
  switch (lang) {
    case 'uz':
      {
        return title?.uz;
      }
    case 'ru':
      {
        return title?.ru;
      }
    case 'cr':
      {
        return title?.cr;
      }
  }
}

String? languageStatusText(StatusName? statusName) {
  SharedPreferences sharedPreferences = di();
  String lang = sharedPreferences.getString(language_pref) ?? 'uz';
  switch (lang) {
    case 'uz':
      {
        return statusName?.uz;
      }
    case 'ru':
      {
        return statusName?.ru;
      }
    case 'cr':
      {
        return statusName?.uz;
      }
  }


}
