import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_type_enum.dart';
import 'package:isar/isar.dart';

part 'embeded_models.g.dart';

@embedded
class Title {
  Title({
    this.ru,
    this.cr,
    this.uz,
  });

  Title.fromJson(dynamic json) {
    ru = json['ru'];
    cr = json['cr'];
    uz = json['uz'];
  }

  String? ru;
  String? cr;
  String? uz;
}

@embedded
class StatusName {
  StatusName({
    this.uz,
    this.ru,
    this.cr,
  });

  StatusName.fromJson(dynamic json) {
    uz = json['uz'];
    ru = json['ru'];
    cr = json['cr'];
  }

  String? uz;
  String? ru;
  String? cr;
}

@embedded
class Notification {
  Notification({
    this.id,
    this.title,
    this.content,
    this.files,
    this.moderator,
    this.serialNumber,
    this.statusName,
    this.statusColor,
    this.notificationId,
  });

  Notification.fromJson(dynamic json, String? notificationId, int? status) {
    id = json['_id'];
    title = json['title'];
    content = json['content'];
    if (json['files'] != null) {
      files = [];
      json['files'].forEach((v) {
        files?.add(Files.fromJson(v));
      });
    }
    moderator = json['moderator'] != null
        ? EmbeddedModerator.fromJson(json['moderator'])
        : null;
    serialNumber = json['serialNumber'];
    this.notificationId = notificationId;

    this.statusName = StatusName(
        uz: statusToString(status, 'uz'),
        ru: statusToString(status, 'ru'),
        cr: statusToString(status, 'cr'));
    this.statusColor = statusToColorString(status);
  }

  String? id;
  String? title;
  String? content;
  List<Files>? files;
  EmbeddedModerator? moderator;
  int? serialNumber;
  String? notificationId;
  StatusName? statusName;
  String? statusColor;
}

@embedded
class EmbeddedModerator {
  EmbeddedModerator({
    this.id,
    this.fullName,
  });

  EmbeddedModerator.fromJson(dynamic json) {
    id = json['_id'];
    fullName = json['fullName'];
  }

  String? id;
  String? fullName;
}

@embedded
class Chat {
  Chat({
    this.message,
    this.date,
    this.sender,
    this.id,
  });

  Chat.fromJson(dynamic json) {
    message = json['message'];
    date = json['date'];
    sender = json['sender'];
    id = json['_id'];
  }

  String? message;
  String? date;
  bool? sender;
  String? id;
}

@embedded
class Files {
  Files({
    this.type,
    this.originalname,
    this.path,
    this.size,
  });

  Files.fromJson(dynamic json) {
    type = json['type'];
    originalname = json['originalname'];
    path = json['path'];
    size = json['size'];
  }

  String? type;
  String? originalname;
  String? path;
  int? size;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['type'] = type;
    map['originalname'] = originalname;
    map['path'] = path;
    map['size'] = size;
    return map;
  }
}

@embedded
class DocumentNumberModel {
  DocumentNumberModel({this.id, this.title});

  DocumentNumberModel.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    title = json['title'];
  }

  String? id;
  String? title;
}

@embedded
class Task {
  Task({
    this.files,
    this.creationTime,
    this.id,
    this.title,
    this.content,
    this.status,
    this.documentDate,
    this.date,
    this.startDate,
    this.endDate,
    this.documentNumber,
    this.contactsCount,
    this.serialNumber,
    this.day,
    this.repeat,
    this.headNumber,
    this.documentType,
    this.responsibleComplex,
    this.organization,
    this.moderator,
    this.statusColor,
    this.taskId,
    this.documentData,
  });

  Task.fromJson(dynamic json, EmbeddedModerator? moderator, TaskTypeEnum? type,
      String? taskId) {
    if (json['files'] != null) {
      files = [];
      json['files'].forEach((v) {
        files?.add(Files.fromJson(v));
      });
    }
    creationTime = json['creationTime'];
    id = json['_id'];
    title = json['title'];
    content = json['content'];
    status = json['status'];
    documentDate = json['documentDate'];
    date = json['date'];
    startDate = json['startDate'];
    endDate = json['endDate'];

    documentNumber = json['documentNumber'] != null
        ? DocumentNumberModel.fromJson(json['documentNumber'])
        : null;

    contactsCount = json['contactsCount'];
    serialNumber = json['serialNumber'];
    day = json['day'];
    repeat = json['repeat'];
    headNumber = json['headNumber'];

    documentType = json['documentType'] != null
        ? DocumentType.fromJson(json['documentType'])
        : null;

    responsibleComplex = json['responsibleComplex'];
    organization = json['organization'];

    /// New fields
    this.moderator = moderator;
    this.statusColor = statusToColorStringTask(type ?? TaskTypeEnum.all);
    this.taskId = taskId;
    this.documentData = json['documentData'];
  }

  List<Files>? files;
  String? creationTime;
  String? id;
  String? title;
  String? content;
  int? status;
  String? documentDate;
  String? date;
  String? startDate;
  String? endDate;
  DocumentNumberModel? documentNumber;
  int? contactsCount;
  int? serialNumber;
  int? day;
  bool? repeat;
  String? headNumber;
  DocumentType? documentType;
  @ignore
  dynamic responsibleComplex;
  @ignore
  dynamic organization;

  /// New fields
  EmbeddedModerator? moderator;
  String? statusColor;
  String? taskId;
  @ignore
  dynamic documentData;
}


@embedded
class DocumentType {
  DocumentType({this.id, this.title});

  DocumentType.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
  }

  String? id;
  String? title;
}

@embedded
class Payments {
  Payments({
    this.clickTransId,
    this.amount,
    this.error,
    this.errorNote,
    this.month,
    this.startDate,
    this.endDate,
  });

  Payments.fromJson(dynamic json) {
    clickTransId = json['click_trans_id'];
    amount = json['amount'];
    error = json['error'];
    errorNote = json['error_note'];
    month = json['month'];
    startDate = json['startDate'];
    endDate = json['endDate'];
  }

  String? clickTransId;
  String? amount;
  String? error;
  String? errorNote;
  int? month;
  String? startDate;
  String? endDate;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['click_trans_id'] = clickTransId;
    map['amount'] = amount;
    map['error'] = error;
    map['error_note'] = errorNote;
    map['month'] = month;
    map['startDate'] = startDate;
    map['endDate'] = endDate;
    return map;
  }
}
