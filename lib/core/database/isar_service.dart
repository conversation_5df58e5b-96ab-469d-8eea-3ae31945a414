import 'package:ijrochi/features/notifications/notification/model/moderator.dart';
import 'package:ijrochi/features/notifications/notification/model/notification.dart';
import 'package:ijrochi/features/notifications/notification_count/models/notification_count.dart';
import 'package:ijrochi/features/settings/data/models/session_model.dart';
import 'package:ijrochi/features/tasks/task/model/task.dart';
import 'package:ijrochi/features/tasks/task_count/model/task_count.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';

const SCHEMES = [
  NotificationCountSchema,
  ModeratorSchema,
  NotificationDocsSchema,
  TaskCountSchema,
  TaskDocsSchema,
  SessionModelSchema
];

class IsarService {
  late final Isar isar;

  IsarService._create(this.isar);

  static Future<IsarService> buildIsarService() async {
    final dir = await getApplicationDocumentsDirectory();
    final isar = await Isar.open(
      SCHEMES,
      directory: dir.path,
    );

    print('=== Isar opened!');
    return IsarService._create(isar);
  }
}

Future<Isar> getIsarForSchedule() async {
  var isar = await Isar.getInstance();

  print("=== Before, isar is: ${isar?.path}"); //PRINT 1

  if (isar == null) {
    print("=== Isar is not open, so opening it..");
    final dir = await getApplicationSupportDirectory();
    return isar = await Isar.open(
      SCHEMES,
      directory: dir.path,
    );
  } else {
    return isar;
  }
}
