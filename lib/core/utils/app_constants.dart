import 'package:flutter/material.dart';

// All colors
const cFirstColor = Color(0xFF008ABF);
const cSecondColor = Color(0xFF089AD1);
const cThirdColor = Color(0xFF74b5d3);
const cTextColor = Color(0xFF475E6A);
const cBlackColor = Color(0xFF000000);
const cWhiteColor = Color(0xFFFFFFFF);
const cRedColor = Color(0xFFE36E6E);
const cGrayColor = Color(0xFF949494);
const cGrayColorLight = Color(0xFFE8E5E5);
const cGrayColor2 = Color(0xFF4F4F4F);
const cGrayColor3 = Color(0xFFFBFDFF);
const cGrayColor4 = Color(0xFF333333);
const cGreenColor = Color(0xFF13BB42);
const cYellowColor = Color(0xFFFFC92F);
//const cDarkYellowColor = Color(0xFFC49400);
const cBackColor = Color(0xFFF8F8F8);
const backgroundLight = Color(0xFFF1F1F1);
const backgroundDark = Color(0xFF1C2632);
const cBlueLight = Color(0xFF6AB3EA);
const cDateBackgroundLight = Color(0xFFE6E6E6);
const cDateBackgroundDark = Color(0x26FFFFFF);

const fastTabColor = Color(0xFFA8A9AD);
const simpleTabColor = Color(0xFF089AD1);
const importantTabColor = Color(0xFFF39703);
const veryImportantTabColor = Color(0xFFF36571);

///Dark
const cFourthColorDark = Color(0xFF101322);
const cPrimaryTextDark = Color(0xFFA3A9C8);
const cCardDarkColor = Color(0xFF313551);

Color cBottomMenuIconColor = HexColor.fromHex("#8E95A3");
Color cMainIconColor = HexColor.fromHex("#323232");
Color cDarkYellowColor = HexColor.fromHex("#F39703");
//Dark theme Color:
Color cFirstColorDark = HexColor.fromHex("#1F3042");
Color cCardColorDark = HexColor.fromHex("#314960");
Color cBackgroundColor = HexColor.fromHex("#2B3B4B");
Color cGrayTextColor = HexColor.fromHex("#AAB3BC");
Color cGrayTextColor1 = HexColor.fromHex("#A8A9AD");
Color cGrayBackgroundColor = HexColor.fromHex("#E9EAEC");
Color cRedTextColor = HexColor.fromHex("#F4676C");
Color cBlueDarkColor = HexColor.fromHex("#192633");
Color cBackDarkColor1 = HexColor.fromHex("#37485E");
const cBackDarkColor2 = Color(0xFF233040);
Color cPinkColor = HexColor.fromHex("#F07373");

// All gradient
const cFirstGradient = LinearGradient(
  colors: [cSecondColor, cFirstColor],
  begin: Alignment.topCenter,
  end: Alignment.bottomCenter,
);

const cSecondGradient = LinearGradient(
  colors: [Colors.transparent, cFirstColor],
  begin: Alignment.centerLeft,
  end: Alignment.centerRight,
);

// All keys for local caches
const String userData = 'user_data';

// All table names for local databases
const String sessionBox = 'session_box';
const String contentCountBox = 'content_count_box';
const String categoryBox = 'category_box';
const String subCategoryBox = 'sub_category_box';
const String objectBox = 'object_box';
const String newHistoryBox = 'new_history_box';
const String oldHistoryBox = 'old_history_box';
const String profileBox = 'profile_box';
const String forSendBox = 'for_send_box';

// All sizes
const double cRadius8 = 8.0;
const double cRadius10 = 10.0;
const double cRadius12 = 12.0;
const double cRadius16 = 16.0;
const double cRadius22 = 22.0;

const double cNumberLockW90 = 95.0;
const double cNumberLockH90 = 90.0;
const double cNumberLockW70 = 75.0;
const double cNumberLockH70 = 70.0;
const double cNumberLockText42 = 42.0;

// consts
String version = "app_version";
const String theme_pref = "theme_pref";
const String language_pref = "language_pref";
const String pin_code_pref = "pin_code";
const String BEARER_TOKEN = "bearer_token";
const String ID = "id";
const String FULL_NAME = "full_name";
const String PHONE_NUMBER = "phone_number";
const String mobileTokenKEY = "mobile_token";
const String firebaseTokenKEY = "firebase_token";

const String NULL = "NULL";
const String NOTIFICATION = "notification";
const String TASK = "task";
const String NEW = "NEW";
const String ACCEPT = "ACCEPT";
const String OPEN = "OPEN";
const String REDIRECT = "REDIRECT";
const String DISMISS = "DISMISS";
const String YES = "YES";
const String NO = "NO";
const String SOCKET_SWITCH = "SOCKET";
const String FCM_SWITCH = "FCM";
const String SWITCH_PUSH = "SWITCH_PUSH";
const String SUPPORT_TEL = "+999732440535";
const String EMAIL = "<EMAIL>";
const String TELEGRAM = "https://t.me/flutterblogs";
const String SUPPORT_TG = "https://t.me/ijrochi_support";
const String WEBSITE = "ijrochi.uz";
String APP_VERSION = "app_version";


const String NO_INTERNET = "no_internet";
const String ERROR = "error";
const String HAVE_INTERNET = "have_internet";
const String androidDownloadPath = "/storage/emulated/0/Download/Ijrochi/";

const String NEW_STATUS = "new_status";
const String PROCESS_STATUS = "process_status";
const String ARCHIVE_STATUS = "archive_status";


const String IS_PAYMENT_DONE = "is_payment_done";
const String functional_live = "functional_live";

///Secrets
const String CLICK_SERVICE_ID = '36097';
const String CLICK_MERCHANT_ID = '24892';
const String CLICK_MERCHANT_USER_ID = '44087';
const String PAYME_MERCHANT_ID = '669b7b67caf5855adf56c4bf';


// lock number style
const numStyle =
    TextStyle(fontSize: 30, fontWeight: FontWeight.w400, color: cWhiteColor);

///fonts
const bold = "assets/fonts/Inter-Bold.ttf";
const semiBold = "assets/fonts/Inter-SemiBold.ttf";
const medium = "assets/fonts/Inter-Medium.ttf";
const regular = "assets/fonts/Inter-Regular.ttf";
const interFont = 'Inter';
const LAST_ACTION_STAMP = 'LAST_ACTION_STAMP';

extension HexColor on Color {
  static Color fromHex(String hexColorString) {
    hexColorString = hexColorString.replaceAll("#", "");
    if (hexColorString.length == 6) {
      hexColorString = "FF$hexColorString";
    }
    return Color(int.parse(hexColorString, radix: 16));
  }

  // Method to convert a Color object to a hexadecimal string
  String toHex({bool leadingHashSign = true}) {
    int alpha = this.alpha;
    int red = this.red;
    int green = this.green;
    int blue = this.blue;

    return '${leadingHashSign ? '#' : ''}'
        '${alpha.toRadixString(16).padLeft(2, '0').toUpperCase()}'
        '${red.toRadixString(16).padLeft(2, '0').toUpperCase()}'
        '${green.toRadixString(16).padLeft(2, '0').toUpperCase()}'
        '${blue.toRadixString(16).padLeft(2, '0').toUpperCase()}';
  }
}