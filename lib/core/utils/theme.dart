import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ijrochi/core/utils/app_constants.dart';

class IjrochiTheme {
  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primaryColor: cFirstColor,
    cardTheme: CardThemeData(color: cBackColor),
    radioTheme: RadioThemeData(overlayColor:
        MaterialStateProperty.resolveWith<Color>((Set<MaterialState> states) {
      return cFirstColor;
    }), fillColor: //here -----
        MaterialStateProperty.resolveWith<Color>((Set<MaterialState> states) {
      return cFirstColor;
    })),
    scaffoldBackgroundColor: cWhiteColor,
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: cWhiteColor,
      selectedLabelStyle: TextStyle(color: cBlackColor),
      unselectedLabelStyle: TextStyle(color: cFirstColor),
    ),
    textTheme: TextTheme(
        displayMedium: TextStyle(color: cBlackColor, fontFamily: interFont,),),
      textSelectionTheme:TextSelectionThemeData(cursorColor: cFirstColor),
    iconTheme: IconThemeData(color: cBlackColor),
    appBarTheme: AppBarTheme(
      color: cWhiteColor,
      titleTextStyle: TextStyle(color: cBlackColor),
      iconTheme: IconThemeData(color: cFirstColor),
      // systemOverlayStyle: SystemUiOverlayStyle(
      //   statusBarColor: cWhiteColor,
      //   statusBarIconBrightness: Brightness.dark,
      //   statusBarBrightness: Brightness.light,
      // ),
    ),
  );

  static ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primaryColor: cFirstColorDark,
    cardTheme: CardThemeData(color: cCardColorDark),
    radioTheme: RadioThemeData(overlayColor:
    MaterialStateProperty.resolveWith<Color>((Set<MaterialState> states) {
      return cFirstColor;
    }), fillColor: //here -----
    MaterialStateProperty.resolveWith<Color>((Set<MaterialState> states) {
      return cFirstColor;
    })),
    scaffoldBackgroundColor: cFirstColorDark,
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: cFirstColorDark,
        selectedLabelStyle: TextStyle(color: cWhiteColor),
        unselectedLabelStyle: TextStyle(color: cBlackColor)),
    textTheme: TextTheme(
      displayMedium: TextStyle(color: cWhiteColor, fontFamily: interFont),
    ),
    textSelectionTheme:TextSelectionThemeData(cursorColor: cWhiteColor),
    iconTheme: IconThemeData(color: cWhiteColor),
    appBarTheme: AppBarTheme(
      iconTheme: IconThemeData(color: cBlueLight),
      color: cBackDarkColor2,
      titleTextStyle: TextStyle(color: cWhiteColor),
      // systemOverlayStyle: SystemUiOverlayStyle(
      //   statusBarColor: cFirstColorDark,
      //   statusBarIconBrightness: Brightness.dark,
      //   statusBarBrightness: Brightness.light,
      // ), // Status bar color
    ),
  );
}
