import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

import '../utils/app_constants.dart';

class NoMoreWidget extends StatelessWidget {
  final VoidCallback onTap;

  const NoMoreWidget({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300.h,
      child: Column(
        children: [
          SizedBox(
            height: 20.h,
          ),
          Icon(
            Icons.refresh,
            size: 50,
            color: cFirstColor,
          ),
          Padding(
              padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 10.w),
              child: Text(
                LocaleKeys.refresh_desc.tr(),
                textAlign: TextAlign.center,
                style: TextStyle(color: isDark() ? cWhiteColor : cBlackColor,fontSize: 16.sp),
              )),
          CupertinoButton(
              child: Text(
                LocaleKeys.action_update.tr(),
                style: TextStyle(color: cWhiteColor),
              ),
              color: cFirstColor,
              onPressed: () {
                onTap();
              }),
          SizedBox(
            height: 20.h,
          )
        ],
      ),
    );
  }
}
