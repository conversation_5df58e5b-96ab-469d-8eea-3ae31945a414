import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:group_radio_button/group_radio_button.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/settings/presentation/bloc/settings_bloc.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageDialog extends StatefulWidget {
  final VoidCallback onSelectCallBack;

  static Widget screen({required VoidCallback onSelectCallBack}) {
    return BlocProvider<SettingsBloc>(
      create: (context) => di<SettingsBloc>(),
      child: LanguageDialog(onSelectCallBack: onSelectCallBack),
    );
  }

  const LanguageDialog({Key? key, required this.onSelectCallBack})
      : super(key: key);

  @override
  State<LanguageDialog> createState() => _LanguageDialogState();
}

class _LanguageDialogState extends State<LanguageDialog> {
  final SharedPreferences prefs = di();
  late SettingsBloc _bloc;

  String _verticalGroupValue = "O\'zbekcha";
  final _status = [
    "O\'zbekcha",
    "Ўзбекча",
    "Русский",
  ];
  BuildContext? dcontext;

  dismissDailog() {
    if (dcontext != null) {
      Navigator.pop(dcontext!);
    }
  }

  void setLanguage(String lang) async {
    await prefs.setString(language_pref, lang);
  }

  String languageText() {
    String lang = prefs.getString(language_pref) ?? 'uz';
    print(lang);
    switch (lang) {
      case 'uz':
        {
          return 'O\'zbekcha';
        }
      case 'ru':
        {
          return 'Русский';
        }
      case 'cr':
        {
          return 'Ўзбекча';
        }
      default:
        {
          return 'O\'zbekcha';
        }
    }
  }

  @override
  void initState() {
    _bloc = BlocProvider.of<SettingsBloc>(context);
    languageText();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _verticalGroupValue = languageText();
    return StatefulBuilder(builder: (context, setState) {
      return Dialog(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(4.r))),
        child: Container(
          decoration: BoxDecoration(
              color: themeIdentify(context) == true
                  ? cBackDarkColor2
                  : cWhiteColor,
              borderRadius: BorderRadius.all(Radius.circular(4.r))),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 20, top: 20),
                child: Text(
                  LocaleKeys.select_language.tr(),
                  style: TextStyle(fontSize: 16.sp, fontFamily: medium),
                ),
              ),
              SizedBox(
                height: 6.h,
              ),
              RadioGroup<String>.builder(
                groupValue: _verticalGroupValue,
                onChanged: (value) {
                  _bloc.add(SaveSelectedLangEvent(value.toString()));
                  _verticalGroupValue = value ?? '';
                  setState(() {
                    _verticalGroupValue = value ?? '';
                    if (_verticalGroupValue == 'O\'zbekcha') {
                      context.setLocale(Locale('uz', 'latin'));
                      setLanguage('uz');
                    } else if (_verticalGroupValue == 'Русский') {
                      context.setLocale(Locale('ru'));
                      setLanguage('ru');
                    } else {
                      context.setLocale(Locale('uz', 'cyrillic'));
                      setLanguage('cr');
                    }
                    widget.onSelectCallBack();
                    dismissDailog();
                  });
                },
                items: _status,
                textStyle: TextStyle(
                    fontSize: 16.sp,
                    color:
                        themeIdentify(context) ? cGrayTextColor : cBlackColor),
                itemBuilder: (item) => RadioButtonBuilder(
                  item,
                ),
                fillColor: Colors.blue,
              ),
              SizedBox(
                height: 20.h,
              ),
            ],
          ),
        ),
      );
    });
  }
}
