import 'package:flutter/material.dart';

class KeyboardDismissibleWidget extends StatelessWidget {
  final Widget child;

  const KeyboardDismissibleWidget({required this.child, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus(); // Dismiss the keyboard
      },
      behavior: HitTestBehavior.opaque, // Ensures the tap gesture works even on empty spaces
      child: child,
    );
  }
}
