import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class FailureWidget extends StatelessWidget {
  final VoidCallback onTap;
  final String title;
  final bool isDark;

  const FailureWidget(
      {super.key,
      required this.onTap,
      required this.title,
      required this.isDark});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error,
            color: cRedColor,
            size: 100.w,
          ),
          Sized<PERSON><PERSON>(
            height: 10.h,
          ),
          Text(title),
          Sized<PERSON><PERSON>(
            height: 5.h,
          ),
          CupertinoButton(
              child: Text(LocaleKeys.reload.tr(),style: TextStyle(color: cWhiteColor),),
              color: cGrayColor,
              onPressed: () {
                onTap();
              }),
        ],
      ),
    );
  }
}
