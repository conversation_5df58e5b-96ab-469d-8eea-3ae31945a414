import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/translations/locale_keys.g.dart';

class EmptyListWidget extends StatelessWidget {
  final VoidCallback onTap;
  final String title;
  final bool isDark;

  const EmptyListWidget(
      {super.key,
      required this.onTap,
      required this.title,
      required this.isDark});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 250.h,
        ),
        Text(
          LocaleKeys.no_data_fount.tr(),
          style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 25.sp,
              color: isDark ? cWhiteColor : cBlackColor),
        ),
        SizedBox(
          height: 15.h,
        ),
        Text(LocaleKeys.empty_list.tr(),
            style: TextStyle(
                fontSize: 20.sp,
                color: isDark ? cWhiteColor : cBlackColor)),
        SizedBox(
          height: 15.h,
        ),
        CupertinoButton(
            child: Text(LocaleKeys.action_update.tr(),style: TextStyle(color: cWhiteColor),),
            color: cFirstColor,
            onPressed: () {
              onTap();
            }),
      ],
    );
  }
}
