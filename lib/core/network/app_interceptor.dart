import 'package:context_holder/context_holder.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ijrochi/core/functions/functions.dart';
import 'package:ijrochi/core/utils/app_constants.dart';
import 'package:ijrochi/di/dependency_injection.dart';
import 'package:ijrochi/features/payments/lock_switcher.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppInterceptor extends Interceptor {
  final GetStorage getStorage;

  AppInterceptor({required this.getStorage});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (getStorage.read(BEARER_TOKEN) != null) {
      options.headers['Authorization'] =
          'Bearer ${getStorage.read(BEARER_TOKEN)}';
      options.headers["Accept"] = "*/*";
    }
    return super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    return super.onResponse(response, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) async {
    try {
      //|| err.response?.statusCode == 403
      if (err.response?.statusCode == 401 || err.response?.statusCode == 403) {
        clearAndLogout(ContextHolder.currentContext);
      }

      if (err.response?.statusCode == 408) {
        SharedPreferences prefs = di();
        await prefs.setBool(IS_PAYMENT_DONE, false);

        WidgetsBinding.instance.addPostFrameCallback((d) {
          Navigator.pushReplacement(
              ContextHolder.currentContext,
              MaterialPageRoute(
                builder: (context) => LockProvider(),
              ));
        });
      }
    } catch (e) {
      return super.onError(err, handler);
    }

    return super.onError(err, handler);
  }
}
