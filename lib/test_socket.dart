import 'package:socket_io_client/socket_io_client.dart';

void main() {
  Socket socket = io(
      'http://************:3202',
      OptionBuilder().setAuth({
        "token":
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2NmFjN2U1ODAyMGZiYTUxOGQxYjU3MzIiLCJ0aXRsZSI6eyJtYWNBZGRyZXNzIjoiNDFlMjNmNjljMjM1ZTliNSIsImZ1bGxOYW1lIjoiQWttYWwifSwicm9sZSI6InBlcmZvcm1lciIsImlhdCI6MTcyNDA2MjAwNSwiZXhwIjoxNzMwMTEwMDA1fQ.Mhs6YuIhUxg1a8nyavVU0LdSCBkq8DFQNiFpBCKJFUg"
      }).setTransports(['websocket']).build());

  socket.connect();

  socket.onConnect((_) {
    print('connect');
    socket.emit('msg', 'test');
  });

  socket.on('connect', (data) => print("connected message:${data}"));

  socket.emit(
      "message", {"id": "66c2e31319758bc4d3106574", "type": "notification"});
  socket.on("chats", (data) => print("received message:${data}"));

  socket.on("notifications", (data) {
    print("notification:" + data.toString());
  });
}
