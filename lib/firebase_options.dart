// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC-ogc0g7jOn-hgdttYISdAgcVtNWttrpA',
    appId: '1:498364100707:android:e8950c65fe8687c3ca8856',
    messagingSenderId: '498364100707',
    projectId: 'ijro-uz',
    storageBucket: 'ijro-uz.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAh40BncZfWUvPVe1bGuIuCsWDe2Eb_ZpE',
    appId: '1:498364100707:ios:75ae2f5bf1f17c23ca8856',
    messagingSenderId: '498364100707',
    projectId: 'ijro-uz',
    storageBucket: 'ijro-uz.firebasestorage.app',
    iosBundleId: 'uz.premiumsoft.ijrochi',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyALPqonSsUPQrRU_-w2lpJndWNQcTyOprI',
    appId: '1:498364100707:web:297931ea642619bdca8856',
    messagingSenderId: '498364100707',
    projectId: 'ijro-uz',
    authDomain: 'ijro-uz.firebaseapp.com',
    storageBucket: 'ijro-uz.firebasestorage.app',
    measurementId: 'G-7B3WNKFVBD',
  );

}