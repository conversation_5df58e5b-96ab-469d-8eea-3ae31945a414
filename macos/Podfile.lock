PODS:
  - audio_session (0.0.1):
    - FlutterMacOS
  - audioplayers_darwin (0.0.1):
    - FlutterMacOS
  - awesome_notifications (0.0.1):
    - FlutterMacOS
  - connectivity_plus_macos (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - Firebase/Analytics (10.9.0):
    - Firebase/Core
  - Firebase/Core (10.9.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.9.0)
  - Firebase/CoreOnly (10.9.0):
    - FirebaseCore (= 10.9.0)
  - Firebase/Crashlytics (10.9.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.9.0)
  - Firebase/Messaging (10.9.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.9.0)
  - firebase_analytics (10.4.1):
    - Firebase/Analytics (= 10.9.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (2.13.0):
    - Firebase/CoreOnly (~> 10.9.0)
    - FlutterMacOS
  - firebase_crashlytics (3.3.1):
    - Firebase/CoreOnly (~> 10.9.0)
    - Firebase/Crashlytics (~> 10.9.0)
    - firebase_core
    - FlutterMacOS
  - firebase_messaging (14.6.1):
    - Firebase/CoreOnly (~> 10.9.0)
    - Firebase/Messaging (~> 10.9.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseAnalytics (10.9.0):
    - FirebaseAnalytics/AdIdSupport (= 10.9.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.9.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.9.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.10.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.10.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.9.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.10.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.9.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseSessions (10.10.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.10)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - flutter_native_timezone (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - GoogleAppMeasurement (10.9.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.9.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.9.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.2.3):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.11.1):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.1):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.1):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.11.1):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.11.1):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.1)"
  - GoogleUtilities/Reachability (7.11.1):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.1):
    - GoogleUtilities/Logger
  - isar_flutter_libs (1.0.0):
    - FlutterMacOS
  - just_audio (0.0.1):
    - FlutterMacOS
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - platform_device_id (0.0.1):
    - FlutterMacOS
  - platform_device_id_macos (0.0.1):
    - FlutterMacOS
  - PromisesObjC (2.2.0)
  - PromisesSwift (2.2.0):
    - PromisesObjC (= 2.2.0)
  - ReachabilitySwift (5.0.0)
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - smart_auth (0.0.1):
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_compress (0.3.0):
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - audioplayers_darwin (from `Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos`)
  - awesome_notifications (from `Flutter/ephemeral/.symlinks/plugins/awesome_notifications/macos`)
  - connectivity_plus_macos (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus_macos/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - firebase_analytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_crashlytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - flutter_native_timezone (from `Flutter/ephemeral/.symlinks/plugins/flutter_native_timezone/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - isar_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/isar_flutter_libs/macos`)
  - just_audio (from `Flutter/ephemeral/.symlinks/plugins/just_audio/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - platform_device_id (from `Flutter/ephemeral/.symlinks/plugins/platform_device_id/macos`)
  - platform_device_id_macos (from `Flutter/ephemeral/.symlinks/plugins/platform_device_id_macos/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - smart_auth (from `Flutter/ephemeral/.symlinks/plugins/smart_auth/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_compress (from `Flutter/ephemeral/.symlinks/plugins/video_compress/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSessions
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift

EXTERNAL SOURCES:
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  audioplayers_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos
  awesome_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/awesome_notifications/macos
  connectivity_plus_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus_macos/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  firebase_analytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_crashlytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  flutter_native_timezone:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_native_timezone/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  isar_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/isar_flutter_libs/macos
  just_audio:
    :path: Flutter/ephemeral/.symlinks/plugins/just_audio/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  platform_device_id:
    :path: Flutter/ephemeral/.symlinks/plugins/platform_device_id/macos
  platform_device_id_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/platform_device_id_macos/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  smart_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/smart_auth/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_compress:
    :path: Flutter/ephemeral/.symlinks/plugins/video_compress/macos

SPEC CHECKSUMS:
  audio_session: dea1f41890dbf1718f04a56f1d6150fd50039b72
  audioplayers_darwin: dcad41de4fbd0099cb3749f7ab3b0cb8f70b810c
  awesome_notifications: 428f5c15a700b117418aed09e29c21c5806fcf69
  connectivity_plus_macos: f6e86fd000e971d361e54b5afcadc8c8fa773308
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  Firebase: bd152f0f3d278c4060c5c71359db08ebcfd5a3e2
  firebase_analytics: 28d1d097d37accf81c520c783f0e5827caea5f5e
  firebase_core: feb9ea6e23cfb6d4961cd2abeec1b79b47790b8d
  firebase_crashlytics: 590044331fe201d96be90ba8fd1f431bcb497a67
  firebase_messaging: e3fc5d74715732e83ff498d7f154287d665d5f8f
  FirebaseAnalytics: 5ea0db4893825e7b0149d575352cd838236313dc
  FirebaseCore: b68d3616526ec02e4d155166bbafb8eca64af557
  FirebaseCoreExtension: 8d93ebbf6838a874b4ed82a564c9d6705f8365dd
  FirebaseCoreInternal: 971029061d326000d65bfdc21f5502c75c8b0893
  FirebaseCrashlytics: b60329455285aff853e54139d8ddbfe1e5f2b9f9
  FirebaseInstallations: 52153982b057d3afcb4e1fbb3eb0b6d00611e681
  FirebaseMessaging: 6b7052cc3da7bc8e5f72bef871243e8f04a14eed
  FirebaseSessions: 5f9e62cd4096e24d2011cbd845b0efceffaee1ec
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  flutter_native_timezone: 3a4724189c47dea215bb3e168e555e18308d312c
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  geolocator_apple: 72a78ae3f3e4ec0db62117bd93e34523f5011d58
  GoogleAppMeasurement: 373bcbead1bb6a85be7a64d5d8f96284b762ea9c
  GoogleDataTransport: f0308f5905a745f94fb91fea9c6cbaf3831cb1bd
  GoogleUtilities: 9aa0ad5a7bc171f8bae016300bfcfa3fb8425749
  isar_flutter_libs: 43385c99864c168fadba7c9adeddc5d38838ca6a
  just_audio: 9b67ca7b97c61cfc9784ea23cd8cc55eb226d489
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  path_provider_foundation: eaf5b3e458fc0e5fbb9940fb09980e853fe058b8
  platform_device_id: 3e414428f45df149bbbfb623e2c0ca27c545b763
  platform_device_id_macos: f763bb55f088be804d61b96eb4710b8ab6598e94
  PromisesObjC: 09985d6d70fbe7878040aa746d78236e6946d2ef
  PromisesSwift: cf9eb58666a43bbe007302226e510b16c1e10959
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  share_plus: 76dd39142738f7a68dd57b05093b5e8193f220f7
  shared_preferences_foundation: e2dae3258e06f44cc55f49d42024fd8dd03c590c
  smart_auth: b38e3ab4bfe089eacb1e233aca1a2340f96c28e9
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  url_launcher_macos: 5335912b679c073563f29d89d33d10d459f95451
  video_compress: c896234f100791b5fef7f049afa38f6d2ef7b42f

PODFILE CHECKSUM: dac0ddf03d136db544afc27b87cc6c08492e67b9

COCOAPODS: 1.12.1
